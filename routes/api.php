<?php

/**
 * --------------------------------------------------------------------------
 * Application Routes
 * --------------------------------------------------------------------------
 *
 * Here is where you can register all of the routes for an application.
 * It is a breeze. Simply tell Lu<PERSON> the URIs it should respond to
 * and give it the Closure to call when that URI is requested.
 *
 * <AUTHOR>
 */
//Route::group(['prefix' => 'v2.0.0'], function () {
// Route::group(['prefix' => 'v2'], function () {

/**
 * Put All the route here, that can only access after authentication
 * <AUTHOR>
 *
 */
Route::group(['middleware' => 'transaction_auth:api'], function ($router) {
    Route::group(['prefix' => 'user'], function () {

        Route::group(['namespace' => 'Payment', 'prefix' => 'payment'], function () {
            Route::group(['namespace' => 'Withdraw'], function () {
                Route::post('withdraw-check', "WithdrawController@withdrawCheck");
                Route::post('withdraw', "WithdrawController@withdrawOrTransfer");
                Route::put('withdraw-revert', "WithdrawController@withdrawRevert");
                Route::get('withdraw-status', "WithdrawController@withdrawStatus");
            });
        });

        /**
         * Route Group for Payment -> Add Money Module.
         * <AUTHOR> Aroraa <<EMAIL>>
         */
        /* Route Group Payment -> Add Money Module START */
        Route::group(['prefix' => 'payment', 'namespace' => 'Payment'], function () {
            Route::group(['namespace' => 'AddMoney'], function () {
                Route::post('check', "AddMoneyController@check");
                Route::get('card-check', "AddMoneyController@checkCardType");
                Route::get('gateway', [
                    'as' => 'depositGateway',
                    'uses' => "AddMoneyController@gateway"
                ]);
                // Route::get('gateway', "AddMoneyController@gateway")->name('depositGateway');
                Route::post('deposit', "AddMoneyController@depositNew");
                Route::delete('credential', "AddMoneyController@removeSavedCredentials");
                Route::get('status', "AddMoneyController@getTransactionStatus");
                Route::get('deposit-amount-suggestion', [
                    'as' => 'depositAmountSuggestionV1',
                    'uses' => "AddMoneyController@getDepositAmountSuggestion"
                ]);
                Route::get('deposit-amount-suggestion-new', [
                    'as' => 'depositAmountSuggestionV2',
                    'uses' => "AddMoneyController@getDepositAmountSuggestionNew"
                ]);
                // Route::get('deposit-amount-suggestion', "AddMoneyController@getDepositAmountSuggestion")->name('depositAmountSuggestionV1');
                // Route::get('deposit-amount-suggestion-new', "AddMoneyController@getDepositAmountSuggestionNew")->name('depositAmountSuggestionV2');
                Route::put('payment-force-fail', "AddMoneyController@failPaymentAfterGivenTime");
            });
        });
        /* Route Group Payment -> Add Money Module END */

        /**
         * Route Group for Payment -> Upi Module.
         * <AUTHOR> Sharma <<EMAIL>>
         */
        /* Route Group Payment -> Upi Module START */
        Route::group(['prefix' => 'payment', 'namespace' => 'Payment'], function () {
            Route::group(['namespace' => 'Upi'], function () {
                Route::post('verify-upi', "UpiController@verifyUpi");
            });
        });
        /* Route Group Payment -> Upi Module END */

        /**
         * Route Group for Voucher -> Redeem Module.
         * <AUTHOR> Aroraa <<EMAIL>>
         */
        /* Route Group Voucher -> Redeem Module START */
        Route::group(['prefix' => 'voucher', 'namespace' => 'Voucher'], function () {
            Route::post('redeem', "VoucherController@redeem");
        });
        /* Route Group Voucher -> Redeem Module END */
    });
});
/**
 * Route Group for Payment -> Add Money -> Web Hooks Module.
 * <AUTHOR> Aroraa <<EMAIL>>
 */
/* Route Group Payment -> Add Money -> Web Hooks Module START */
Route::group(['prefix' => 'user'], function () {
    Route::group(['prefix' => 'payment', 'namespace' => 'Payment'], function () {
        Route::group(['namespace' => 'AddMoney'], function () {
            Route::group(['prefix' => 'webhook'], function () {
                Route::post('response', "WebhookHandlerController@webHookResponse");
            });
            Route::get('response', "WebhookHandlerController@returnResponse");
            Route::post('response', "WebhookHandlerController@returnResponse");
        });
        /**
         * test
         */
        Route::get('test', function () {
            $mySqlConnect = true;
            $redisConnect = true;
            try {
                \DB::connection('mysql')->getPdo();  // Checking main db connection
            } catch (\Exception $e) {
                \Log::error($e);
                $mySqlConnect = false;
                // return response()->json(['status' => 503, 'message' => 'Not able to connect m-db'], 503);
            }

            try {
                \RedisManager::ping();
            } catch (\Exception $e) {
                \Log::error($e);
                $redisConnect = false;
            }

            if (!$mySqlConnect || !$redisConnect) {
                $errorMesage = "";
                $errorMesage .= !$mySqlConnect ? "Not able to connect m-db, " : "";
                $errorMesage .= !$redisConnect ? "Not able to Redis." : "";
                return response()->json(['status' => 503, 'message' => "$errorMesage"], 503);
            }

            return response()->json(['status' => 200, 'message' => 'hello txn']);
        });
    });
});

Route::group(['prefix' => 'adjustment', 'namespace' => 'Adjustment'], function () {
    Route::group(['prefix' => 'webhook'], function () {
        Route::post('request', "BalanceController@adjustBalance");
    });
});