version: '3.8'
services:
  nginx:
    volumes:
      - "www:/usr/share/nginx/html/"
      - "~/nginx_logs:/var/log/nginx/"
    build:
      dockerfile: ""
      context: ../.
    ports:
    - "80:80"
    container_name: "PB2.0-API-WebServer"
    networks:
      - web-network
    tty: "true"
    stdin_open: true

  php:
    container_name: "php"
    image: "php:7.4-fpm"
    volumes:
      - "www:/usr/share/nginx/html/"
    networks:
      - web-network

  composer:
    image: "composer:latest"
    working_dir: "/usr/share/nginx/html/"
    volumes:
      - "www:/usr/share/nginx/html/"
    command: ["composer","install"]

volumes:
  www:

networks:
  web-network:
