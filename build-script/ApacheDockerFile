# this is based on alipne images for apache
# version of apache is 2.4
#FROM  httpd:2.4-alpine
FROM httpd:2.4-alpine3.14

# setting the work directory
WORKDIR /usr/local/apache2/htdocs


# installing the packages requires
# MAKING symbolic link for the apache module
#RUN echo -e "https://dl-cdn.alpinelinux.org/alpine/v3.15/main\nhttps://dl-cdn.alpinelinux.org/alpine/v3.15/community" > /etc/apk/repositories
RUN apk update && apk upgrade \
    && apk --no-cache --no-progress add php7 php-bcmath php-cli php-fileinfo php-exif php-simplexml php-common php7-pecl-redis redis php-gd php-intl php-ldap php-mbstring \
        php-mysqlnd php-pear php-soap php-xml php-xmlrpc php-zip php7-json php7-phar php7-curl php7-ctype php7-dom \
        php7-xmlwriter php7-tokenizer php7-apache2 php7-pdo php7-pdo_mysql php7-pdo_pgsql libgcc curl\
    && ln -s /usr/lib/apache2/mod_php7.so /usr/local/apache2/modules/mod_php7.so

RUN wget https://github.com/elastic/apm-agent-php/releases/download/v1.8.0/apm-agent-php_1.8.0_all.apk
RUN  apk add --allow-untrusted apm-agent-php_1.8.0_all.apk
RUN curl -LO https://github.com/DataDog/dd-trace-php/releases/latest/download/datadog-setup.php

 
RUN ln -s /opt/elastic/apm-agent-php/etc/elastic-apm.ini /etc/php7/conf.d/
# installing the  php composer
# todo
#   - will have to look into this as their may be potential blunder in the hash of the composer-setup.php changes
#   - will have to look into this , one alternative is to blundle the hash or compser file in a config file
RUN php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');" \
    && php -r "if (hash_file('sha384', 'composer-setup.php') === 'dac665fdc30fdd8ec78b38b9800061b4150413ff2e3b6f88543c636f7cd84f6db9189d43a81e5503cda447da73c7e5b6') { echo 'Installer verified'; } else { echo 'Installer corrupt'; unlink('composer-setup.php'); } echo PHP_EOL;" \
    && php composer-setup.php \
    && php -r "unlink('composer-setup.php');"

# Install Elastic APM  agent 
ARG ELASTIC_APM_SERVICE_NAME
ARG ELASTIC_APM_SERVER_URL
ENV ELASTIC_APM_SERVICE_NAME=$ELASTIC_APM_SERVICE_NAME
ENV ELASTIC_APM_SERVER_URL=$ELASTIC_APM_SERVER_URL
#ENV ELASTIC_APM_URL_GROUPS=/*
RUN echo ELASTIC_APM_SERVICE_NAME $ELASTIC_APM_SERVICE_NAME
RUN echo ELASTIC_APM_SERVER_URL $ELASTIC_APM_SERVER_URL
#RUN echo ELASTIC_APM_URL_GROUPS /*
RUN echo ELASTIC_APM_URL_GROUPS /vendor/v1/user/checkbalance/* 

##--------------DATADOG Integration------------------------------------
ARG DD_SERVICE
ARG DD_ENV
ARG DD_VERSION
ARG DD_PROFILING_ENABLED

ENV DD_SERVICE=$DD_SERVICE
ENV DD_ENV=$DD_ENV
ENV DD_VERSION=$DD_VERSION
ENV DD_PROFILING_ENABLED=$DD_PROFILING_ENABLED

RUN echo DD_SERVICE $DD_SERVICE
RUN echo DD_ENV $DD_ENV
RUN echo DD_VERSION $DD_VERSION

# todo enable this service , checks the container health
#  traditionally should ping the DB , memory cluster etc.
#  commenting this for now
# todo change the health check path as per the API
HEALTHCHECK --interval=120s --timeout=30s --start-period=10s --retries=2 \
  CMD wget -nv -t1 --spider "http://localhost:80/user/payment/test" || exit 1

# todo Creating Sym link from Application log to STDOUT
#   - this is needed to expose logs to cloudwatch using fargate logs driver
#   - Apache application logs need to be published here as well
#   - commenting this for now
#RUN ln -sf /dev/stdout storage/logs/test.log


# copy the files
# this will copy al the files which are not mentioned in  .dockerignore file
COPY . .

#todo  inject a test php file, remove this after testing

RUN echo "<?php phpinfo(); ?>" >> test.php
RUN chown -R daemon.daemon storage
RUN php datadog-setup.php --php-bin=all --enable-profiling
# installing all the php dependencies
RUN php composer.phar install

# httd.conf file
# replaces the file by our file
RUN cp webserver-config/httpd.conf /usr/local/apache2/conf/httpd.conf

EXPOSE 80
#RUN ln -sf /usr/local/apache2/htdocs/storage/logs/lumen.log /dev/stdout

# Start Apache
CMD ["httpd","-D","FOREGROUND"]
