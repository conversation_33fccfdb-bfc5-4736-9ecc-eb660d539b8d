# RummyBaazi API

[![Build Status](https://travis-ci.org/laravel/lumen-framework.svg)](https://travis-ci.org/laravel/lumen-framework)

These API are based on [Lumen](https://lumen.laravel.com/docs) Framework. 

##  Documentation

Please follow this [link](https://moonshinetechnology.atlassian.net/wiki/spaces/PB/pages/104104921/Setting+Up+API), this gives step by step instructions to set up the API
pleas use this to get a deeper understanding of the whole structure.

####  END Points

Links are to be divided on bases of  following 


## Contributing

Please get in touch with <PERSON><PERSON> and <PERSON> for accessing a feature branch, and they will bring you to speed so you can start helping the team out.

##Container Deployment

To run the container 
make sure docker is installed and confingures in the server 


#####Building Image

```bash
# build the image 
docker build -f ./docker/ApacheDockerFile -t php-api-transaction:v1.0.0 -t php-api-transaction:latest .
```

The Above builds a apache powered imagae, please change the tags to the actual version used `Vx.x.x`

##### Running the Image 

Use the following command to tun the image locally 

```bash 
docker run -d --rm -p 80:80 php-api-transaction:latest 

```

Make sure you are using the appropiratly tagged image to start the container

The above command will  remove the container as soon as it is killed or stopped to change this behavior 
ditch the `--rm` flag 

#####Cleaning up

Make sure you clean all the images after building and clear all the stopped container to free up space
else things will get messy really quick, one neat command to do so is 

`docker rmi  $(docker images -q)`

this will remove all the images , so be very carefull with this.

### Issues

Please feel free to open a ticket on Jira (enter jira board link in)

##Ownership 

All API are right full property of Moonshine technologies.

