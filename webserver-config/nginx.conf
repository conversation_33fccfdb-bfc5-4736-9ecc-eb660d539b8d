server {
    listen       80;
    server_name  ${SERVER_NAME};


    error_log  /var/log/nginx/api-error.log warn;
    access_log  /var/log/nginx/api-access.log  main;


    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }



    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;

        root           /usr/share/nginx/html/public;
        fastcgi_pass   php:9000;
        fastcgi_index  index.php;

        include        fastcgi_params;
        fastcgi_param  SCRIPT_FILENAME  /usr/share/nginx/html/public$fastcgi_script_name;
    }


    location / {
         try_files $uri $uri/ /index.php?$query_string;
    }

    # sins of past
    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    location ~ /\.ht {
        deny  all;
    }
}

