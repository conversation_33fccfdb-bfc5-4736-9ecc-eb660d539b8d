From 5c8c9bc8ac9a74f049f309b599cb1303872f1b47 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Wed, 20 Oct 2021 22:56:06 +0530
Subject: [PATCH] Upate Patch QA

---
 .env                      | 105 ++++++++++++++++++++++++++++++++++++++
 firebase_credentials.json |  12 +++++
 2 files changed, 117 insertions(+)
 create mode 100644 .env
 create mode 100644 firebase_credentials.json

diff --git a/.env b/.env
new file mode 100644
index 0000000..f50908b
--- /dev/null
+++ b/.env
@@ -0,0 +1,105 @@
+APP_NAME=RummyBaazi_Api
+APP_ENV=QA
+APP_KEY=PQK1TO2xL2c0INK2kBDyab2laa1oggdg
+APP_DEBUG=true
+APP_URL=https://nxtgenapi.managemytestbox.com/txn
+APP_TIMEZONE="Asia/Kolkata"
+CDN_URL=https://web.pokerbaazicdn.com
+
+LOG_CHANNEL=stack
+LOG_SLACK_WEBHOOK_URL=
+
+CACHE_DRIVER=file
+QUEUE_CONNECTION=sync
+
+# Main DB connection limited permission
+DB_HOST=************
+DB_PORT=3306
+DB_DATABASE=rummybaazi_opt_preprod
+DB_USERNAME=cb_trxn_natuser
+DB_PASSWORD="fU6Vwu$43K"
+
+AWS_ACCESS_KEY_ID=MAIL_DRIVER=smtp
+MAIL_HOST=email-smtp.us-east-1.amazonaws.com
+MAIL_PORT=587
+MAIL_USERNAME=AKIAZHWQTAY2RNK7W2MY
+MAIL_PASSWORD=BPNCAJJu6uHXjXvI+QZ0dH7JiPs1AEc49FJFL03Liv7y
+MAIL_ENCRYPTION=tls
+MAIL_FROM_SUPPORT="<EMAIL>"
+
+# MSG91 CREDIENTIALS
+MSG91_AUTH_KEY=96543A6u26uCVcNm5d2c4998
+MSG91_SENDER_ID=PBAAZI
+MSG91_SEND_OTP_CURL_URL=https://control.msg91.com/api/sendotp.php
+MSG91_VERIFY_OTP_CURL_URL=https://control.msg91.com/api/verifyRequestOTP.php
+MSG91_TEMPLATE_ID_V5=5e3130b7d6fc050f2c56f0c2
+MSG91_SEND_OTP_CURL_URL_V5=https://api.msg91.com/api/v5/otp
+
+AWS_SECRET_ACCESS_KEY=
+AWS_DEFAULT_REGION=us-east-1
+
+# FIREBASE SDK SERVICE ACCOUNT CREDENTIALS
+FIREBASE_CREDENTIALS=./firebase_credentials.json
+
+#PAYMENT GATEWAYS AND RETURN URL
+WEBSITE_BASE_URL=https://www.cardbaazi.com
+PAYMENT_RESPONSE_ROUTE=/about-us
+ACCOUNTSECTION_BASE_URL=https://dashboard.managemytestbox.com
+ACCOUNTSECTION_RESPONSE_ROUTE=/home/<USER>/payment
+
+PAYU_SEAMLESS_CODE=payu_seamless
+JUSPAY_CODE=juspay
+PAYMENTGATEWAY_PRODUCT_DESC=CardBaazi
+
+#PAYU SEAMLESS CREDENTIALS
+PAYU_KEY=UpciTImW
+PAYU_SALT=HMzViTYs
+PAYU_URL=https://secure.payu.in/_payment
+PAYU_MONEY_WEBHOOK_IP=************|************|0.0.0.0
+
+#JUSPAY CREDENTIALS
+# JUSPAY_GATEWAY_NAME=juspay // not in as per search
+# JSUPAY_SIGNED_RESPONSE_KEY=1A52637A8984F37B0CF341B0AE2CE6  // not in as per search
+JUSPAY_API_KEY=6086C2B898F4BC89BA50876F5393B6
+JUSPAY_CODE=juspay
+JUSPAY_MERCHANT_ID=rummybaazi
+JUSPAY_URL=https://sandbox.juspay.in/
+JUSPAY_VERSION=2018-10-25
+JUSPAY_DEFAULT_CURRENCY=INR
+JUSPAY_WEBHOOK_IP=0.0.0.0
+
+# CASHFREE INSTANT PAYOUT CREDENTIALS 
+CASHFREE_PAYOUT_CLIENT_ID=***************************
+CASHFREE_PAYOUT_CLIENT_SEC=5859419452728bb9003a968f4cf1bd3e915e467b
+CASHFREE_PAYOUT_URL=https://payout-gamma.cashfree.com/payout/v1
+
+#WITHDRAW RELATED FLAGS
+CONSIDER_GREEN_FLAG_USER=TRUE
+CONSIDER_YELLOW_FLAG_USER=TRUE
+CONVERT_INTO_RED_FLAG=FALSE
+# INSTANT_WITHDRAW_APPICABLE_FOR_COMMISSION=FALSE // not in as per search
+# CASHFREE_INSTANT_WITHDRAW_LIMIT=100000 // not in as per search
+
+#DEPOSIT CONFIGURATION DEFAULTS
+MIN_DEPOSIT_AMOUNT=1
+
+#RAF BONUS DEFAULTS
+# FIRST_TIME_DEPOSIT_KEY=first_time_deposit // not in as per search
+# RECURRENCE_DEPOSIT_KEY=recurrence_deposit // not in as per search
+
+# Default admin id
+ADMIN_ID=10001
+
+INSTANT_WITHDRAWAL_GREEN_MAX_LIMIT=500
+INSTANT_WITHDRAWAL_YELLOW_MAX_LIMIT=500
+INSTANT_WITHDRAWAL_GREEN_MAX_PER_DAY_CAPPING=500000
+INSTANT_WITHDRAWAL_YELLOW_MAX_PER_DAY_CAPPING=30000
+
+PRO_IP_URL='https://pro.ip-api.com/json/{{ip}}?key=6BleQjZkaztXkUq'
+IPSTACK_URL='https://api.ipstack.com/{{ip}}?access_key=********************************'
+ACTIVE_IP_VENDOR='proip'
+VALIDATE_BANNED_STATE='1'
+BANNED_STATES='bihar'
+BANNED_STATE_BYPASS_JSON='https://cardbaazi-upload-qa.s3.ap-south-1.amazonaws.com/oauth_bannedstate_check/user.json'
+BANNED_STATE_SETTING_JSON='https://rb-upload-qa.s3.ap-south-1.amazonaws.com/bypass/banned_setting.json'
+BANNED_STATE_LAMBDA_URL='https://wewillhuntyoudown.managemytestbox.com/check/check-ip'
\ No newline at end of file
diff --git a/firebase_credentials.json b/firebase_credentials.json
new file mode 100644
index 0000000..4b5fd97
--- /dev/null
+++ b/firebase_credentials.json
@@ -0,0 +1,12 @@
+{
+    "type": "service_account",
+    "project_id": "rummybaazi-new-qa",
+    "private_key_id": "323ae5e71d81efb67c1eb3d13c93a036b8f3f3d1",
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
+    "client_email": "<EMAIL>",
+    "client_id": "105365598105527567876",
+    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
+    "token_uri": "https://oauth2.googleapis.com/token",
+    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
+    "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-eknng%40rummybaazi-new-qa.iam.gserviceaccount.com"
+}
\ No newline at end of file
-- 
2.30.0.windows.2

