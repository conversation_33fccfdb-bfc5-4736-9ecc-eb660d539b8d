{"name": "cardbaaziapi/txn", "version": "v2.0.2", "description": "The Laravel Lumen Framework", "keywords": ["framework", "laravel", "lumen"], "license": "MIT", "type": "project", "require": {"php": "^7.2.5", "bugsnag/bugsnag-laravel": "^2.0", "facebook/php-business-sdk": "^14.0", "guzzlehttp/guzzle": "^6.5", "illuminate/mail": "7.0.*", "illuminate/redis": "^7.30", "kickbox/kickbox": "^2.2", "kreait/laravel-firebase": "^2.4", "laravel/lumen-framework": "^7.0", "league/flysystem-aws-s3-v3": "^2.1", "predis/predis": "^1.1", "tymon/jwt-auth": "^1.0"}, "require-dev": {"fzaninotto/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "phpunit/phpunit": "^8.5"}, "autoload": {"classmap": ["database/seeds", "database/factories"], "psr-4": {"App\\": "app/"}}, "autoload-dev": {"classmap": ["tests/"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}}