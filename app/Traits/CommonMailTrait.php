<?php

namespace App\Traits;

use App\Mail\CommonMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

/**
 *
 */
trait CommonMailTrait
{
    public function sendMail($params)
    {
        try {
            $domainName = substr(strrchr($params['email'], "@"), 1);
            $validDomainList = config('rummy_config.mail.validDomainList');
            if (
                (strtoupper(config('app.env')) == 'PRODUCTION' || strtoupper(config('app.env')) == 'PROD')
                || in_array($domainName, $validDomainList)
            ) {
                if (!empty($params['email'])) {
                    Mail::to($params['email'])->send(new CommonMail($params));
                }
            } else {
                return true;
            }
        } catch (\Exception $exception) {
            Log::error($exception);
        }
    }
}
