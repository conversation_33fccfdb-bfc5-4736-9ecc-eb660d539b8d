<?php

namespace App\Traits;

use App\Facades\RummyBaazi;
use App\Jobs\CommonMailJob;
use App\Models\SiteJincNewsLetter;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Log;
use App\Models\OtpTracking;
use Illuminate\Support\Carbon;

trait AuthenticationTraits
{
    public function authoriseTwoStepVerification($user, $params = [])
    {
        $isSendToMobile = $params['isSendToMobile'] ?? true;
        $isSendToMobileCustomOTP = $params['isSendToMobileCustomOTP'] ?? false;
        $isSendToEmail = $params['isSendToEmail'] ?? true;
        $otpTrackingMailMsg = $params['otpTrackingMailMsg'] ?? "SEND_OTP_EMAIL";
        $otpTrackingMobileMsg = $params['otpTrackingMobileMsg'] ?? "SEND_OTP_MOBILE";


        if (!$user instanceof User && is_numeric($user)) {
            $user = User::select('USER_ID', 'USERNAME', 'EMAIL_ID', 'CONTACT')->find($user);
        }

        if ($user instanceof User) {
            date_default_timezone_set('Asia/Kolkata');
            $currentDate = date("YmdHis");

            $otpResponse = null;
            $isOtpSentToEmail = null;

            $userId = $user->USER_ID;
            $otp = $params['otp'] ?? substr(str_shuffle("0123456789"), 0, 6);
            $referenceNumber = "OTP" . $userId . $currentDate . mt_rand(100, 999);

            if ($isSendToMobile) $this->sendOTPOnMobile($user->CONTACT, $userId, $referenceNumber, $otpTrackingMobileMsg);
            if ($isSendToMobileCustomOTP) $otpResponse = $this->sendToMobileCustomOTP($user->CONTACT, $otp, $userId, $referenceNumber, $otpTrackingMobileMsg);
            if ($isSendToEmail) $isOtpSentToEmail = $this->sendOTPOnEmail($user->USER_ID, $user->EMAIL_ID, $otp, $user->USERNAME, $referenceNumber, $otpTrackingMailMsg);

            // Will modify this function when the mail functionality will open
            if ($isSendToEmail) {
                $update = User::where('USER_ID', $user->USER_ID)
                    ->update([
                        'ACTIVATION_CODE' => $referenceNumber,
                    ]);
            } else {
                $update = true;
            }

            if ($otpResponse != null && $otpResponse->response->type == 'success') {
                $isOtpSent = true;
            } else {
                $isOtpSent = false;
            }

            if ($isOtpSentToEmail == 'success') {
                $isOtpSent = true;
            }

            if ($isOtpSent) {
                return [
                    'code' => 1,
                    'desc' => $otp,
                    'maskedPhoneNumber' => substr($user->CONTACT, 0, 2) . "XXXXXX" . substr($user->CONTACT, 7, 9),
                    'maskedEmailAddress' => substr($user->EMAIL_ID, 0, 4) . "<EMAIL>"
                ];
            } else {
                return [
                    'code' => 3,
                    'desc' => "something went wrong, query failed"
                ];
            }
        } else {
            return [
                'code' => 2,
                'desc' => "Not Valid User"
            ];
        }
    }

    public function sendOTPOnMobile($phoneNumber, $userId = null, $referenceNumber = "", $otpTrackingMobileMsg = null)
    {
        $msg91AuthKey = config('rummy_config.msg91.authKey');
        $sendOtpCurlUrl = config('rummy_config.msg91.sendOtpCurlUrl');
        $senderId = config('rummy_config.msg91.senderId');
        $otpTrackingMobileMsg = $otpTrackingMobileMsg ?? "SEND_OTP_MOBILE";

        $form_params['authkey'] = $msg91AuthKey;
        $form_params['mobile'] = $phoneNumber;
        $form_params['sender'] = $senderId;
        $form_params['otp_length'] = "4";
        $form_params['otp_expiry'] = "5";

        $data = $this->ApiCallCurl([
            "url" => $sendOtpCurlUrl,
            "form_params" => $form_params,
            "headers" => [
                "Content-Type" => "application/x-www-form-urlencoded utf-8"
            ],
            "extra_curl_options_array" => [
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1
            ]
        ]);

        if (!empty($response = $data->response)) {
            $errorStatus = $response->type;
            $errorMessage = $response->message ?? "";
            RummyBaazi::updateOTPTracking($phoneNumber, $otp = "", $userId, $otpTrackingMobileMsg, $errorStatus, $errorMessage, $referenceNumber, 1);
        } else {
            RummyBaazi::updateOTPTracking($phoneNumber, $otp = "", $userId, $otpTrackingMobileMsg, "NO_RESPONSE", "NO_RESPONSE", $referenceNumber, 3);
        }

        return $data;
    }

    public function sendToMobileCustomOTP($phoneNumber, $otp, $userId = null, $referenceNumber = "", $otpTrackingMobileMsg = null)
    {
        $msg91AuthKey = config('rummy_config.msg91.authKey');
        $msg91TemplateIdv5 = config('rummy_config.msg91.msg91TemplateIdv5');
        $sendOtpCurlUrl = config('rummy_config.msg91.sendOTPCurlURLv5');
        $senderId = config('rummy_config.msg91.senderId');

        $otpTrackingMobileMsg = $otpTrackingMobileMsg ?? "SEND_OTP_MOBILE";

        $form_params['authkey'] = $msg91AuthKey;
        $form_params['template_id'] = $msg91TemplateIdv5;
        $form_params['mobile'] = "91" . $phoneNumber;
        $form_params['otp'] = $otp;
        $form_params['sender'] = $senderId;
        $form_params['extra_param'] = ["OTP" => $otp];


        $data = $this->ApiCallCurl([
            "url" => $sendOtpCurlUrl,
            "form_params" => $form_params,
            "headers" => [
                "Content-Type" => "application/x-www-form-urlencoded utf-8"
            ],
            "extra_curl_options_array" => [
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1
            ]
        ]);

        if (!empty($response = $data->response)) {
            $errorStatus = $response->type;
            $errorMessage = $response->message ?? "";
            RummyBaazi::updateOTPTracking($phoneNumber, $otp, $userId, $otpTrackingMobileMsg, $errorStatus, $errorMessage, $referenceNumber, 1);
        } else {
            RummyBaazi::updateOTPTracking($phoneNumber, $otp, $userId, $otpTrackingMobileMsg, "NO_RESPONSE", "NO_RESPONSE", $referenceNumber, 3);
        }

        return $data;
    }

    public function verifyMobileOtp($phoneNumber, $otp, $userId=null,$verifiedFrom = '')
    {
        //---------------------------------------
        //  BLOCKING USER FROM BRUTE FORCE
        //---------------------------------------

        $lastSentOtp = OtpTracking::where([
                                    'USER_ID' => $userId,
                                    'STATUS' => '1',
                                ])
                                ->whereJsonContains('other_details->send_for', $verifiedFrom)
                                ->latest('otp_tracking_id')
                                ->limit(1)
                                ->first();
        
        if(empty($lastSentOtp)){
            $lastSentOtp = OtpTracking::where([
                                    'USER_ID' => $userId,
                                    'STATUS' => '1',
                                ])
                                ->whereJsonContains('other_details->send_for', 'VERIFY_DEFAULT_OTP')
                                ->latest('otp_tracking_id')
                                ->limit(1)
                                ->first();
            
            if(empty($lastSentOtp)){
                return $data = (Object) [
                    'response' =>(Object) [
                        'message' => 'Invalid OTP',
                        'type' => 'error',
                        'code' => 422006
                    ]
                ];
            }
        }


        if(!Carbon::parse($lastSentOtp->date_of_verify)->gte(Carbon::now()->subMinutes(5))){
            return $data = (Object) [
                'response' =>(Object) [
                    'message' => 'Your otp has been expired',
                    'type' => 'error',
                    'code' => 422124
                ]
            ];
        }          
        
        $verifyFailedCount = OtpTracking::where([
                                    'USER_ID' => $userId,
                                    'STATUS' => '4',
                                    'INTERNAL_REFERENCE_NO' => $lastSentOtp->INTERNAL_REFERENCE_NO 
                                    ])
                                    ->latest('otp_tracking_id')
                                    ->count(); 


        if($verifyFailedCount > config('rummy_config.maxOtpVerifyLimit')){
            return $data = (Object) [
                'response' =>(Object) [
                    'message' => 'Maximum limit of verification is exceeded',
                    'type' => 'error',
                    'code' => 422123
                ]
            ];
        }



        //---------------------------------------
        //  BLOCKING USER FROM BRUTE FORCE END
        //---------------------------------------                                                   

        $msg91AuthKey = config('rummy_config.msg91.authKey');
        $verifyOtpCurlUrl = config('rummy_config.msg91.verifyOtpCurlUrl');
        $senderId = config('rummy_config.msg91.senderId');

        $form_params['authkey'] = $msg91AuthKey;
        $form_params['mobile'] = '91'.$phoneNumber;
        $form_params['sender'] = $senderId;
        $form_params['otp'] = $otp;

        $data = $this->ApiCallCurl([
            "url" => $verifyOtpCurlUrl,
            "form_params" => $form_params,
            "headers" => [
                "Content-Type" => "application/x-www-form-urlencoded utf-8"
            ],
            "extra_curl_options_array" => [
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1
            ]
        ]);

        
        //---------------------------------------
        //  LOGGING FAILED VERIFIED OTP
        //---------------------------------------

        if($userId != null){
            try{
                
                if($data->response->type != 'success'){
                    OtpTracking::create([
                        'INTERNAL_REFERENCE_NO' => $lastSentOtp->INTERNAL_REFERENCE_NO,
                        'MOBILE_NO' => $phoneNumber,
                        'CRAETED_DATE' => date('Y-m-d H:i:s'),
                        'OTP_NO' => $otp,
                        'STATUS' => 4,
                        'user_id' => $userId,
                        'otp_request_type' => $verifiedFrom,
                        'otp_response_type' => 'error',
                        'otp_response_message' => 'incorrect_otp'
                    ]);
                }  
                else{

                    $currentSentOtp = OtpTracking::where([
                                    'USER_ID' => $userId,
                                    'STATUS' => '1',
                                ])
                                ->whereJsonContains('other_details->send_for', $verifiedFrom)
                                ->latest('otp_tracking_id')
                                ->limit(1)
                                ->first();          
                    
                    if(!empty($currentSentOtp) && $currentSentOtp->OTP_NO == $otp){
                        $isUpdated = OtpTracking::where([
                            'OTP_NO' => $otp,
                            'USER_ID' => $userId,
                            'STATUS' => 1
                        ])->update([
                            'STATUS' => 2,
                            'otp_response_message' => $data->response->message
                        ]);
                       
                        if($isUpdated < 1){
                            return $data = (Object) [
                                'response' =>(Object) [
                                    'message' => 'Invalid OTP',
                                    'type' => 'error',
                                    'code' => 422006
                                ]
                            ];
                        }
                    }else{
                        return $data = (Object) [
                            'response' =>(Object) [
                                'message' => 'Invalid OTP',
                                'type' => 'error',
                                'code' => 422006
                            ]
                        ];
                    }                       
                }
            }
            catch(\Exception $e){
                Log::error($e);
                return $data = (Object) [
                    'response' =>(Object) [
                        'message' => 'Invalid OTP',
                        'type' => 'error',
                        'code' => 422006
                    ]
                ];
            }
        }
        //---------------------------------------
        //  LOGGING FAILED VERIFIED OTP END
        //---------------------------------------

        return $data;
    }

    public function sendOTPOnEmail($userId, $emailId, $otp, $username, $referenceNumber = "", $otpTrackingMsg = null)
    {
        //Email Template ID for Sending Mail OTP
        $emailTemplateID = 27;

        $live_site = config('rummy_config.cdn_url') . '/';
        $otpTrackingMsg = $otpTrackingMsg ?? "SEND_OTP_EMAIL";


        $result = SiteJincNewsLetter::select('name', 'description')->find($emailTemplateID);

        $headerLogoURL = $live_site . "images/mailer/rummybaazi-logo-blue.png";
        $headerImageURL = $live_site . "images/mailer/mailer-01.jpg";
        $footerImageURL = $live_site . "images/mailer/footer.jpg";
        $footerFacebookURL = $live_site . "images/mailer/social-media-facebook.png";
        $footerTwitterURL = $live_site . "images/mailer/social-media-twitter.png";
        $footerLinkedinURL = $live_site . "images/mailer/social-media-linkedin.png";
        $footerYoutubeURL = $live_site . "images/mailer/social-media-youtube.png";
        $downloadAndroidURL = $live_site . "images/mailer/android.png";
        $downloadiOSURL = $live_site . "images/mailer/apple.png";

        $mailSubject = $result->name;
        $mailContent = $result->description;

        $mailContent = str_replace('_HEADER_LOGO_URL_', $headerLogoURL, $mailContent);
        $mailContent = str_replace('_HEADER_IMAGE_URL_', $headerImageURL, $mailContent);
        $mailContent = str_replace('_FOOTER_IMAGE_URL_', $footerImageURL, $mailContent);
        $mailContent = str_replace('_FOOTER_FACEBOOK_URL_', $footerFacebookURL, $mailContent);
        $mailContent = str_replace('_FOOTER_TWITTER_URL_', $footerTwitterURL, $mailContent);
        $mailContent = str_replace('_FOOTER_LINKEDIN_URL_', $footerLinkedinURL, $mailContent);
        $mailContent = str_replace('_FOOTER_YOUTUBE_URL_', $footerYoutubeURL, $mailContent);
        $mailContent = str_replace('_DOWNLOAD_ANDROID_URL_', $downloadAndroidURL, $mailContent);
        $mailContent = str_replace('_DOWNLOAD_IOS_URL_', $downloadiOSURL, $mailContent);
        $mailContent = str_replace("_USERNAME_", $username, $mailContent);
        $mailContent = str_replace("_CODE_", $otp, $mailContent);
        $mailContent = str_replace("_EMAIL_", $emailId, $mailContent);

        $currentDate = date('Y-m-d H:i:s');
        $requestNo = date_format(date_create($currentDate), "jS M 'y g:iA");

        $data['mailContent'] = $mailContent;
        $data['requestNo'] = $requestNo;
        $data['emailId'] = $emailId;
        $data['subject'] = "OTP for verifying your account | Request Dated: " . $requestNo;


        RummyBaazi::updateOTPTracking($emailId, $otp, $userId, $otpTrackingMsg, "success", "", $referenceNumber, 1);
        Queue::push(new CommonMailJob($data));
        return 'success';
    }

    /**
     * Get the token array structure.
     *
     * @param string $token
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function respondWithToken($token)
    {
        return [
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => Auth::factory()->getTTL() * 60
        ];
    }
}
