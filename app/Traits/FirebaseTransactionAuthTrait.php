<?php

namespace App\Traits;

use <PERSON><PERSON>it\Firebase\Auth as FirebaseAuth;
use Firebase\Auth\Token\Exception\ExpiredToken;
use Firebase\Auth\Token\Exception\InvalidSignature;
use Firebase\Auth\Token\Exception\InvalidToken;
use Firebase\Auth\Token\Exception\UnknownKey;
use InvalidArgumentException;
use App\Models\User;
use App\Models\UserSetting;
use App\Facades\RummyBaazi;
use Carbon\Carbon;

trait FirebaseTransactionAuthTrait
{
    /**
     * Verify Firebase token and handle authentication
     *
     * @param string $token
     * @param FirebaseAuth $firebaseAuth
     * @return array|null Returns error response array if failed, null if successful
     */
    public function verifyFirebaseToken($token, FirebaseAuth $firebaseAuth)
    {
        if (!$token) {
            return RummyBaazi::errorResponse(1, [['code' => 401005, 'message' => "Token not provided."]]);
        }

        try {
            $verifiedIdToken = $firebaseAuth->verifyIdToken($token, true);
            return null; // Success
        } catch (ExpiredToken $e) {
            return RummyBaazi::errorResponse(1, [['code' => 401002, 'message' => "Token expired."]]);
        } catch (InvalidToken $e) {
            return RummyBaazi::errorResponse(1, [['code' => 401003, 'message' => "Invalid Token."]]);
        } catch (InvalidSignature $e) {
            return RummyBaazi::errorResponse(1, [['code' => 401000, 'message' => "Unauthorized to access."]]);
        } catch (InvalidArgumentException $e) {
            return RummyBaazi::errorResponse(1, [['code' => 401006, 'message' => "An error while decoding token."]]);
        } catch (\Exception $e) {
            return RummyBaazi::errorResponse(1, [['code' => 401006, 'message' => "An error while decoding token."]]);
        }
    }

    /**
     * Check if the current route requires transaction validation
     *
     * @param string|null $routeName
     * @return bool
     */
    public function isTransactionRoute($routeName)
    {
        $transactionRoutes = [
            'depositAmountSuggestionV1',
            'depositAmountSuggestionV2',
            'depositV3',
            'withdrawV2'
        ];

        return in_array($routeName, $transactionRoutes);
    }

    /**
     * Check if the current route is a deposit route
     *
     * @param string|null $routeName
     * @return bool
     */
    public function isDepositRoute($routeName)
    {
        $depositRoutes = [
            'depositAmountSuggestionV1',
            'depositAmountSuggestionV2',
            'depositV3'
        ];

        return in_array($routeName, $depositRoutes);
    }

    /**
     * Validate transaction based on banned state and KYC requirements
     *
     * @param \Illuminate\Http\Request $request
     * @return array|null Returns error response array if validation fails, null if successful
     */
    public function validateTransactionAuth($request)
    {
        // Skip validation if user bypasses banned state
        if ($this->isUserBypassBannedState()) {
            return null;
        }

        $route = $request->route();
        $routeName = $route ? $route[1]['as'] ?? null : null;

        // Only validate transaction routes
        if (!$this->isTransactionRoute($routeName)) {
            return null;
        }

        $checkStateBan = $this->isIpFromBannedState();

        if ($checkStateBan['isBanned']) {
            return $this->handleBannedStateValidation($request, $checkStateBan, $routeName);
        } else {
            return $this->handleNonBannedStateValidation($request, $checkStateBan, $routeName);
        }
    }

    /**
     * Handle validation for banned states
     *
     * @param \Illuminate\Http\Request $request
     * @param array $checkStateBan
     * @param string|null $routeName
     * @return array|null
     */
    protected function handleBannedStateValidation($request, $checkStateBan, $routeName)
    {
        $state = isset($checkStateBan['region']) ? $this->getStateDetails($checkStateBan['region']) : null;

        if (!empty($state) && $state->PARTIAL_BANNED_STATUS == 1) {
            return $this->handlePartialBannedState($request, $checkStateBan, $state, $routeName);
        } else {
            return RummyBaazi::errorResponse(1, [['code' => 422104, 'message' => $checkStateBan['region']]]);
        }
    }

    /**
     * Handle validation for non-banned states
     *
     * @param \Illuminate\Http\Request $request
     * @param array $checkStateBan
     * @param string|null $routeName
     * @return array|null
     */
    protected function handleNonBannedStateValidation($request, $checkStateBan)
    {
        $kycStatus = $this->checkKycForPartialBannedState($request, $checkStateBan);

        if ($kycStatus === "blackhours") {
            return RummyBaazi::errorResponse(1, [['code' => 422110, 'message' => 'As per government regulations, you are not allowed to make deposits.']]);
        } elseif ($kycStatus === false) {
            return RummyBaazi::errorResponse(1, [['code' => 422104, 'message' => 'As per government regulations, you need to complete your KYC to make deposits.']]);
        }

        return null;
    }

    /**
     * Handle partial banned state validation with time restrictions
     *
     * @param \Illuminate\Http\Request $request
     * @param array $checkStateBan
     * @param object $state
     * @param string|null $routeName
     * @return array|null
     */
    protected function handlePartialBannedState($request, $checkStateBan, $state)
    {
        $restrictedHours = json_decode($state->OTHER_DETAILS);

        if (!$restrictedHours || !isset($restrictedHours->start_time, $restrictedHours->end_time)) {
            return RummyBaazi::errorResponse(1, [['code' => 422104, 'message' => $checkStateBan['region']]]);
        }

        $start = explode(":", $restrictedHours->start_time);
        $end = explode(":", $restrictedHours->end_time);

        $restrictedHourStart = Carbon::today()->setTime($start[0], $start[1], $start[2] ?? 0);
        $restrictedHourEnd = Carbon::today()->setTime($end[0], $end[1], $end[2] ?? 0);
        $currentHour = Carbon::now();

        if ($currentHour->gte($restrictedHourStart) && $currentHour->lte($restrictedHourEnd)) {
            return RummyBaazi::errorResponse(1, [['code' => 422104, 'message' => 'As per government regulations, you are not allowed to make deposits.']]);
        } else {
            $kycStatus = $this->checkKycForPartialBannedState($request, $checkStateBan);

            if ($kycStatus === "blackhours") {
                return RummyBaazi::errorResponse(1, [['code' => 422110, 'message' => 'As per government regulations, you are not allowed to make deposits.']]);
            } elseif ($kycStatus === false) {
                return RummyBaazi::errorResponse(1, [['code' => 422104, 'message' => 'As per government regulations, you need to complete your KYC to make deposits.']]);
            }
        }

        return null;
    }

    /**
     * Check KYC status for partial banned state
     *
     * @param \Illuminate\Http\Request $request
     * @param array $checkStateBan
     * @return bool|string Returns true if KYC valid, false if invalid, "blackhours" if in restricted time
     */
    public function checkKycForPartialBannedState($request, $checkStateBan)
    {
        $kycStatus = true;
        $route = $request->route();
        $routeName = $route ? $route[1]['as'] ?? null : null;

        if (!$this->isDepositRoute($routeName)) {
            return $kycStatus;
        }

        $state = isset($checkStateBan['region']) ? $this->getStateDetails($checkStateBan['region']) : null;

        if (!empty($state) && $state->PARTIAL_BANNED_STATUS == 1) {
            $restrictedHours = json_decode($state->OTHER_DETAILS);

            if ($restrictedHours && isset($restrictedHours->start_time, $restrictedHours->end_time)) {
                $start = explode(":", $restrictedHours->start_time);
                $end = explode(":", $restrictedHours->end_time);

                $restrictedHourStart = Carbon::today()->setTime($start[0], $start[1], $start[2] ?? 0);
                $restrictedHourEnd = Carbon::today()->setTime($end[0], $end[1], $end[2] ?? 0);
                $currentHour = Carbon::now();

                if ($currentHour->gte($restrictedHourStart) && $currentHour->lte($restrictedHourEnd)) {
                    return "blackhours";
                }
                return true;
            }
        }

        return $kycStatus;
    }

    /**
     * Validate user KYC status
     *
     * @return bool
     */
}
