<?php

namespace App\Http\Middleware;

use <PERSON>reait\Firebase\Auth as FirebaseAuth;
use Firebase\Auth\Token\Exception\ExpiredToken;
use Firebase\Auth\Token\Exception\InvalidSignature;
use Firebase\Auth\Token\Exception\InvalidToken;
use Firebase\Auth\Token\Exception\UnknownKey;
use InvalidArgumentException;
use App\Models\User;
use App\Models\UserSetting;
use Illuminate\Support\Facades\Auth;
use App\Facades\RummyBaazi;
use Closure;
use Carbon\Carbon;
use App\Traits\CommonTraits;

class FirebaseTransactionAuthMiddleware
{
    protected $auth;
    use CommonTraits;
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function __construct(FirebaseAuth $auth)
    {
        $this->auth = $auth;
    }

    public function handle($request, Closure $next)
    {
        $token = $request->bearerToken();

        if (!$this->isUserBypassBannedState()) {
            $route = $request->route();
            $routeName = $route ? $route[1]['as'] ?? null : null;
            dd($routeName);
            if ($routeName == 'depositAmountSuggestionV1' || $routeName == 'depositAmountSuggestionV2' || $routeName == 'depositV3' || $routeName == 'withdrawV2') {
                $checkStateBan = $this->isIpFromBannedState();
                $state = isset($checkStateBan['region']) ? $this->getStateDetails($checkStateBan['region']) : null;

                if ($checkStateBan['isBanned'] || !empty($state) && $state->PARTIAL_BANNED_STATUS == 1) {
                    if (!empty($state) && $state->PARTIAL_BANNED_STATUS == 1) {
                        // Handle partial banned state with time restrictions
                        $restrictedHours = json_decode($state->OTHER_DETAILS);
                        $start = explode(":", $restrictedHours->start_time);
                        $end = explode(":", $restrictedHours->end_time);

                        $restrictedHourStart = Carbon::today()->setTime($start[0], $start[1], $start[2]);
                        $restrictedHourEnd = Carbon::today()->setTime($end[0], $end[1], $end[2]);
                        $currentHour = Carbon::now();

                        if ($currentHour->gte($restrictedHourStart) && $currentHour->lte($restrictedHourEnd)) {
                            return RummyBaazi::errorResponse(1, [['code' => 422104, 'message' => 'As per government regulations, you are not allowed to make deposits.']]);
                        } else {
                            $kycStatus = $this->checkKycForPartialBannedState($request, $checkStateBan);
                            if ($kycStatus === "blackhours") {
                                return RummyBaazi::errorResponse(1, [['code' => 422110, 'message' => 'As per government regulations, you are not allowed to make deposits.']]);
                            } elseif ($kycStatus === false) {
                                return RummyBaazi::errorResponse(1, [['code' => 422104, 'message' => 'As per government regulations, you need to complete your KYC to make deposits.']]);
                            }
                        }
                    } else {
                        return RummyBaazi::errorResponse(1, [['code' => 422103, 'message' => 'As per government regulations, you are not allowed to make deposits.']]);
                    }
                } else {
                    // Not banned, but check KYC for partial banned states
                    $kycStatus = $this->checkKycForPartialBannedState($request, $checkStateBan);
                    if ($kycStatus === "blackhours") {
                        return RummyBaazi::errorResponse(1, [['code' => 422110, 'message' => 'As per government regulations, you are not allowed to make deposits.']]);
                    } elseif ($kycStatus === false) {
                        return RummyBaazi::errorResponse(1, [['code' => 422104, 'message' => 'As per government regulations, you need to complete your KYC to make deposits.']]);
                    }
                }
            }
        }

        // Firebase token validation
        if (!$token) {
            return RummyBaazi::errorResponse(1, [['code' => 401005, 'message' => "Token not provided."]]);
        }

        try {
            $verifiedIdToken = $this->auth->verifyIdToken($token, true);
        } catch (ExpiredToken $e) {
            return RummyBaazi::errorResponse(1, [['code' => 401002, 'message' => "Token expired."]]);
        } catch (InvalidToken $e) {
            return RummyBaazi::errorResponse(1, [['code' => 401003, 'message' => "Invalid Token."]]);
        } catch (InvalidSignature $e) {
            return RummyBaazi::errorResponse(1, [['code' => 401000, 'message' => "Unauthorized to access."]]);
        } catch (InvalidArgumentException $e) {
            return RummyBaazi::errorResponse(1, [['code' => 401006, 'message' => "An error while decoding token."]]);
        } catch (\Exception $e) {
            return RummyBaazi::errorResponse(1, [['code' => 401006, 'message' => "An error while decoding token."]]);
        }

        return $next($request);
    }

    public function checkKycForPartialBannedState($request, $checkStateBan)
    {
        $kycStatus = true;
        $route = $request->route();

        $routeName = $route ? $route[1]['as'] ?? null : null;

        if ($routeName == 'depositAmountSuggestionV1' || $routeName == 'depositAmountSuggestionV2' || $routeName == 'depositV3') {
            $state = isset($checkStateBan['region']) ? $this->getStateDetails($checkStateBan['region']) : null;

            if (!empty($state) && $state->PARTIAL_BANNED_STATUS == 1) {
                $restrictedHours = json_decode($state->OTHER_DETAILS);

                $start = explode(":", $restrictedHours->start_time);
                $end = explode(":", $restrictedHours->end_time);

                $restrictedHourStart = Carbon::today()->setTime($start[0], $start[1], $start[2]);
                $restrictedHourEnd = Carbon::today()->setTime($end[0], $end[1], $end[2]);
                $currentHour =  Carbon::now();

                if ($currentHour->gte($restrictedHourStart) && $currentHour->lte($restrictedHourEnd)) {
                    $kycStatus = "blackhours";
                } else {
                    $userDetails = \RummyBaazi::user();
                    // $userSetting = UserSetting::select('KYC_EXPIRY')->where('USER_ID', $userDetails->USER_ID)->first();

                    if ($userDetails->KYC_REMAINING_STEPS == 0) {
                        $kycStatus = true;
                        // $kycStatus = (!empty($userSetting->KYC_EXPIRY) && Carbon::parse($userSetting->KYC_EXPIRY)->lt(Carbon::today())) ? false : true;
                    } else {
                        $kycStatus = false;
                    }
                }
            }
        }

        return $kycStatus;
    }
}
