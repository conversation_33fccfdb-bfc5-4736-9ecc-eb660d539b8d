<?php

namespace App\Traits;

use App\Facades\DynamoDB;
use App\Helpers\InvoidAesCipher;
use App\Models\MasterTransactionHistory;
use App\Models\OtpTracking;
use App\Models\State;
use App\Models\Tracking;
use App\Models\User;
use App\Models\UserKyc;
use App\Models\UserKycDetail;
use App\Models\UserPoint;
use App\RummyBaazi\RummyBaazi;
use Aws\DynamoDb\Marshaler;
use Illuminate\Support\Facades\Auth;
use \Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Kickbox\Client as KickboxClient;

trait CommonTraits
{

    /**
     * Common JsonResponse
     *
     * This method is used For Common response,
     * for all the api's
     *
     * @param int $apiCode
     * @param array $data
     * @param array $optional
     * $optional = ['headers' => ['Authorization' => 'df'], 'httpCode' =>201, 'api_version' => '2.0.1']
     *
     * @return object \Illuminate\Http\JsonResponse
     *
     * @example use with RummyBaazi Facade:
     * \RummyBaazi::successResponse(1, ['test=>'testing'],200)
     *
     * @example by inherted Controller:
     * $this->successResponse(1, ['test=>'testing'], ['httpCode' => 401])
     */
    public function successResponse($apiCode, $data = [], $optional = [])
    {
        $headers = $optional['headers'] ?? [];
        $httpCode = $optional['httpCode'] ?? 200;
        $api_version = $optional['api_version'] ?? config('rummy_config.api_version');

        $responseData['status'] = "success";
        $responseData['api_version'] = $api_version;
        $responseData['api_code'] = $apiCode;
        $responseData['response'] = [
            "response_id" => $api_version . $apiCode . time()
        ];
        if ($data) {
            $responseData['response'] += [
                "data" => $data
            ];
        }

        return \response()->json($responseData, $httpCode, $headers);
    }

    /**
     * Common JsonResponse
     *
     * This method is used For Common response,
     * for all the api's
     *
     * @param int $apiCode
     * @param array $errorBag
     * @param array $optional
     * $optional = ['headers' => ['Authorization' => 'df'], 'api_version' => '2.0.1']
     *
     * @return object \Illuminate\Http\JsonResponse
     *
     * @example use with RummyBaazi Facade:
     * \RummyBaazi::errorResponse(1, [ ['code'=> 500032, "message" => "Internal server error"] ],500)
     *
     * @example by inherted Controller:
     * $this->errorResponse(1, [ ['code'=> 500032, "message" => "Internal server error"] ])
     */
    public function errorResponse($apiCode, $errorBag, $httpCode = null, $optional = [])
    {

        $headers = $optional['headers'] ?? [];
        $httpCode = $httpCode ?? substr($errorBag[0]['code'], 0, 3) ?? 500;
        $api_version = $optional['api_version'] ?? config('rummy_config.api_version');

        $responseData['status'] = "fail";
        $responseData['api_version'] = $api_version;
        $responseData['api_code'] = $apiCode;
        $responseData['error'] = $errorBag;

        return \response()->json($responseData, $httpCode, $headers);
    }

    /**
     * Common method for Api/cUrl/Ajax call
     *
     * This method is used for Api/cUrl/Ajax as a client,
     * it will handle the response or error and will dispatch the
     * resonse with statusCode, response Message and body
     *
     * @param array $param
     * $param
     *  ['url']                 "https://www.rummybaazi.com"
     *  ['form_params'] array   ["xyz"=>"abc"] optional
     *  ['headers]      array   ['X-Requested-With' => 'XMLHttpRequest'] optional
     *  ['method']      string  "POST", "GET"
     *
     * @return object (object)[]
     *  ['statusCode' => $response->getStatuscode(),
     *   'responseMessage' => $response->getReasonPhrase(),
     *   'body' => json_decode($response->getBody())
     *  ]
     * @example
     * $param['url' => "https://www.rummybaazi.com", 'form_params'=> [ 'xyz' => 'abc'] ]
     *
     */
    public function ApiCallCurl($param)
    {

        $defaultHeaders = [];
        $headers = $param['headers'] ?? [];
        $headers = array_merge($defaultHeaders, $headers);
        $headers_curl = \array_map(function ($key, $value) {
            return "$key:$value";
        }, \array_keys($headers), \array_values($headers));

        $url = $param['url'];
        $form_params = $param['form_params'] ?? [];
        $method = $param['method'] ?? 'POST';

        $form_params_curl = [];
        if (\in_array("Content-Type:application/json", $headers_curl)) {
            $form_params_curl = \json_encode($form_params);
        } elseif (\in_array("Content-Type:application/x-www-form-urlencoded", $headers_curl)) {
            $form_params_curl = http_build_query($form_params);
        } elseif (\in_array("Content-Type:application/x-www-form-urlencoded utf-8", $headers_curl)) {
            $form_params_curl = utf8_encode(http_build_query($form_params));
        } else {
            $form_params_curl = $method == "POST" ? $form_params : http_build_query($form_params);
        }


        $default_curl_options_array = [
            CURLOPT_URL => $url,
            CURLOPT_HTTPHEADER => $headers_curl,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_POSTFIELDS => $form_params_curl,
            CURLOPT_RETURNTRANSFER => $param['IS_CURLOPT_RETURNTRANSFER'] ?? TRUE,
            CURLOPT_FOLLOWLOCATION => $param['IS_CURLOPT_FOLLOWLOCATION'] ?? TRUE,
            CURLOPT_SSL_VERIFYPEER => $param['IS_CURLOPT_SSL_VERIFYPEER'] ?? FALSE,
        ];
        $extra_curl_options_array = $param['extra_curl_options_array'] ?? [];
        $curl_options_array = $extra_curl_options_array + $default_curl_options_array;

        $ch = curl_init();
        curl_setopt_array($ch, $curl_options_array);

        $result = curl_exec($ch);
        $errors = curl_error($ch);
        $httpStatus = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        return (object) [
            'response' => empty($errors) && !empty($result) ? json_decode($result) : $result,
            'errors' => $errors,
            'httpStatus' => $httpStatus
        ];
    }

    /**
     * generate Random string or salt
     *
     * @param mixed $randomVariable null
     * @param int $min_length 0
     * @param int $max_length 10
     * @return string
     */
    public function generateSalt($randomVariable = null, $min_length = 0, $max_length = 10)
    {
        $keyString = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789" . $randomVariable;
        $salt = substr(str_shuffle($keyString), $min_length, $max_length);
        return $salt;
    }

    /**
     * User Login Tracking
     *
     * Track all the event in login process,
     * for trace failer and other events
     *
     * @param array $data
     * @example $data contains
     * SESSION_ID,USERNAME,USER_ID,ACTION_NAME,STATUS,LOGIN_STATUS,DEVICE_TYPE,APP_TYPE,
     * BROWSER_NAME,SCREEN_SIZE,FINGERPRINT_ID,OPERATING_SYSTEM,HACK_COUNT,ACCOUNT_STATUS
     */
    public function storeLoginActivities($data)
    {
        \extract($data);

        $currentDateTime = Carbon::now();

        $data['DATE_TIME'] = $currentDateTime;
        $data['SYSTEM_IP'] = $data['SYSTEM_IP'] ?? $this->getRealIpAddr();

        try {
            DB::transaction(function () use ($data) {
                $tracking = new Tracking($data);
                $tracking->save();
            }, 5);
        } catch (\Exception $e) {
            Log::error("Login Activity: " . json_encode($data) . " error: " . $e->getMessage());
        }

        if (in_array($STATUS, [1, 4, 8, 9, 10, 12, 13])) {
            // Update LOGIN_STATUS,SESSION_ID,USER_LAST_LOGIN on successful login
            $user = RummyBaazi::user() ?? User::findOrFail($USER_ID);
            $user->LOGIN_STATUS = 1;
            $user->HACK_COUNT = 0;
            $user->USER_LAST_LOGIN = $currentDateTime;
            $user->save();
        } else if ($STATUS == 2) {
            // Update Hack Count on unsuccessful login
            $user = User::findOrFail($USER_ID);
            $user->HACK_COUNT += 1;
            if ($HACK_COUNT >= 4 && $ACCOUNT_STATUS) {
                $user->TWO_STEP_VERIFICATION = 1;
            }
            $user->save();
        }
    }

    public function login_tracking($SESSION_ID, $USERNAME, $USER_ID, $ACTION_NAME, $STATUS, $LOGIN_STATUS, $DEVICE_TYPE, $APP_TYPE, $BROWSER_NAME, $SCREEN_SIZE, $FINGERPRINT_ID, $OPERATING_SYSTEM, $HACK_COUNT, $ACCOUNT_STATUS, $SYSTEM_IP = null)
    {
        $data = [
            'SESSION_ID' => $SESSION_ID,
            'USERNAME' => $USERNAME,
            'USER_ID' => !empty($USER_ID) ? $USER_ID : NULL,
            'ACTION_NAME' => $ACTION_NAME,
            'STATUS' => $STATUS,
            'LOGIN_STATUS' => $LOGIN_STATUS,
            'DEVICE_TYPE' => $DEVICE_TYPE,
            'APP_TYPE' => $APP_TYPE,
            'BROWSER_NAME' => $BROWSER_NAME,
            'SCREEN_SIZE' => $SCREEN_SIZE,
            'FINGERPRINT_ID' => $FINGERPRINT_ID,
            'OPERATING_SYSTEM' => $OPERATING_SYSTEM,
            'HACK_COUNT' => $HACK_COUNT,
            'ACCOUNT_STATUS' => $ACCOUNT_STATUS,
            'SYSTEM_IP' => $SYSTEM_IP
        ];

        $this->storeLoginActivities($data);
    }

    public function getRealIpAddr($server = null)
    {
        $server = $server ?? $_SERVER;
        if (!empty($server['HTTP_CLIENT_IP'])) { //check ip from share internet
            $ip = $server['HTTP_CLIENT_IP'];
        } elseif (!empty($server['HTTP_X_FORWARDED_FOR'])) {  //to check ip is pass from proxy
            $ip = $server['HTTP_X_FORWARDED_FOR'];
        } else {
            $ip = $server['REMOTE_ADDR'];
        }
        return $ip;
    }

    public function validateEmailAddress($email)
    {
        $validDomainList = config('rummy_config.kickbox_setting.validDomainList');
        $invalidDomainList = config('rummy_config.kickbox_setting.invalidDomainList');

        $emailDomainArray = explode('@', $email);
        $emailDomain = explode('.', $emailDomainArray[1]);
        $emailDomainProvider = strtolower($emailDomain[0]);

        if (
            (!in_array($emailDomainProvider, $validDomainList)) &&
            (!in_array(strtolower($emailDomainArray[1]), $invalidDomainList))
        ) {
            $kickBoxApiKey = config('rummy_config.kickbox_setting.kickBoxApiKey');
            $client = new KickboxClient($kickBoxApiKey);
            $kickbox = $client->kickbox();
            try {
                $response = $kickbox->verify($email);
                $kickboxResultList = config('rummy_config.kickbox_setting.kickboxResultList');
                $kickboxReasonList = config('rummy_config.kickbox_setting.kickboxReasonList');
                $kickboxDisposableList = config('rummy_config.kickbox_setting.kickboxDisposableList');
                if (
                    in_array($response->body["result"], $kickboxResultList) ||
                    in_array($response->body["reason"], $kickboxReasonList) ||
                    in_array($response->body["disposable"], $kickboxDisposableList)
                ) {
                    return false;
                }
            } catch (Exception $e) {
                return false;
            }
        } elseif (in_array($emailDomainArray[1], $invalidDomainList)) {
            return false;
        }

        return true;
    }

    public function generateURL($PATH, $PARAMETERS = null, $BASE_URL = null)
    {
        $BASE_URL = $BASE_URL ?? config('rummy_config.live_site_url');
        $PATH = $PATH;
        $PARAMETERS = is_array($PARAMETERS) && !empty($PARAMETERS) ? "?" . http_build_query($PARAMETERS) : "";
        $URL = $BASE_URL . $PATH . $PARAMETERS;
        return $URL;
    }

    //branch curl method to track user login
    public function branchLoginEvent($userId, $username, $branchFingerprint, $SYSTEM_IP = null)
    {

        $branchApiKey = config('rummy_config.branch.apiKey');
        $branchUrl = config('rummy_config.branch.standardEventUrl');
        $ip = $SYSTEM_IP ?? $this->getRealIpAddr();

        $data = [
            "name" => "LOGIN",
            "user_data" => [
                "developer_identity" => "$userId",
                "country" => "IN",
                "language" => "en",
                "local_ip" => $ip,
                "os" => "OTHER",
                "browser_fingerprint_id" => $branchFingerprint
            ],
            "custom_data" => [
                "username" => $username
            ],
            "branch_key" => $branchApiKey
        ];

        $data = $this->ApiCallCurl([
            "url" => $branchUrl,
            "form_params" => $data,
            "headers" => [
                "Content-Type" => "application/json"
            ],
            "extra_curl_options_array" => [
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1
            ]
        ]);
        $this->userActivitiesTracking($userId, "BRANCH_LOGIN_EVENT", $data);
    }

    public function getProfileStatus($userId, $tempStatus = 0)
    {
        $user = User::select('KYC_REMAINING_STEPS')->where('USER_ID', $userId)->first();
        return $user->KYC_REMAINING_STEPS;
    }

    public function verifyOtpForEmail($userId, $otp)
    {
        $user = User::select('ACTIVATION_CODE', 'EMAIL_ID')->where('USER_ID', $userId)->limit(1)->first();

        try {
            if ($user != null) {
                $otpTracking = OtpTracking::select('otp_no')
                    ->where('user_id', $userId)
                    ->where('otp_request_type', 'SEND_OTP_EMAIL')
                    ->where('INTERNAL_REFERENCE_NO', $user->ACTIVATION_CODE)
                    ->where('date_of_verify', '>', DB::raw('NOW() - INTERVAL 30 MINUTE'))
                    ->pluck('otp_no')
                    ->first();

                if ($otpTracking != null) {
                    if ($otp == $otpTracking) {
                        /*  otp verified */
                        $user->EMAIL_VERIFY = '1';
                        $user->ACTIVATION_CODE = "";
                        $user->save();

                        $this->updateOTPTracking($user->EMAIL_ID, $otp, $userId, "VERIFY_OTP_EMAIL", "success", "", $user->ACTIVATION_CODE, 2, "");
                        return [
                            'code' => 200000,
                            'desc' => "User email verified successfully"
                        ];
                    } else {
                        /* otp is not verified */
                        return [
                            'code' => 422006,
                            'desc' => "You have entered wrong otp"
                        ];
                    }
                } else {
                    return [
                        'code' => 422006,
                        'desc' => "This OTP has been Expired."
                    ];
                }
            } else {
                return [
                    'code' => 500000,
                    'desc' => "Internal Server Error"
                ];
            }
        } catch (\Exception $exception) {
            return [
                'code' => 500000,
                'desc' => "Internal Server Error"
            ];
        }
    }

    public function getUserPoints($uid)
    {
        return UserPoint::select('USER_TOT_BALANCE', 'USER_DEPOSIT_BALANCE', 'USER_WIN_BALANCE', 'USER_PROMO_BALANCE')
            ->where('USER_ID', $uid)
            ->where('COIN_TYPE_ID', 1)
            ->first();
    }

    public function getDepositTransactionHistory($uid, $startDate, $endDate)
    {
        return MasterTransactionHistory::select('TRANSACTION_TYPE_ID', DB::raw('SUM(TRANSACTION_AMOUNT) as TOTAL'), DB::raw('COUNT(USER_ID) as TRANSACTION_COUNT'))
            ->groupBy('TRANSACTION_TYPE_ID')
            ->where('USER_ID', $uid)
            ->where('TRANSACTION_TYPE_ID', 62)
            ->whereBetween('TRANSACTION_DATE', [$startDate, $endDate])
            ->first() ?? null;
    }

    public function calculateProfileCompletionPercentage($userInfo)
    {
        if (($userInfo->FIRSTNAME == NULL || $userInfo->FIRSTNAME == 'none' || $userInfo->FIRSTNAME == '') || ($userInfo->LASTNAME == NULL || $userInfo->LASTNAME == 'none' || $userInfo->LASTNAME == '')) {
            return 40;
        } else {
            if ($userInfo->KYC_REMAINING_STEPS == 0) {
                return 100;
            } elseif ($userInfo->KYC_REMAINING_STEPS == 1) {
                return 85;
            } elseif ($userInfo->KYC_REMAINING_STEPS == 2) {
                return 75;
            } elseif ($userInfo->KYC_REMAINING_STEPS == 3) {
                return 65;
            } else {
                /* if kyc remaining step is null and 4 */
                return 55;
            }
        }
    }

    protected function verify_hmac_of_juspay($params, $secret, $signature)
    {
        $receivedHmac = $signature;
        // UrlEncode key/value pairs
        /*foreach ($params as $key => $value) {
            if ($key != 'signature' && $key != 'signature_algorithm') {
                $encoded_params[urlencode($key)] = urlencode($value);
            }
        }*/
        foreach ($params as $key => $value) {
            if ($key != 'signature' && $key != 'signature_algorithm') {
                if (is_array($value)) {
                    foreach ($value as $key1 => $value1) {
                        if (is_array($value1)) {
                            foreach ($value1 as $key2 => $value2) {
                                if (is_array($value2)) {
                                    foreach ($value2 as $key3 => $value3) {
                                        $encoded_params[urlencode($key3)] = urlencode($value3);
                                    }
                                } else {
                                    $encoded_params[urlencode($key2)] = urlencode($value2);
                                }
                            }
                        } else {
                            $encoded_params[urlencode($key1)] = urlencode($value1);
                        }
                    }
                } else {
                    $encoded_params[urlencode($key)] = urlencode($value);
                }
            }
        }
        ksort($encoded_params);
        $serialized_params = "";
        foreach ($encoded_params as $key => $value) {
            $serialized_params = $serialized_params . $key . "=" . $value . "&";
        }
        $serialized_params = urlencode(substr($serialized_params, 0, -1));
        $computedHmac = base64_encode(hash_hmac('sha256', $serialized_params, $secret, true));
        $receivedHmac = urldecode($receivedHmac);
        return [
            'status' => (urldecode($computedHmac) == $receivedHmac),
            'rehash' => urldecode($computedHmac)
        ];
    }

    protected function getUserAvatar()
    {
        $avatar = '';
        return $avatar;
    }

    protected function isUserInBannedState($userId)
    {
        $userState = User::select('STATE')
            ->where('USER_ID', $userId)
            ->where('BYPASS_BANNED_STATE', '0')
            ->pluck('STATE')
            ->first();

        if (empty($userState)) {
            return false;
        }

        $bannedState = DB::table('state')
            ->where('STATUS', 2)
            ->where('StateName', $userState)
            ->count();

        return $bannedState > 0 ? $userState : false;
    }

    public function putOnKinesis(array $data)
    {
        $kinesisClient = $this->getKinesisClient();
        // $groupID = "input to a hash function that maps the partition key (and associated data) to a specific shard";
        $groupID = $name = config('rummy_config.kinesis.stream_name');
        /** Content data type must be object or string. */
        try {
            $result = $kinesisClient->PutRecord([
                'Data' => json_encode($data),
                'StreamName' => $name,
                'PartitionKey' => $groupID
            ]);
            return $result;
        } catch (\Aws\Exception\AwsException $e) {
            \Log::error($e);
            return false;
        }
    }

    /**
     * This is a helper function to connect AWS Kinesis
     */
    private function getKinesisClient()
    {
        $kinesisClient = new \Aws\Kinesis\KinesisClient([
            //'profile' => 'default',
            //'version' => '2013-12-02',
            'version' => 'latest',
            'region' => config('rummy_config.kinesis.region')
        ]);
        return $kinesisClient;
    }

    protected function createCleverTapEvent($data)
    {
        $action = "CleverTap_" . $data['event'];
        try {
            /* Kinesis call for clever-top */
            $data['evtData']['identity'] = $data['userId'];
            $cTapKinesisData = [
                "type" => "event",
                "type_name" => $data['event'],
                "data" => $data['evtData']
            ];
            $response = $this->putOnKinesis($cTapKinesisData);
            //$response = $this->ApiCallCurl($formParams);

            /* store clever-tap response on user activities*/
            $this->userActivitiesTracking($data['userId'], $action, $response);
        } catch (\Exception $exception) {
            $this->userActivitiesTracking($data['userId'], $action, $exception);
        }
    }

    function checkBannedState()
    {
        $ip = $this->getRealIpAddr();
        $bannedStateSettings = $this->ApiCallCurl(["url" => config('rummy_config.bannedState.bannedStateLambdaUrl') . '?IP=' . $ip . '&service_type=TXN', "method" => "GET"]);

        $bannedStateBypassUsers = $this->ApiCallCurl(["url" => config('rummy_config.bannedState.bannedStateBypassJson'), "method" => "GET"]);

        if ($bannedStateSettings->httpStatus == "200" && empty($bannedStateSettings->errors)) {
            if ($bannedStateSettings->response->isBanned && !in_array(getUserId(), $bannedStateBypassUsers->response ?? [])) {
                return true;
            }
        }
        return false;
    }

    function getRgion()
    {
        $ipVendor = "";
        $bannedStateSettings = $this->ApiCallCurl(["url" => config('rummy_config.bannedState.bannedStateSettingJson'), "method" => "GET"]);
        if ($bannedStateSettings->httpStatus == "200" && empty($bannedStateSettings->errors)) {
            if (isset($bannedStateSettings->response->active_vendor)) {
                $ipVendor = trim($bannedStateSettings->response->active_vendor) ? trim($bannedStateSettings->response->active_vendor) : "";
            }
        }
        $ip = $this->getRealIpAddr();
        $ipVendor = trim($ipVendor) ? trim($ipVendor) : "proip";
        //$ip = '************';
        if ($ipVendor == "proip") {
            $vendorUrl = str_replace("{{ip}}", $ip, config('rummy_config.bannedState.proIpUrl'));
            $ipDetails = $this->ApiCallCurl(["url" => $vendorUrl, "method" => "GET"]);
            if ($ipDetails->httpStatus == "200" && empty($ipDetails->errors)) {
                if (isset($ipDetails->response->regionName)) {
                    return $ipDetails->response->regionName;
                }
            }
        } else {

            $vendorUrl = str_replace("{{ip}}", $ip, config('rummy_config.bannedState.ipstackUrl'));
            $ipDetails = $this->ApiCallCurl(["url" => $vendorUrl, "method" => "GET"]);
            if ($ipDetails->httpStatus == "200" && empty($ipDetails->errors)) {
                if (isset($ipDetails->response->region_name)) {
                    return $ipDetails->response->region_name;
                }
            }
        }

        return "";
    }

    protected function generateDigilockerUrl($userId)
    {
        $kycInitRequest = "kyc-init-digilocker-aadhaar";
        try {
            $user = User::select('USER_ID', 'EMAIL_VERIFY', 'FIRSTNAME', 'LASTNAME', 'CONTACT', 'KYC_REMAINING_STEPS', 'USER_OTHER_DETAILS')
                ->where('USER_ID', $userId)
                ->limit(1)
                ->first();

            $kycAttempt = UserKycDetail::where('USER_ID', $userId)
                ->where('DOCUMENT_STATUS', 0)/* document status 0 for failures */
                ->count();

            if ($kycAttempt > config('rummy_config.maxKycAttempt'))
                return "Max KYC Attempt Reached";

            /* check kyc is already done */
            $isAadhaarDone = UserKycDetail::where([
                'DOCUMENT_TYPE' => 1,
                'DOCUMENT_STATUS' => 1,
                'APPROVED_BY' =>  "SYSTEM",
                "DOCUMENT_SUB_TYPE" => 1,
                "USER_ID" => $userId
            ])->count() > 0;

            if ($isAadhaarDone) {
                $this->userActivitiesTracking($userId, $kycInitRequest . '-failed', 'Aadhar is already verified.');
                return "Aadhar Already Verified";
            }

            $userRemainingStep = $user->KYC_REMAINING_STEPS ?? ($user->EMAIL_VERIFY == 1 ? 3 : 4);

            $userOtherDetails = $user->USER_OTHER_DETAILS ?? null;
            $kycUrlExpiredOn = $userOtherDetails['digilocker_kyc_url_expired_on'] ?? null;
            $kycInitUrl = $userOtherDetails['digilocker_kyc_url'] ?? null;

            if (!empty($kycInitUrl) && $kycUrlExpiredOn > Carbon::now()) {
                /* already initiated */
                return $kycInitUrl;
            } else {
                $mobile = $user->CONTACT;

                if (strlen($mobile) == 12 && substr($mobile, 0, 2) == "91") {
                    $mobile = substr($mobile, 2, 10);
                }

                $digiLokerKey = config('rummy_config.invoid.invoid_cipher_digilocker_key');
                $userPayload = InvoidAesCipher::encrypt($digiLokerKey, InvoidAesCipher::getIV(), '{"doctype":"aadhaar"}');

                $formParams = array(
                    "userId" => $user->USER_ID,
                    "payload" => "$userPayload",
                    "consent" => "Y",
                    "consent-text" => "I agree to share my data with cardbaazi.",
                    "callback-url" => config('rummy_config.invoid.invoid_pending_url_digilocker'),
                    "cancel-url" => config('rummy_config.invoid.invoid_fail_url_digilocker'),
                );
                $response = "";

                try {
                    /* first time initiated or re-initiated after 3-days */
                    $response = $this->ApiCallCurl([
                        "url" => config('rummy_config.invoid.init-step-digilocker'),
                        "form_params" => $formParams,
                        "headers" => [
                            "authkey" => config('rummy_config.invoid.authKey'),
                            "Content-Type" => 'application/json'
                        ],
                    ]);
                } catch (\Exception $e) {
                    $this->userActivitiesTracking($userId, "ERROR_IN_INVOID_CALL", $response);
                }
                /* track user activity on kyc verification */
                $this->userActivitiesTracking($userId, $kycInitRequest, $response);

                if (!empty($response) && $response->response->status == 200) {
                    $responsePayload = InvoidAesCipher::decrypt($digiLokerKey, $response->response->payload);
                    $decryptedPayload = json_decode($responsePayload);
                    $urlExpireOn = Carbon::now()->addDays(3)->toDateTimeString();

                    $userOtherDetails['digilocker_kyc_url'] = $decryptedPayload->url;
                    $userOtherDetails['digilocker_kyc_url_expired_on'] = $urlExpireOn;
                    $userOtherDetails['u_txn_id'] = $decryptedPayload->transactionId;

                    User::where('USER_ID', $userId)->update([
                        'USER_OTHER_DETAILS' => $userOtherDetails
                    ]);
                    return  $decryptedPayload->url;
                } else {
                    \Log::error($response);
                    $this->userActivitiesTracking($userId, "ERROR_IN_INVOID_CALL", $response);
                    return false;
                }
            }
        } catch (\Exception $exception) {
            \Log::error($exception);
            $this->userActivitiesTracking($userId, "ERROR_IN_INVOID_CALL", $exception);
            return false;
        }
    }

    /**
     * Send Data to Cardbaazi Real Time Events Table for Real Time Notification
     * 
     * @param $userId
     * @param $action
     * @param $data
     */
    public function sendDataToCardbaaziRealTimeEventsTable($userID, $action, $data)
    {
        $data = json_encode($data);

        $columns = [
            'user_id' => (int)$userID,
            'is_seen' => false,
            'event_uuid' => \Illuminate\Support\Str::uuid(),
            'action' => $action,
            'event_data' => $data,
            'created_date' => Carbon::now()
        ];

        $marshaller = new Marshaler();

        return DynamoDB::getConnection()->putItem([
            'TableName' => 'cardbaazi_realtime_events',
            'Item' => $marshaller->marshalJson(json_encode($columns))
        ]);
    }
    public function getStateDetails($state)
    {
        return State::select('STATE_CODE', 'display_status', 'StateName', 'PARTIAL_BANNED_STATUS', 'OTHER_DETAILS')->where('StateName', 'like', '%' . $state . '%')->orWhere('possible_names', 'like', '%' . $state . '%')->first();
    }
    function isUserBypassBannedState()
    {
        $byPassUsersList = $this->ApiCallCurl(["url" => config('rummy_config.bannedState.bannedStateBypassJson'), "method" => "GET"]);
        $isBypass = false;

        if (!empty($byPassUsersList->response) && is_array($byPassUsersList->response)) {
            $isBypass = in_array(getUserId(), $byPassUsersList->response);
        }

        return $isBypass;
    }
    function isIpFromBannedState()
    {
        $ip = $this->getRealIpAddr();
        $ip = '*************';
        $lambdaUrl = config('rummy_config.bannedState.bannedStateLambdaUrl');
        $lambdaUrl = str_replace('{{IP}}', $ip, $lambdaUrl);
        $lambdaResp = $this->ApiCallCurl(["url" => $lambdaUrl, "method" => "GET"]);
        return [
            'isBanned' => $lambdaResp->response->isBanned ?? false,
            'region' => $lambdaResp->response->ipdetails->region_name ?? ''
        ];
    }
}
