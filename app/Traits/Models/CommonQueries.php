<?php

namespace App\Traits\Models;

use App\Models\GlobalConfig;
use App\Models\OtpTracking;
use App\Models\RFTransactionHitory;
use App\Models\Tracking;
use App\Models\UserActivity;
use App\Models\ResponsibleGameSetting;
use App\Models\User;
use App\Models\PaymentTransaction;
use App\Models\WithdrawalCriteria;
use App\Models\PlayerLedger;
use App\Models\WithdrawTransactionHistory;
use App\Models\MasterTransactionHistoryRewardCoins;
use App\Models\TournamentUserTicket;
use App\Models\ReferFriendTable;
use App\Models\PromoCampaign;
use App\Models\UserPoint;
use App\Models\MasterTransactionHistory;
use App\Models\UserKycDetail;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

/**
 *
 */
trait CommonQueries
{

    public function updateOTPTracking($MOBILE_NO, $OTP_NO, $USER_ID, $OTP_REQUEST_TYPE, $OTP_RESPONSE_TYPE, $OTP_RESPONSE_MESSAGE, $INTERNAL_REFERENCE_NO, $STATUS, $refresh_token = "")
    {
        $columns = [
            'MOBILE_NO' => $MOBILE_NO,
            'OTP_NO' => $OTP_NO,
            'STATUS' => $STATUS,
            'USER_ID' => $USER_ID,
            'OTP_REQUEST_TYPE' => $OTP_REQUEST_TYPE,
            'OTP_RESPONSE_TYPE' => $OTP_RESPONSE_TYPE,
            'OTP_RESPONSE_MESSAGE' => $OTP_RESPONSE_MESSAGE,
            'INTERNAL_REFERENCE_NO' => $INTERNAL_REFERENCE_NO,
            'refresh_token' => $refresh_token
        ];

        return OtpTracking::create($columns);
    }

    public function checkMaximumNoofIP($clientIP)
    {
        date_default_timezone_set('Asia/Kolkata');
        $thestime = date('Y-m-d H:i');
        $datetime_from = date("Y-m-d H:i", strtotime("-30 minutes", strtotime($thestime)));
        if ($clientIP != "" && $clientIP != NULL && $clientIP != 0) {
            $ipAddress = $clientIP;
        } else {
            $ipAddress = $this->getRealIpAddr();
        }

        $actionName = 'register';
        $trackingCount = Tracking::select('TRACKING_ID')
            ->where('SYSTEM_IP', $ipAddress)
            ->where('ACTION_NAME', $actionName)
            ->whereBetween('DATE_TIME', [$datetime_from, $thestime])
            ->count();

        $regLimit = config('rummy_config.reg_setting.registrationLimitToIP');
        if ($trackingCount >= $regLimit) {
            return FALSE; //valid
        } else {
            return TRUE; //invalid
        }
    }

    public function checkIPBlockStatus($clientIP)
    {
        $actionName = 'register';
        $trackingCount = Tracking::select('TRACKING_ID')
            ->where('SYSTEM_IP', $clientIP)
            ->where('ACTION_NAME', $actionName)
            ->where('STATUS', 2)
            ->count();
        if ($trackingCount > 0) {
            return FALSE; //valid
        } else {
            return TRUE; //invalid
        }
    }

    public function checkFingerprintRestriction($fingerprintId)
    {
        date_default_timezone_set('Asia/Kolkata');
        $thestime = date('Y-m-d H:i');
        $datetime_from = date("Y-m-d H:i", strtotime("-1440 minutes", strtotime($thestime)));
        if (!empty($fingerprintId)) {
            $actionName = 'register';
            $fingerprintCount = Tracking::select('TRACKING_ID')
                ->where('FINGERPRINT_ID', $fingerprintId)
                ->where('ACTION_NAME', $actionName)
                ->whereBetween('DATE_TIME', [$datetime_from, $thestime])
                ->count();

            $fingerprintLimit = config('rummy_config.reg_setting.registrationLimitToFingerprint');
            if ($fingerprintCount >= $fingerprintLimit) {
                return FALSE; //valid
            } else {
                return TRUE; //invalid
            }
        }
        return TRUE;
    }

    public function userActivitiesTracking($USER_ID, $ACTION, $DATA)
    {
        $DATA = \json_encode($DATA);
        $columns = [
            'USER_ID' => $USER_ID,
            'ACTION' => $ACTION,
            'DATA' => $DATA,
            'CREATED_DATE' => \DB::raw('NOW()')
        ];
        return UserActivity::create($columns);
    }

    public function fireRegistrationTrackingEvents($userID, $username, $signUpCode, $clientIP, $userAgent, $branchFingerprint, $branchFire = TRUE, $googleFire = TRUE, $gaActionRegistration = '', $others = [])
    {
        $os = (isset($others['os']) && !empty($others['os'])) ? $others['os'] : "OTHER";
        if ($userID != NULL && $userID != "") {
            if ($googleFire) {
                $gaTrackingId = config('rummy_config.ga_setting.gaTrackingId');
                $gaCategorySignups = config('rummy_config.ga_setting.gaCategorySignups');
                if ($gaActionRegistration != '') {
                    $gaActionRegistrationSuccess = config('rummy_config.ga_setting.' . $gaActionRegistration);
                } else {
                    $gaActionRegistrationSuccess = config('rummy_config.ga_setting.gaActionRegistrationSuccess');
                }

                $gaUrl = config('rummy_config.ga_setting.gaMeasurementProtocolUrl');

                $data = array();
                $data['v'] = "1";
                $data['tid'] = $gaTrackingId;
                $data['cid'] = $this->gen_uuid();
                $data['t'] = "event";
                $data['ec'] = $gaCategorySignups;
                $data['ea'] = $gaActionRegistrationSuccess;
                $data['el'] = $userID;
                $data['ev'] = "1";

                $data = $this->ApiCallCurl([
                    "url" => $gaUrl,
                    "form_params" => $data,
                    "headers" => [
                        "Content-Type" => "application/x-www-form-urlencoded utf-8"
                    ],
                    "extra_curl_options_array" => [
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1
                    ]
                ]);
                $this->userActivitiesTracking($userID, "GA_REGISTRATION_CURL", $data);
            }

            if ($branchFire) {
                $signUpCode = empty($signUpCode) ? "No Sign Up Code" : $signUpCode;
                $clientIP = empty($clientIP) ? "**************" : $clientIP;
                $username = empty($username) ? "No Username Recorded" : $username;
                $branchFingerprint = empty($branchFingerprint) ? "123456789123456789" : $branchFingerprint;
                $userAgent = empty($userAgent) ? "" : $userAgent;

                $branchApiKey = config('rummy_config.branch.apiKey');
                $branchUrl = config('rummy_config.branch.standardEventUrl');

                $data = [
                    "name" => "COMPLETE_REGISTRATION",
                    "user_data" => [
                        "developer_identity" => "$userID",
                        "country" => "IN",
                        "os" => $os,
                        "user_agent" => $userAgent,
                        "language" => "en",
                        "local_ip" => "$clientIP",
                        "browser_fingerprint_id" => "$branchFingerprint"
                    ],
                    "custom_data" => [
                        "username" => "$username",
                        "signupcode" => "$signUpCode",
                        "branchFingerprint" => "$branchFingerprint"
                    ],
                    "branch_key" => "$branchApiKey"
                ];

                $data = $this->ApiCallCurl([
                    "url" => $branchUrl,
                    "form_params" => $data,
                    "headers" => [
                        "Content-Type" => "application/json"
                    ],
                    "extra_curl_options_array" => [
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1
                    ]
                ]);
                $this->userActivitiesTracking($userID, "BRANCH_REGISTRATION_CURL", $data);
            }
        }
    }

    public function gen_uuid()
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff)
        );
    }

    /**
     * Common Method for checking user existence
     *
     * This method is used to check wether user is
     * on a rummy break
     * @param String or Int $userId
     * @return Boolean
     */
    protected function isUserOnRummyBreak($userId)
    {
        $rummyBreak = ResponsibleGameSetting::select('BREAK_ACTIVE', 'BREAK_TO_DATE', 'BREAK_FROM_DATE')
            ->where('USER_ID', $userId)
            ->where('BREAK_ACTIVE', 1)
            ->where('BREAK_TO_DATE', ">=", date('Y-m-d H:i:s'))
            ->where('BREAK_FROM_DATE', "<=", date('Y-m-d H:i:s'))
            ->first();

        if (!empty($rummyBreak)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Method to get user details.
     * <AUTHOR> Aroraa <<EMAIL>>
     */
    /* Method to get user details START */
    protected function getUserDetails($userID)
    {
        return User::select(
            "USER_ID",
            "USERNAME",
            "FIRSTNAME",
            "LASTNAME",
            "EMAIL_ID",
            "CONTACT",
            "STATE",
            "USER_OTHER_DETAILS",
            "PARTNER_ID",
            "REGISTRATION_TIMESTAMP",
            "LEVEL_CONFIG_ID",
            "EMAIL_VERIFY",
            "KYC_REMAINING_STEPS"
        )
            ->where("USER_ID", $userID)
            ->first();
    }

    /* Method to get user details END */

    protected function executeProcedureTicket($tourId, $playerId, $source, $reason)
    {
        $tour_ticket_in = DB::select("call sp_tournament_registration_v2(?,?,?,?,@out1)", [
            $playerId,
            $tourId,
            $source,
            $reason,
        ]);

        $tour_ticket_out = DB::select(DB::raw("select @out1 as out1"));
        $out = $tour_ticket_out[0]->out1;
        return $out;
    }

    protected function executeProcedureRAF($type, $refid, $userId, $amount)
    {
        DB::statement(
            'call sp_raf_procedure_new_v2(:TYPE, :RAF_ID, :USER_ID, :AMOUNT, @refNo)',
            [
                "TYPE" => (int)$type,
                "RAF_ID" => (int)$refid,
                "USER_ID" => (int)$userId,
                "AMOUNT" => (int)$amount
            ]
        );
        $sp_raf_procedure = DB::select(DB::raw("select @refNo as 'refNo'"));
        return $sp_raf_procedure[0]->refNo;
    }

    /* protected function executeProcedure($tourId, $playerId) {
      //Tournament Auto Registration
      $tour_ticket_in = DB::select("call sp_tournament_registration(?,?,@out1)", [
      $tourId,
      $playerId,
      ]);
      $tour_ticket_out = DB::select(DB::raw("select @out1 as 'out' "));
      $out1 = $tour_ticket_out[0]->out;
      $out = ['ref_no' => $out1];
      return $out;
      } */

    protected function assignUserToAnUserForRAF($userPromOfferPlace, $userID)
    {
        /* THIS IS TO ASSIGN AN USER TO AFFILIATE USER */
        if (!empty($userPromOfferPlace->REFERRAL_USERNAME) && !empty($userPromOfferPlace->RAF_PERCENTAGE)) {
            $query = ReferFriendTable::query();
            $query->from(app(ReferFriendTable::class)->getTable());
            $query->where('USER_ID', $userID);
            $getUserSelect = $query->select('*');
            $rsUserReferredStatus = $getUserSelect->first();

            $query = User::query();
            $query->from(app(User::class)->getTable());
            $query->where('USER_ID', $userID);
            $getUserSelect = $query->select('USER_ID', 'PARTNER_ID');
            $rsUserPartnerID = $getUserSelect->first();
            if (empty($rsUserReferredStatus)) {
                $refByName = $userPromOfferPlace->REFERRAL_USERNAME;
                $query = User::query();
                $query->from(app(User::class)->getTable());
                $query->where('USERNAME', $refByName);
                $getUserSelect = $query->select('USER_ID', 'USERNAME');
                $rsRefByID = $getUserSelect->first();
                $refByID = $rsRefByID->USER_ID;
                $userName = $rsUserReferredStatus->USERNAME;
                $promoCampaignID = $userPromOfferPlace->PROMO_CAMPAIGN_ID;
                $promoCampaignVal = $userPromOfferPlace->RAF_PERCENTAGE;
                $userPartnerID = $rsUserPartnerID->PARTNER_ID;
                $currentDate = date('Y-m-d H:i:s');
                $raf = new ReferFriendTable();
                $raf->REF_BY_ID = $refByID;
                $raf->REF_BY_NAME = $refByName;
                $raf->USER_ID = $userID;
                $raf->USERNAME = $userName;
                $raf->PROMO_CAMPAIGN_ID = $promoCampaignID;
                $raf->PROMO_CAMPAIGN_VALUE = $promoCampaignVal;
                $raf->PARTNER_ID = $userPartnerID;
                $raf->CREATED_DATE = $currentDate;
                $raf->STATUS = 1;
                $raf->UPDATED_DATE = $currentDate;
                $rafRes = $raf->save();
            }
        }
        /* THIS IS TO ASSIGN AN USER TO AFFILIATE USER */
    }

    protected function createNewLedger($userId)
    {
        if ($this->isLedgerAlreadyStarted($userId)) {
            return "Already Exist";
        }

        //get current financial year
        $ledgerConfig = getLedgerConfig();
        $nextYear = (date('m') > 3) ? date("Y", strtotime("+1 year")) : date('Y');
        $currentYear = (date('m') <= 3) ? date("Y", strtotime("-1 year")) : date('Y');

        $fYearStart = $ledgerConfig['tds_financial_year_start'];
        $fYearEnd = $nextYear . "-03-31 23:59:00";

        $pendingTransaction = PaymentTransaction::where('USER_ID', $userId)->whereIn('PAYMENT_TRANSACTION_STATUS', [255])->whereBetween('PAYMENT_TRANSACTION_CREATED_ON', [$fYearStart, $fYearEnd])->count();

        if ($pendingTransaction == 0) {

            if (!empty($userId)) {

                //new exempted value
                $exemptedValue = $ledgerConfig['tds_exemption'];

                // Deleting all player ledger entries
                PlayerLedger::where(['USER_ID' => $userId])->delete();

                \DB::beginTransaction();
                try {
                    date_default_timezone_set('Asia/Kolkata');
                    $currentDate = date('Y-m-d H:i:s');
                    // DB::enableQueryLog();
                    $insertPlayerLedgerData = new PlayerLedger();
                    $insertPlayerLedgerData->USER_ID                                  = $userId;
                    $insertPlayerLedgerData->ACTION                                   = 'LEDGER_START';
                    $insertPlayerLedgerData->TRANSACTION_AMOUNT                       = 0;
                    $insertPlayerLedgerData->INTERNAL_REFERENCE_NO                    = '';
                    $insertPlayerLedgerData->TOTAL_DEPOSITS                           = 0;
                    $insertPlayerLedgerData->TOTAL_WITHDRAWALS                        = 0;
                    $insertPlayerLedgerData->TOTAL_TAXABLE_WITHDRAWALS                = 0;
                    $insertPlayerLedgerData->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX          = 0;
                    $insertPlayerLedgerData->EXEMPTION_10K                            = $exemptedValue;
                    $insertPlayerLedgerData->TOTAL_ELIGIBLE_WITHDRAWAL_WITHOUT_TAX    = $exemptedValue;
                    $insertPlayerLedgerData->CREATED_DATE                             = $currentDate;
                    $insertPlayerLedgerData->PAYMENT_TRANSACTION_CREATED_ON           = $fYearStart;

                    $insertPlayerLedgerData->save();

                    \DB::commit();
                } catch (\Exception $e) {
                    \DB::rollBack();
                    throw $e;
                }

                //Get all previous success deposits and approved withdrawal records
                PaymentTransaction::select('USER_ID', 'INTERNAL_REFERENCE_NO', 'PAYMENT_TRANSACTION_AMOUNT', 'TRANSACTION_TYPE_ID', 'PAYMENT_TRANSACTION_STATUS', 'PAYMENT_TRANSACTION_CREATED_ON')
                    ->where('USER_ID', $userId)
                    ->whereIn('PAYMENT_TRANSACTION_STATUS', [125, 103, 208])
                    ->whereBetween('PAYMENT_TRANSACTION_CREATED_ON', [$fYearStart, $fYearEnd])
                    ->orderBy('UPDATED_DATE', 'ASC')
                    ->chunk(1000, function ($approvedSuccessDepositsWithdrawals) use ($userId) {
                        foreach ($approvedSuccessDepositsWithdrawals as $approvedSuccessDepositsWithdrawalsResult) {
                            $referenceNumber = $approvedSuccessDepositsWithdrawalsResult->INTERNAL_REFERENCE_NO;
                            $action = "DEPOSIT";
                            if ($approvedSuccessDepositsWithdrawalsResult->TRANSACTION_TYPE_ID == 10) {
                                //WITHDRAWAL                                
                                $this->updatePlayerLedgerWithdraw($referenceNumber, 208);
                            } else {
                                //DEPOSIT                                
                                $this->updatePlayerLedgerDeposit($referenceNumber);
                            }
                        }
                    });

                return  "SUCCESS";
            } else {
                return  "FAILED";
            }
        } else {
            return  "PENDING";
        }
    }

    // Function to update player ledger table on a successfull deposit
    protected function updatePlayerLedgerDeposit($referenceNumber)
    {
        $ledgerStartAction = 'LEDGER_START';

        $paymentDetailsResult = $this->getPaymentDetails($referenceNumber);

        $ledgerConfig = getLedgerConfig();

        if (!empty($paymentDetailsResult->INTERNAL_REFERENCE_NO)) {

            $userId = $paymentDetailsResult->USER_ID;
            $depositAmount = $paymentDetailsResult->PAYMENT_TRANSACTION_AMOUNT;
            $paymentTransactionCreatedOn = $paymentDetailsResult->PAYMENT_TRANSACTION_CREATED_ON;

            #Ledger Start If not started yet in deposit case
            if (!$this->isLedgerAlreadyStarted($userId)) {

                $newTotalDeposits                       = 0;
                $newTotalWithdrawals                    = 0;
                $newTotalTaxableWithdrawals             = 0;
                $newEligibleWithdrawalWithoutTax        = 0;
                $newTenKExemption                       = $ledgerConfig['tds_exemption'];
                $newTotalEligibleWithdrawalWithoutTax   = $newEligibleWithdrawalWithoutTax + $newTenKExemption;

                $this->insertIntoPlayerLedger($userId, $ledgerStartAction, 0, '', $newTotalDeposits, $newTotalWithdrawals, $newTotalTaxableWithdrawals, $newEligibleWithdrawalWithoutTax, $newTenKExemption, $newTotalEligibleWithdrawalWithoutTax, $paymentTransactionCreatedOn);
            }

            #Check if reference number already exists then skip
            $isReferenceExists = PlayerLedger::where('INTERNAL_REFERENCE_NO', $referenceNumber)->count();
            if ($isReferenceExists > 0) {
                return false;
            }

            #Get Previous Ledger Data
            $previousLedgerResult = $this->getPreviousPlayerLedgerData($userId);

            if (!empty($previousLedgerResult)) {

                #Case: if there are previous records of player ledger
                $newTotalDeposits = $depositAmount + $previousLedgerResult->TOTAL_DEPOSITS;
                $newTotalWithdrawals = $previousLedgerResult->TOTAL_WITHDRAWALS;
                $newTotalTaxableWithdrawals = $previousLedgerResult->TOTAL_TAXABLE_WITHDRAWALS;
                $newEligibleWithdrawalWithoutTax = $previousLedgerResult->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX + $depositAmount;
                if ($newEligibleWithdrawalWithoutTax < 0) {
                    $newEligibleWithdrawalWithoutTax = 0;
                }
                $newTenKExemption = $previousLedgerResult->EXEMPTION_10K;

                $newTotalEligibleWithdrawalWithoutTax = $newEligibleWithdrawalWithoutTax + $newTenKExemption;
            } else {
                #Case: If there was no previous ledger details
                $newTotalDeposits = $depositAmount;
                $newTotalWithdrawals = 0;
                $newTotalTaxableWithdrawals = 0;
                $newEligibleWithdrawalWithoutTax = $depositAmount;
                $newTenKExemption = $ledgerConfig['tds_exemption'];
                $newTotalEligibleWithdrawalWithoutTax = $newEligibleWithdrawalWithoutTax + $newTenKExemption;
            }

            $this->insertIntoPlayerLedger($userId, 'DEPOSIT', $depositAmount, $referenceNumber, $newTotalDeposits, $newTotalWithdrawals, $newTotalTaxableWithdrawals, $newEligibleWithdrawalWithoutTax, $newTenKExemption, $newTotalEligibleWithdrawalWithoutTax, $paymentTransactionCreatedOn);

            return "PLAYER_LEDGER_INSERTED";
        } else {
            return "REF_NOT_FOUND";
        }
    }

    // Function to update player ledger table on a successfull withdrawal
    protected function updatePlayerLedgerWithdraw($internalReferenceNumber, $WITHDRAW_INITIATED_TRANSACTION_STATUS)
    {
        //----------------------------------------------------------
        // CHECK IF REFERENCE NO ALREADY EXISTS
        // IF EXISTS DON'T MOVE FORWARD FOR PLAYER LEDGER INSERTION
        //-----------------------------------------------------------
        $isReferenceExists = PlayerLedger::where('INTERNAL_REFERENCE_NO', $internalReferenceNumber)->count();
        if ($isReferenceExists > 0) {
            return false;
        }

        //------------------------------------------------------------
        // GET WITHDRAWAL DATA
        //------------------------------------------------------------
        $withdrawalDetailsResult = WithdrawTransactionHistory::select(
            'USER_ID',
            'INTERNAL_REFERENCE_NO',
            'WITHDRAW_AMOUNT',
            'TRANSACTION_DATE'
        )
            ->where('INTERNAL_REFERENCE_NO', $internalReferenceNumber)
            ->where('TRANSACTION_STATUS_ID', $WITHDRAW_INITIATED_TRANSACTION_STATUS)
            ->limit(0)
            ->first();

        if (!empty($withdrawalDetailsResult)) {

            // If records exists for that withdrawal
            $userId = $withdrawalDetailsResult->USER_ID;
            $withdrawAmount = $withdrawalDetailsResult->WITHDRAW_AMOUNT;
            $paymentTransactionCreatedOn = $withdrawalDetailsResult->TRANSACTION_DATE;
            //Get previous player ledger data


            $previousLedgerResult = PlayerLedger::select(
                'ACTION',
                'TOTAL_DEPOSITS',
                'TOTAL_WITHDRAWALS',
                'TOTAL_TAXABLE_WITHDRAWALS',
                'ELIGIBLE_WITHDRAWAL_WITHOUT_TAX',
                'EXEMPTION_10K',
                'TOTAL_ELIGIBLE_WITHDRAWAL_WITHOUT_TAX'
            )
                ->where(['USER_ID' => $userId])
                ->orderBy('PLAYER_LEDGER_ID', 'DESC')
                ->limit(1)->first();

            if (!empty($previousLedgerResult)) {

                $newTotalTaxableWithdrawals = $previousLedgerResult->TOTAL_TAXABLE_WITHDRAWALS;
                $newEligibleWithdrawalWithoutTax = $previousLedgerResult->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX;
                $newTenKExemption = $previousLedgerResult->EXEMPTION_10K;
                $newTotalEligibleWithdrawalWithoutTax = $previousLedgerResult->TOTAL_ELIGIBLE_WITHDRAWAL_WITHOUT_TAX;

                // Amount Exemption Calaculation
                if ($newTotalEligibleWithdrawalWithoutTax <= $withdrawAmount) {
                    $newTotalTaxableWithdrawals += ($withdrawAmount - $newEligibleWithdrawalWithoutTax);
                    $newTenKExemption = 0;
                    $newEligibleWithdrawalWithoutTax = 0;
                    $newTotalEligibleWithdrawalWithoutTax = 0;
                } else {
                    $newTotalEligibleWithdrawalWithoutTax -=  $withdrawAmount;
                    if ($withdrawAmount <= $newEligibleWithdrawalWithoutTax) {
                        $newEligibleWithdrawalWithoutTax -= $withdrawAmount;
                    } else {
                        $needToSubFromTenk = $withdrawAmount - $newEligibleWithdrawalWithoutTax;

                        $newEligibleWithdrawalWithoutTax = 0;
                        $newTotalTaxableWithdrawals += $needToSubFromTenk;

                        if ($needToSubFromTenk <= $newTenKExemption) {
                            $newTenKExemption -= $needToSubFromTenk;
                        } else {
                            $newTenKExemption = 0;
                        }
                    }
                }

                if ($newTenKExemption < 0)
                    $newTenKExemption = 0;

                if ($newEligibleWithdrawalWithoutTax < 0)
                    $newEligibleWithdrawalWithoutTax = 0;

                if ($newTotalEligibleWithdrawalWithoutTax < 0) {
                    $newTotalEligibleWithdrawalWithoutTax = 0;
                    $newTenKExemption = 0;
                    $newEligibleWithdrawalWithoutTax = 0;
                }
                // END

                //------------------------------------------------
                // INSERTING IN PLAYER LEDGER
                //------------------------------------------------

                $insertPlayerLedgerData = new PlayerLedger();
                $insertPlayerLedgerData->USER_ID                                  = $userId;
                $insertPlayerLedgerData->ACTION                                   = 'WITHDRAWAL';
                $insertPlayerLedgerData->TRANSACTION_AMOUNT                       = $withdrawAmount;
                $insertPlayerLedgerData->INTERNAL_REFERENCE_NO                    = $internalReferenceNumber;
                $insertPlayerLedgerData->TOTAL_DEPOSITS                           = $previousLedgerResult->TOTAL_DEPOSITS;
                $insertPlayerLedgerData->TOTAL_WITHDRAWALS                        = ($withdrawAmount + $previousLedgerResult->TOTAL_WITHDRAWALS);
                $insertPlayerLedgerData->TOTAL_TAXABLE_WITHDRAWALS                = $newTotalTaxableWithdrawals;
                $insertPlayerLedgerData->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX          = $newEligibleWithdrawalWithoutTax;
                $insertPlayerLedgerData->EXEMPTION_10K                            = $newTenKExemption;
                $insertPlayerLedgerData->TOTAL_ELIGIBLE_WITHDRAWAL_WITHOUT_TAX    = $newTotalEligibleWithdrawalWithoutTax;
                $insertPlayerLedgerData->CREATED_DATE                             = date('Y-m-d H:i:s');
                $insertPlayerLedgerData->PAYMENT_TRANSACTION_CREATED_ON           = $paymentTransactionCreatedOn;
                return $insertPlayerLedgerData->save();
            }
        }
    }







    protected function checkReferenceAlreadyExistsInPlayerLedger($referenceNumber)
    {
        $previousCriteriaResult = PlayerLedger::select('USER_ID')
            ->where('INTERNAL_REFERENCE_NO', $referenceNumber)
            ->first();
        if (!empty($previousCriteriaResult->USER_ID)) {
            return true;
        } else {
            return false;
        }
    }
    protected function isLedgerAlreadyStarted($user_id)
    {
        $isLedgerAlreadyStarted = PlayerLedger::select('USER_ID')->where('USER_ID', $user_id)->whereIn('ACTION', ['LEDGER_START', 'LEDGER_RESET'])->exists();

        return ($isLedgerAlreadyStarted) ? true : false;
    }

    protected function getWithdrawalDetails($referenceNo, $withdrawalStatus)
    {
        //Get userid, reference number, amount of withdrawal by passing reference number
        $transStatusId = array('203', '112');
        $withdrawalDetailsResult = PaymentTransaction::select('USER_ID', 'INTERNAL_REFERENCE_NO', 'PAYMENT_TRANSACTION_AMOUNT', 'PAYMENT_TRANSACTION_CREATED_ON')
            ->where('INTERNAL_REFERENCE_NO', $referenceNo)
            ->when($withdrawalStatus == "NOT_REVERT_REJECTED", function ($query) use ($transStatusId) {
                return $query->whereNotIn('PAYMENT_TRANSACTION_STATUS', $transStatusId);
            })
            ->first();
        return $withdrawalDetailsResult;
    }

    protected function getPreviousPlayerLedgerData($userId)
    {
        $previousCriteriaResult = PlayerLedger::select('ACTION', 'TOTAL_DEPOSITS', 'TOTAL_WITHDRAWALS', 'TOTAL_TAXABLE_WITHDRAWALS', 'ELIGIBLE_WITHDRAWAL_WITHOUT_TAX', 'EXEMPTION_10K', 'TOTAL_ELIGIBLE_WITHDRAWAL_WITHOUT_TAX')
            ->where('USER_ID', $userId)
            ->orderBy('PLAYER_LEDGER_ID', 'DESC')
            ->first();
        return $previousCriteriaResult;
    }

    protected function insertIntoPlayerLedger($userId, $action, $transactionAmount, $referenceNumber, $totalDeposits, $totalWithdrawals, $totalTaxableWithdrawals, $eligibleWithdrawalWithoutTax, $tenKExemption, $totalEligibleWithdrawalWithoutTax, $paymentTransactionCreatedOn)
    {
        date_default_timezone_set('Asia/Kolkata');
        $currentDate = date('Y-m-d H:i:s');
        // DB::enableQueryLog();
        $insertPlayerLedgerData = new PlayerLedger();
        $insertPlayerLedgerData->USER_ID = $userId;
        $insertPlayerLedgerData->ACTION = $action;
        $insertPlayerLedgerData->TRANSACTION_AMOUNT = $transactionAmount;
        $insertPlayerLedgerData->INTERNAL_REFERENCE_NO = $referenceNumber;
        $insertPlayerLedgerData->TOTAL_DEPOSITS = $totalDeposits;
        $insertPlayerLedgerData->TOTAL_WITHDRAWALS = $totalWithdrawals;
        $insertPlayerLedgerData->TOTAL_TAXABLE_WITHDRAWALS = $totalTaxableWithdrawals;
        $insertPlayerLedgerData->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX = $eligibleWithdrawalWithoutTax;
        $insertPlayerLedgerData->EXEMPTION_10K = $tenKExemption;
        $insertPlayerLedgerData->TOTAL_ELIGIBLE_WITHDRAWAL_WITHOUT_TAX = $totalEligibleWithdrawalWithoutTax;
        $insertPlayerLedgerData->CREATED_DATE = $currentDate;
        $insertPlayerLedgerData->PAYMENT_TRANSACTION_CREATED_ON = $paymentTransactionCreatedOn;

        return $insertPlayerLedgerData->save();
        //  $queriesss = DB::getQueryLog();
        //    dd($queriesss);
    }

    /* protected function getPreviousPlayerLedgerDataForRevert($userId, $referenceNumber) {

      $referenceLedgerResult = PlayerLedger::select('PLAYER_LEDGER_ID')
      ->where('USER_ID', $userId)
      ->where('INTERNAL_REFERENCE_NO', $referenceNumber)
      ->first();
      //print_r ($referenceLedgerResult);exit;

      if (!empty($referenceLedgerResult->PLAYER_LEDGER_ID)) {
      $previousLedgerResult = PlayerLedger::select('PLAYER_LEDGER_ID', 'ACTION', 'TRANSACTION_AMOUNT', 'INTERNAL_REFERENCE_NO')
      ->where('USER_ID', $userId)
      ->where('PLAYER_LEDGER_ID', '>=', $referenceLedgerResult->PLAYER_LEDGER_ID)
      ->orderBy('PLAYER_LEDGER_ID', 'ASC')
      ->get();

      //print_r ($previousLedgerResult);exit;
      return $previousLedgerResult;
      } else {
      return "REF_NOT_FOUND";
      }
      } */

    /* protected function deletePlayerLedgerEntry($userId, $playerLedgerID) {
      return $deletedRows = PlayerLedger::where(['USER_ID' => $userId, 'PLAYER_LEDGER_ID' => $playerLedgerID])->delete();
      } */

    protected function updateWithdrawalCriteria($paymentRefNo)
    {
        // Get userid, amount,promo code, deposit time of payment by passing reference number
        $paymentDetailsResult = $this->getPaymentDetails($paymentRefNo);

        // If records exists for that successful payment
        if (!empty($paymentDetailsResult)) {
            $userId = $paymentDetailsResult->USER_ID;
            $depositAmount = $paymentDetailsResult->PAYMENT_TRANSACTION_AMOUNT;
            $codeUsed = $paymentDetailsResult->PROMO_CODE;
            $depositDate = $paymentDetailsResult->UPDATED_DATE;
            $comment = "";

            //Get previous withdrawal criteria data
            $previousCriteriaResult = $this->getPreviousWithdrawalCriteria($userId);
            if (!empty($previousCriteriaResult)) { // Case: if there are previous records of criteria
                $previousATK = $previousCriteriaResult->AMOUNT_TO_BE_KEPT;
                $previousCoinsToBeMade = $previousCriteriaResult->COINS_TO_BE_MADE;
                $previousCoinsStartDate = $previousCriteriaResult->COINS_START_DATE;
                $withdrawalCriteriaId = $previousCriteriaResult->WITHDRAWAL_CRITERIA_ID;
                $codeCoins = 0;
                $maxDepositAmount = 0;
                $depositAmountToBeConsidered = 0;
                $codeCoinsToBeConsidered = 0;

                //Get coins required for that code
                $codeCoinsResult = $this->getCoinsRequired($codeUsed);
                if (!empty($codeCoinsResult) && $codeCoinsResult->COINS_REQUIRED != NULL && $codeCoinsResult->COINS_REQUIRED != "") {
                    $codeCoins = $codeCoinsResult->COINS_REQUIRED;
                    $maxDepositAmount = $codeCoinsResult->MAXIMUM_DEPOSIT_AMOUNT;
                    $bonusChipsFlag = $codeCoinsResult->P_PROMO_CHIPS;
                    $bonusPercentage = $codeCoinsResult->P_PROMO_VALUE;
                    $depositAmountToBeConsidered = $depositAmount;
                    $codeCoinsToBeConsidered = $codeCoins;
                    if ($depositAmount > $maxDepositAmount) {
                        $depositAmountToBeConsidered = $maxDepositAmount;
                    }
                    if ($bonusChipsFlag == 1) {
                        if (!empty($bonusPercentage)) {
                            $bonusAmount = round(($bonusPercentage / 100) * $depositAmountToBeConsidered);
                            if ($bonusAmount < $codeCoins && $bonusAmount != 0) {
                                $codeCoinsToBeConsidered = $bonusAmount * 1.5;
                            }
                        } else {
                            if ($codeCoinsResult->PROMO_CAMPAIGN_TYPE_ID == 1) {
                                if ($codeCoinsResult->TIER_TYPE == 1) {
                                    $tier_level_id = User::select('LEVEL_CONFIG_ID')->find($userId);
                                    $user_tier_level_id = $tier_level_id->LEVEL_CONFIG_ID;

                                    $tier_bonus = $codeCoinsResult->TIER_BONUS_DATA;
                                    $tier_bonus_data = json_decode($tier_bonus);

                                    foreach ($tier_bonus_data as $key => $value) {
                                        $level = $value->level;
                                        if ($user_tier_level_id == $level) {
                                            $bonus_percentage = $value->bonus_percentage;
                                        }
                                    }

                                    $bonusAmount = round(($bonus_percentage / 100) * $depositAmountToBeConsidered);
                                    if ($bonusAmount < $codeCoins && $bonusAmount != 0) {
                                        $codeCoinsToBeConsidered = $bonusAmount * 1.5;
                                    }
                                }
                            }
                        }

                        if ($codeCoinsToBeConsidered > $codeCoins) {
                            $codeCoinsToBeConsidered = $codeCoins;
                        } else {
                            $codeCoinsToBeConsidered = $codeCoinsToBeConsidered;
                        }
                    }
                }
                if ($codeCoinsToBeConsidered == 0) {
                    $depositAmountToBeConsidered = 0;
                }

                $newATK = 0; // Initialise to 0
                $newCoinsToBeMade = 0; // Initialise to 0
                $newCoinsStartDate = $depositDate;

                // Get Coins made since previousCoinsStartDate
                $accCoinsResult = $this->getAccCoinsBetweenDates($userId, $previousCoinsStartDate, $depositDate);
                $coinsMadeSinceLastDeposit = $accCoinsResult->ACC_REWARD_COINS;
                // Get Total Balance of user including cash tables balance and future tournaments buy-ins
                $coinType = "1";
                $cashBalanceResult = $this->getUserBalanceFromUserPointsByCoinType($coinType, $userId);
                $totalCashBalance = $cashBalanceResult->USER_DEPOSIT_BALANCE + $cashBalanceResult->USER_WIN_BALANCE - $depositAmount;

                $currentBalPointGame = $this->getCurrentBalanceOnGamePoint($userId);
                $currentBalPoolGame = $this->getCurrentBalanceOnGamePool($userId);
                $currentBalanceOnTour = $this->getCurrentBalanceOnTour($userId);
                $currentBalanceOnPoker = $this->getCurrentBalancePoker($userId);
                $currentBalancePokerTour = $this->getCurrentBalancePokerTour($userId);
                \Log::info("In withdrawal criteria");
                $balAtTheTimeOfDeposit = $totalCashBalance + $currentBalPointGame + $currentBalPoolGame + $currentBalanceOnTour + $currentBalancePokerTour + $currentBalanceOnPoker;
                // dd($balAtTheTimeOfDeposit);

                // 		// Calculate new amount to be kept
                if ($codeUsed != NULL && $codeUsed != "") { // If there is a code used
                    if (($coinsMadeSinceLastDeposit > $previousCoinsToBeMade) || ($balAtTheTimeOfDeposit < (0.1 * $previousATK))) {
                        // If the user has made the coins OR almost lost amount
                        $newATK = $depositAmountToBeConsidered;
                        $comment = $comment . "\"LOCKED AMT.\" revised to \"" . $newATK . "\" as you have either \"COMPLETED\" the \"COINS CRITERIA\" or you almost \"LOST\" your \"PREVIOUS LOCKED AMT.\" and you used the code \"" . $codeUsed . "\".";
                    } else {
                        // If the user hasnt made the coins and hasnt lost deposit amount also
                        if ($coinsMadeSinceLastDeposit == 0) {
                            $ratValue = 0;
                        } else {
                            $ratValue = $coinsMadeSinceLastDeposit / $previousCoinsToBeMade;
                        }
                        $ratio = 1 - ($ratValue);
                        if (($ratio * $previousATK) > $balAtTheTimeOfDeposit) {
                            // If the user has made coins in ratio to his balance
                            $newATK = $balAtTheTimeOfDeposit + $depositAmountToBeConsidered;
                            $comment = $comment . "\"LOCKED AMT.\" revised to \"(" . $balAtTheTimeOfDeposit . "+" . $depositAmountToBeConsidered . ")=" . $newATK . "\" as you have \"NOT ACCUMULATED\" the \"REQUIRED COINS\" and \"YOU HAVE NOT LOST THE PREVIOUS LOCKED AMT.\" but your \"BAL. AT THE TIME OF DEPOSIT\" i.e. " . $balAtTheTimeOfDeposit . " was \"LESS THAN\" the \"(" . $ratio . "x" . $previousATK . ")\" and you used the code \"" . $codeUsed . "'.";
                        } else {
                            // If the user hasnt made coins in ratio to his balance
                            $newATK = ($ratio * $previousATK) + $depositAmountToBeConsidered;
                            $comment = $comment . "New \"LOCKED AMT.\" has been changed to the \"CURR. DEPOSIT AMOUNT + (RATIO OF COINS MADE x PREVIOUS AMOUNT TO BE KEPT)\" as you have \"NOT COMPLETED\" the \"COINS CRITERIA\" and \"YOU HAVE NOT LOST THE PREVIOUS AMOUNT TO BE KEPT\" but your \"BAL AT THE TIME OF DEPOSIT\" was \"GREATER THAN\" the \"(RATIO OF COINS MADE x PREVIOUS AMOUNT TO BE KEPT)\" as there was a \"CODE USED\".";
                        }
                    }
                } else { //If no code used
                    if (($coinsMadeSinceLastDeposit > $previousCoinsToBeMade) || ($balAtTheTimeOfDeposit < (0.1 * $previousATK))) {
                        // If the user has made the coins OR almost lost amount
                        $newATK = 0;
                        $comment = $comment . "New \"AMOUNT TO BE KEPT\" has been changed to \"0\" as you have \"COMPLETED\" the \"COINS CRITERIA\" or you almost \"LOST\" your \"PREVIOUS AMOUNT TO BE KEPT\" and there was \"NO CODE USED\".";
                    } else {
                        // If the user hasnt made the coins and hasnt lost deposit amount also
                        if ($coinsMadeSinceLastDeposit == 0) {
                            $ratValue = 0;
                        } else {
                            $ratValue = $coinsMadeSinceLastDeposit / $previousCoinsToBeMade;
                        }
                        $ratio = 1 - ($ratValue);
                        if (($ratio * $previousATK) > $balAtTheTimeOfDeposit) {
                            // If the user has made coins in ratio to his balance
                            $newATK = $balAtTheTimeOfDeposit;
                            $comment = $comment . "New \"AMOUNT TO BE KEPT\" has been changed to \"BAL AT THE TIME OF DEPOSIT\" as you have \"NOT COMPLETED\" the \"COINS CRITERIA\" and \"YOU HAVE NOT LOST THE PREVIOUS AMOUNT TO BE KEPT\" but your \"BAL AT THE TIME OF DEPOSIT\" was \"LESS THAN\" the \"(RATIO OF COINS MADE x PREVIOUS AMOUNT TO BE KEPT)\" and there was \"NO CODE USED\".";
                        } else {
                            // If the user hasnt made coins in ratio to his balance
                            $newATK = $ratio * $previousATK;
                            $comment = $comment . "New \"AMOUNT TO BE KEPT\" has been changed to \"(RATIO OF COINS MADE x PREVIOUS AMOUNT TO BE KEPT)\" as you have \"NOT COMPLETED\" the \"COINS CRITERIA\" and \"YOU HAVE NOT LOST THE PREVIOUS AMOUNT TO BE KEPT\" but your \"BAL AT THE TIME OF DEPOSIT\" was \"GREATER THAN\" the \"(RATIO OF COINS MADE x PREVIOUS AMOUNT TO BE KEPT)\" and there was \"NO CODE USED\".";
                        }
                    }
                }

                // Calculate new coins to be made
                if (($coinsMadeSinceLastDeposit > $previousCoinsToBeMade) || ($balAtTheTimeOfDeposit < (0.1 * $previousATK))) {
                    // 			// If the user has made the coins OR almost lost amount
                    if ($codeUsed == NULL || $codeUsed == "") {
                        // If no code used in current deposit the new coins to made is set as 0
                        $newCoinsToBeMade = 0;
                        $comment = $comment . " <---> New \"COINS TO BE MADE\" has been changed to \"0\" as you have \"COMPLETED\" the \"COINS CRITERIA\" or you almost \"LOST\" your \"PREVIOUS AMOUNT TO BE KEPT\" and there was \"NO CODE USED\".";
                    } else {
                        // If code is used in current deposit the new coins to made is set as current code coins
                        $newCoinsToBeMade = $codeCoinsToBeConsidered;
                        $comment = $comment . " <---> New \"COINS TO BE MADE\" has been changed to \"COINS OF CURR. CODE\" as you have \"COMPLETED\" the \"COINS CRITERIA\" or you almost \"LOST\" your \"PREVIOUS AMOUNT TO BE KEPT\" and there was a \"CODE USED\".";
                    }
                } else {
                    // 			// If the user hasnt made the coins and hasnt lost deposit amount also
                    $newCoinsToBeMade = ($previousCoinsToBeMade - $coinsMadeSinceLastDeposit) + $codeCoinsToBeConsidered;
                    $comment = $comment . " <---> New \"COINS TO BE MADE\" has been changed to \"(PREVIOUS COINS TO BE MADE - COINS MADE SINCE LAST DEPOSIT) + COINS OF CURR. CODE\" as you have \"NOT COMPLETED\" the \"COINS CRITERIA\" and you have \"NOT LOST\" your \"PREVIOUS AMOUNT TO BE KEPT\".";
                }

                if (!empty($withdrawalCriteriaId)) {
                    $this->updatePreviousWithdrawalCriteria($userId, $withdrawalCriteriaId, $coinsMadeSinceLastDeposit);
                }

                $this->insertIntoWithdrawalCriteria($userId, $balAtTheTimeOfDeposit, $depositAmount, $depositDate, $codeUsed, $codeCoinsToBeConsidered, $coinsMadeSinceLastDeposit, $newATK, $newCoinsToBeMade, $newCoinsStartDate, $comment);
            } else { // Case: if there was no previous criteria
                if ($codeUsed != NULL && $codeUsed != "") { // If there is a code used
                    // Initialise to 0
                    $codeCoins = 0;
                    $maxDepositAmount = 0;
                    $depositAmountToBeConsidered = 0;
                    $codeCoinsToBeConsidered = 0;

                    //Get coins required for that code
                    $codeCoinsResult = $this->getCoinsRequired($codeUsed);
                    if (!empty($codeCoinsResult) && $codeCoinsResult->COINS_REQUIRED != NULL && $codeCoinsResult->COINS_REQUIRED != "") {
                        $codeCoins = $codeCoinsResult->COINS_REQUIRED;
                        $maxDepositAmount = $codeCoinsResult->MAXIMUM_DEPOSIT_AMOUNT;
                        $bonusChipsFlag = $codeCoinsResult->P_PROMO_CHIPS;
                        $bonusPercentage = $codeCoinsResult->P_PROMO_VALUE;
                        $depositAmountToBeConsidered = $depositAmount;
                        $codeCoinsToBeConsidered = $codeCoins;
                        if ($depositAmount > $maxDepositAmount) {
                            $depositAmountToBeConsidered = $maxDepositAmount;
                        }
                        if ($bonusChipsFlag == 1) {
                            if (!empty($bonusPercentage) && $bonusPercentage != NULL && $bonusPercentage != "") {
                                $bonusAmount = round(($bonusPercentage / 100) * $depositAmountToBeConsidered);
                                if ($bonusAmount < $codeCoins && $bonusAmount != 0) {
                                    $codeCoinsToBeConsidered = $bonusAmount * 1.5;
                                }
                            } else {
                                if ($codeCoinsResult->PROMO_CAMPAIGN_TYPE_ID == 1) {
                                    if ($codeCoinsResult->TIER_TYPE == 1) {
                                        $tier_level_id = User::select('LEVEL_CONFIG_ID')->find($userId);
                                        $user_tier_level_id = $tier_level_id->LEVEL_CONFIG_ID;

                                        $tier_bonus = $codeCoinsResult->TIER_BONUS_DATA;
                                        $tier_bonus_data = json_decode($tier_bonus);

                                        foreach ($tier_bonus_data as $key => $value) {
                                            $level = $value->level;
                                            if ($user_tier_level_id == $level) {
                                                $bonus_percentage = $value->bonus_percentage;
                                            }
                                        }

                                        $bonusAmount = round(($bonus_percentage / 100) * $depositAmountToBeConsidered);
                                        if ($bonusAmount < $codeCoins && $bonusAmount != 0) {
                                            $codeCoinsToBeConsidered = $bonusAmount * 1.5;
                                        }
                                    }
                                }
                            }

                            if ($codeCoinsToBeConsidered > $codeCoins) {
                                $codeCoinsToBeConsidered = $codeCoins;
                            } else {
                                $codeCoinsToBeConsidered = $codeCoinsToBeConsidered;
                            }
                        }
                    }
                    if ($codeCoinsToBeConsidered == 0) {
                        $depositAmountToBeConsidered = 0;
                    }

                    $coinsMadeSinceLastDeposit = 0;
                    // Get Total Balance of user including cash tables balance and future tournaments buy-ins
                    $coinType = "1";
                    $cashBalanceResult = $this->getUserBalanceFromUserPointsByCoinType($coinType, $userId);
                    $totalCashBalance = $cashBalanceResult->USER_DEPOSIT_BALANCE + $cashBalanceResult->USER_WIN_BALANCE - $depositAmount;

                    $currentBalPointGame = $this->getCurrentBalanceOnGamePoint($userId);
                    $currentBalPoolGame = $this->getCurrentBalanceOnGamePool($userId);
                    $currentBalanceOnTour = $this->getCurrentBalanceOnTour($userId);

                    $balAtTheTimeOfDeposit = $totalCashBalance + $currentBalPointGame + $currentBalPoolGame + $currentBalanceOnTour;
                    $newATK = $depositAmountToBeConsidered;
                    $newCoinsToBeMade = $codeCoinsToBeConsidered;
                    $newCoinsStartDate = $depositDate;
                    $comment = $comment . "New \"AMOUNT TO BE KEPT\" has been changed to the \"CURR. DEPOSIT AMOUNT\" as there was \"NO PREVIOUS WITHDRAWAL CRITERIA\" and there was a \"CODE USED\".";
                    $comment = $comment . " <---> New \"COINS TO MADE\" has been changed to the \"CURR. CODE COINS\" as there was \"NO PREVIOUS WITHDRAWAL CRITERIA\" and there was a \"CODE USED\".";
                    $this->insertIntoWithdrawalCriteria($userId, $balAtTheTimeOfDeposit, $depositAmount, $depositDate, $codeUsed, $codeCoinsToBeConsidered, $coinsMadeSinceLastDeposit, $newATK, $newCoinsToBeMade, $newCoinsStartDate, $comment);
                } else { //If no code used
                    // Do nothing as no previous record of criteria and no code also used
                    // $comment = $comment."No previous criteria and no code used at the time of deposit so no new criteria inserted.";
                    // print_r('$comment - '.$comment.'<br/>');
                }
            }
        } else {
            // No successful payments found for that refernce number
            // $comment = $comment."Payment was not successfull.";
            // print_r('$comment - '.$comment.'<br/>');
        }
        // exit;
    }

    protected function getPaymentDetails($paymentRefNo)
    {
        $paymentDetailsResult = PaymentTransaction::select('USER_ID', 'INTERNAL_REFERENCE_NO', 'PAYMENT_TRANSACTION_AMOUNT', 'PROMO_CODE', 'UPDATED_DATE', 'PAYMENT_TRANSACTION_CREATED_ON')
            ->where('INTERNAL_REFERENCE_NO', $paymentRefNo)
            ->whereIn('PAYMENT_TRANSACTION_STATUS', [125, 103])
            ->first();
        return $paymentDetailsResult;
    }

    // Function to get last entry of a user from withdrawal_criteria table
    protected function getPreviousWithdrawalCriteria($userId)
    {
        $query = WithdrawalCriteria::query();
        $query->from(app(WithdrawalCriteria::class)->getTable());
        $query->where('USER_ID', $userId);
        $query->orderBy('DEPOSIT_DATE', 'desc');
        $getPaymentselect = $query->select('AMOUNT_TO_BE_KEPT', 'COINS_TO_BE_MADE', 'COINS_START_DATE', 'WITHDRAWAL_CRITERIA_ID');
        $previousCriteriaResult = $getPaymentselect->first();
        return $previousCriteriaResult;
    }

    // Function to get COINS_REQUIRED of a particular deposit code
    protected function getCoinsRequired($codeUsed)
    {
        $query = PromoCampaign::query();
        $query->from(app(PromoCampaign::class)->getTable() . " as pc");
        $query->leftJoin('promo_rule as pr', 'pc.PROMO_CAMPAIGN_ID', '=', 'pr.PROMO_CAMPAIGN_ID');
        $query->where('pc.PROMO_CAMPAIGN_CODE', $codeUsed);
        $query->where('pc.status', 1);
        $query->orderBy('START_DATE_TIME', 'DESC');
        $codeCoinsResult = $query->select(
            'pc.COINS_REQUIRED',
            'pc.MAXIMUM_DEPOSIT_AMOUNT',
            'pr.P_PROMO_CHIPS',
            'pr.P_PROMO_VALUE',
            'pc.TIER_TYPE',
            'pc.PROMO_CAMPAIGN_TYPE_ID',
            'pr.TIER_BONUS_DATA'
        );
        return $codeCoinsResult->first();
    }

    // Function to get coins accumulated between 2 dates
    protected function getAccCoinsBetweenDates($userId, $startDate, $endDate)
    {
        $query = MasterTransactionHistoryRewardCoins::query();
        $query->from(app(MasterTransactionHistoryRewardCoins::class)->getTable());
        $query->where('USER_ID', $userId);
        $query->whereBetween('TRANSACTION_DATE', [$startDate, $endDate]);
        $getPaymentselect = $query->select(DB::RAW('IFNULL(SUM(TRANSACTION_AMOUNT),0) AS ACC_REWARD_COINS'));
        $accCoinsResult = $getPaymentselect->first();
        return $accCoinsResult;
    }

    protected function getUserBalanceFromUserPointsByCoinType($coinTypeId, $userId)
    {
        $query = UserPoint::query();
        $query->from(app(UserPoint::class)->getTable());
        $query->where('USER_ID', $userId);
        $query->where('COIN_TYPE_ID', $coinTypeId);
        $getUserSelect = $query->select('*');
        $userPointInfo = $getUserSelect->first();
        return $userPointInfo;
    }

    // Function to get currentBalance of a user on cash tables
    protected function getCurrentBalanceOnCash($userId)
    {
        $currBalOnCashResult = DB::select(DB::raw("SELECT tmp1.INTERNAL_REFERENCE_NO, tmp2.CLOSING_TOT_BALANCE AS BAL FROM(SELECT DISTINCT(INTERNAL_REFERENCE_NO) AS INTERNAL_REFERENCE_NO FROM master_transaction_history WHERE USER_ID=$userId AND TRANSACTION_TYPE_ID=31 AND TRANSACTION_DATE >= ADDDATE(NOW() , INTERVAL -1000 hour) AND INTERNAL_REFERENCE_NO NOT IN(SELECT INTERNAL_REFERENCE_NO FROM master_transaction_history  WHERE USER_ID = $userId AND TRANSACTION_TYPE_ID IN (71) AND TRANSACTION_DATE >= ADDDATE(NOW() , INTERVAL -1000 hour))) tmp1 INNER JOIN(SELECT pwt.PLAY_GROUP_ID, pwt.INTERNAL_REFERENCE_NO, pwt.CLOSING_TOT_BALANCE FROM game_wallet_transaction pwt INNER JOIN(SELECT INTERNAL_REFERENCE_NO,MAX(PLAY_GROUP_ID) AS PLAY_GROUP_ID FROM game_wallet_transaction WHERE USER_ID=$userId AND TRANSACTION_DATE >= adddate(now(), interval -1000 hour) GROUP BY INTERNAL_REFERENCE_NO) t1 ON pwt.PLAY_GROUP_ID=t1.PLAY_GROUP_ID WHERE pwt.USER_ID=$userId AND TRANSACTION_DATE >= adddate(now(), interval -1000 hour) ORDER BY pwt.PLAY_GROUP_ID DESC) tmp2 ON tmp1.INTERNAL_REFERENCE_NO = tmp2.INTERNAL_REFERENCE_NO WHERE tmp2.CLOSING_TOT_BALANCE > 0"));
        // print_r($currBalOnCashQuery);exit;
        // $currBalOnCashResult = $currBalOnCashQuery->get();
        if (!empty($currBalOnCashResult)) {
            $totBuyInOnCash = 0;
            $reNoArray = array();
            foreach ($currBalOnCashResult as $currBalOnCash) {
                $reNoArray[] = $currBalOnCash->INTERNAL_REFERENCE_NO;
                $totBuyInOnCash = $totBuyInOnCash + $currBalOnCash->BAL;
            }
            // $reNoArray = rtrim($reNoArray, ", ");
            $query = MasterTransactionHistory::query();
            $query->from(app(MasterTransactionHistory::class)->getTable());
            $query->where('USER_ID', $userId);
            $query->where('BALANCE_TYPE_ID', 2);
            $query->whereIn('INTERNAL_REFERENCE_NO', $reNoArray);
            $checkRef = $query->select(DB::RAW('IFNULL(SUM(TRANSACTION_AMOUNT),0) AS PROMO_BUYIN'));
            $getPromoBuyInResult = $checkRef->first();
            if (($totBuyInOnCash - $getPromoBuyInResult->PROMO_BUYIN) < 0) {
                $buyInToBeConsideredOnCash = 0;
            } else {
                $buyInToBeConsideredOnCash = $totBuyInOnCash - $getPromoBuyInResult->PROMO_BUYIN;
            }
        } else {
            $buyInToBeConsideredOnCash = 0;
        }
        return $buyInToBeConsideredOnCash;
    }

    protected function getCurrentBalanceOnOFCCash($userId)
    {
        $buyInToBeConsideredOnOFCCash = 0;
        $currBalOnCashResult = DB::select(DB::raw("SELECT tmp1.INTERNAL_REFERENCE_NO, tmp2.CLOSING_TOT_BALANCE AS BAL FROM(SELECT DISTINCT(INTERNAL_REFERENCE_NO) AS INTERNAL_REFERENCE_NO FROM master_transaction_history WHERE USER_ID=$userId AND TRANSACTION_TYPE_ID=31 AND TRANSACTION_DATE >= ADDDATE(NOW() , INTERVAL -10 hour) AND INTERNAL_REFERENCE_NO NOT IN(SELECT INTERNAL_REFERENCE_NO FROM master_transaction_history  WHERE USER_ID = $userId AND TRANSACTION_TYPE_ID IN (71) AND TRANSACTION_DATE >= ADDDATE(NOW() , INTERVAL -10 hour))) tmp1 INNER JOIN(SELECT pwt.GAME_TRANSACTION_ID, pwt.INTERNAL_REFERENCE_NO, pwt.CLOSING_TOT_BALANCE FROM ofc_rummy_wallet_transaction pwt INNER JOIN(SELECT INTERNAL_REFERENCE_NO,MAX(GAME_TRANSACTION_ID) AS GAME_TRANSACTION_ID FROM ofc_rummy_wallet_transaction WHERE USER_ID=$userId AND CREATED >= adddate(now(), interval -10 hour) GROUP BY INTERNAL_REFERENCE_NO) t1 ON pwt.GAME_TRANSACTION_ID=t1.GAME_TRANSACTION_ID WHERE pwt.USER_ID=$userId AND CREATED >= adddate(now(), interval -10 hour) ORDER BY pwt.GAME_TRANSACTION_ID DESC) tmp2 ON tmp1.INTERNAL_REFERENCE_NO = tmp2.INTERNAL_REFERENCE_NO WHERE tmp2.CLOSING_TOT_BALANCE > 0"));
        // print_r($currBalOnCashResult);exit;
        // $currBalOnCashResult = $currBalOnCashQuery->get();
        if (!empty($currBalOnCashResult)) {
            $totBuyInOnCash = 0;
            $reNoArray = array();
            foreach ($currBalOnCashResult as $currBalOnCash) {
                $reNoArray[] = $currBalOnCash->INTERNAL_REFERENCE_NO;
                $totBuyInOnCash = $totBuyInOnCash + $currBalOnCash->BAL;
            }
            // $reNoArray = rtrim($reNoArray, ", ");
            $query = MasterTransactionHistory::query();
            $query->from(app(MasterTransactionHistory::class)->getTable());
            $query->where('USER_ID', $userId);
            $query->where('BALANCE_TYPE_ID', 2);
            $query->whereIn('INTERNAL_REFERENCE_NO', $reNoArray);
            $checkRef = $query->select(DB::RAW('IFNULL(SUM(TRANSACTION_AMOUNT),0) AS PROMO_BUYIN'));
            $getPromoBuyInResult = $checkRef->first();
            if (($totBuyInOnCash - $getPromoBuyInResult->PROMO_BUYIN) < 0) {
                $buyInToBeConsideredOnOFCCash = 0;
            } else {
                $buyInToBeConsideredOnOFCCash = $totBuyInOnCash - $getPromoBuyInResult->PROMO_BUYIN;
            }
        } else {
            $buyInToBeConsideredOnOFCCash = 0;
        }
        return $buyInToBeConsideredOnOFCCash;
    }

    // Function to get total buy-ins made by user in upcoming tournaments
    protected function getCurrentBalanceOnTour($userId)
    {
        $currentDate = date('Y-m-d H:i:s');
        $fivecurrentDate = date('Y-m-d H:i:s', strtotime("10 days", strtotime($currentDate)));
        $query = TournamentUserTicket::query();
        $query->from(app(TournamentUserTicket::class)->getTable() . " as tut");
        $query->leftJoin('tournament as t', 'tut.TOURNAMENT_ID', '=', 't.TOURNAMENT_ID');
        $query->where('tut.USER_ID', $userId);
        $query->where('tut.TICKET_STATUS', 2);
        $query->where('tut.GENERATED_TICK_MODE', 1);
        $query->whereBetween('t.TOURNAMENT_START_TIME', [$currentDate, $fivecurrentDate]);
        $currBalOnTour = $query->select(DB::RAW('IFNULL(SUM(t.BUYIN),0) AS TOT_TOUR_BUYIN'));
        $currBalOnTourResult = $currBalOnTour->first();
        $this->userActivitiesTracking($userId, "getCurrentBalanceOnTour", $currBalOnTourResult);
        return $currBalOnTourResult->TOT_TOUR_BUYIN;
    }

    // Function to update previous data in to withdrwal_criteria table
    protected function updatePreviousWithdrawalCriteria($userId, $withdrawalCriteriaId, $coinsMadeSinceLastDeposit)
    {
        $updateUserPoints = WithdrawalCriteria::where(['USER_ID' => $userId, 'WITHDRAWAL_CRITERIA_ID' => $withdrawalCriteriaId])
            ->update(['COINS_MADE_SINCE_LAST_DEPOSIT' => $coinsMadeSinceLastDeposit]);
    }

    // Function to insert data in to withdrwal_criteria table
    protected function insertIntoWithdrawalCriteria($userId, $balAtTheTimeOfDeposit, $depositAmount, $depositDate, $codeUsed, $codeCoins, $coinsMadeSinceLastDeposit, $amountToBeKept, $coinsToBeMade, $coinsStartDate, $comment)
    {
        $currentDate = date('Y-m-d H:i:s');
        $insertWCQuery = new WithdrawalCriteria();
        $insertWCQuery->USER_ID = $userId;
        $insertWCQuery->BAL_AT_DEPOSIT = $balAtTheTimeOfDeposit;
        $insertWCQuery->DEPOSIT_AMOUNT = $depositAmount;
        $insertWCQuery->DEPOSIT_DATE = $depositDate;
        $insertWCQuery->CODE_USED = $codeUsed;
        $insertWCQuery->CODE_COINS = $codeCoins;
        $insertWCQuery->COINS_MADE_SINCE_LAST_DEPOSIT = $coinsMadeSinceLastDeposit;
        $insertWCQuery->AMOUNT_TO_BE_KEPT = $amountToBeKept;
        $insertWCQuery->COINS_TO_BE_MADE = $coinsToBeMade;
        $insertWCQuery->COINS_START_DATE = $coinsStartDate;
        $insertWCQuery->COMMENTS = $comment;
        $insertWCQuery->CREATED_DATE = $currentDate;
        $insertWCQuery->UPDATED_DATE = $currentDate;
        $withdrawCriteriaRes = $insertWCQuery->save();
    }

    protected function firePaymentTrackingEvents($userId, $amount, $refNo, $promoCode, $paymentCount)
    {
        if (!empty($userId) || !empty($amount)) {
            $newRepeat = "";

            $trackingData = $this->getUserLatestLoginInfo($userId);

            $appType = $trackingData->APP_TYPE ?? "";
            $deviceId = $trackingData->DEVICE_PRIVATE_ID ?? "";
            $fingerprintId = $trackingData->FINGERPRINT_ID ?? "";

            if ($appType == 1) {
                $operatingSystem = "iOS";
            } elseif ($appType == 2) {
                $operatingSystem = "Android";
            } else {
                $operatingSystem = "OTHER";
            }

            if (!empty($paymentCount)) {
                if ($paymentCount > 1) {
                    $newRepeat = "Repeat";
                } else {
                    $newRepeat = "New";
                    $this->branchFTDEvent($userId, $amount, $refNo, $promoCode, $newRepeat, $operatingSystem, $deviceId, $fingerprintId);
                }
            }
            $this->branchCommerceEvent($userId, $amount, $refNo, $promoCode, $newRepeat, $operatingSystem, $deviceId, $fingerprintId);
            $this->firePaymentTrackingForGA($userId, $amount, $newRepeat);
        }
    }

    //branch curl method to track user successful FTD payment
    public function branchFTDEvent($userId, $amount, $refNo, $promoCode, $newRepeat, $operatingSystem, $deviceId, $fingerprintId)
    {

        if ($amount == NULL || $amount == '') {
            $amount = 0;
        } else {
            $amount = round($amount);
        }

        if ($newRepeat == NULL || $newRepeat == '') {
            $newRepeat = "Unknown";
        }

        if ($refNo == NULL || $refNo == '') {
            $refNo = "Unknown";
        }

        if ($promoCode == NULL || $promoCode == '') {
            $promoCode = "No Deposit Code";
        }


        $branchApiKey = config('rummy_config.branch.apiKey');
        $branchUrl = config('rummy_config.branch.standardEventUrl');
        $ch = curl_init($branchUrl);

        $userData = [
            "developer_identity" => "$userId",
            "country" => "IN",
            "language" => "en",
            "os" => "$operatingSystem"
        ];

        if ($operatingSystem == "Android") {
            $userData["aaid"] = $deviceId;
        } else if ($operatingSystem == "iOS") {
            $userData["idfa"] = $deviceId;
        } else { // Browser
            $userData["browser_fingerprint_id"] = $fingerprintId;
        }

        $data = [
            "name" => "ADD_TO_CART",
            "user_data" => $userData,
            "event_data" => [
                "transaction_id" => "$refNo",
                "currency" => "INR",
                "revenue" => "$amount"
            ],
            "custom_data" => [
                "deposit_code" => "$promoCode",
                "new_repeat" => "$newRepeat",
                "reference_no" => "$refNo",
                "deposit_amount" => "$amount",
                "deposit_currency" => "INR"
            ],
            "branch_key" => "$branchApiKey"
        ];

        $jsonData = json_encode($data);

        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json'));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $result = curl_exec($ch);
        $errors = curl_error($ch);
        $response = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        $action = "BRANCH_FTD_EVENT";
        $this->userActivitiesTracking($userId, $action, [
            'url' => $branchUrl,
            'content' => $jsonData,
            'response' => $response
        ]);
    }

    protected function branchCommerceEvent($userId, $amount, $refNo, $promoCode, $newRepeat, $operatingSystem, $deviceId, $fingerprintId)
    {

        if ($amount == NULL || $amount == '') {
            $amount = 0;
        } else {
            $amount = round($amount);
        }

        if ($newRepeat == NULL || $newRepeat == '') {
            $newRepeat = "Unknown";
        }

        if ($refNo == NULL || $refNo == '') {
            $refNo = "Unknown";
        }

        if ($promoCode == NULL || $promoCode == '') {
            $promoCode = "No Deposit Code";
        }


        $branchApiKey = config('rummy_config.branch.apiKey');
        $branchUrl = config('rummy_config.branch.standardEventUrl');

        $userData = [
            "developer_identity" => "$userId",
            "country" => "IN",
            "language" => "en",
            "os" => "$operatingSystem"
        ];

        if ($operatingSystem == "Android") {
            $userData["aaid"] = $deviceId;
        } else if ($operatingSystem == "iOS") {
            $userData["idfa"] = $deviceId;
        } else { // Browser
            $userData["browser_fingerprint_id"] = $fingerprintId;
        }

        $data = [
            "name" => "PURCHASE",
            "user_data" => $userData,
            "event_data" => [
                "transaction_id" => "$refNo",
                "currency" => "INR",
                "revenue" => "$amount"
            ],
            "custom_data" => [
                "deposit_code" => "$promoCode",
                "new_repeat" => "$newRepeat"
            ],
            "branch_key" => "$branchApiKey"
        ];

        $response = $this->ApiCallCurl([
            "url" => $branchUrl,
            "form_params" => $data,
            "headers" => [
                "Content-Type" => "application/json"
            ],
            "extra_curl_options_array" => [
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1
            ]
        ]);

        $trackingData = [
            'url' => $branchUrl,
            'content' => $data,
            'response' => $response
        ];

        $action = "BRANCH_COMMERCE_EVENT";


        $this->userActivitiesTracking($userId, $action, $trackingData);
    }

    protected function firePaymentTrackingForGA($user_id, $amount, $newRepeat)
    {
        $gaTrackingId = config('rummy_config.ga_setting.gaTrackingId');
        $gaCategoryDeposits = config('rummy_config.ga_setting.gaCategoryDeposits');
        $url = config('rummy_config.ga_setting.gaMeasurementProtocolUrl');

        if ($amount == NULL || $amount == '') {
            $amount = 0;
        }

        if ($newRepeat == NULL || $newRepeat == '') {
            $newRepeat = "Unknown";
        }

        $data = array();
        $data['v'] = "1";
        $data['tid'] = $gaTrackingId;
        $data['cid'] = $this->gen_uuid();
        $data['t'] = "event";
        $data['ec'] = $gaCategoryDeposits;
        $data['ea'] = $newRepeat;
        $data['el'] = $user_id;
        $data['ev'] = round($amount);

        $response = $this->ApiCallCurl([
            "url" => $url,
            "form_params" => $data,
            "headers" => [
                "Content-Type" => "application/x-www-form-urlencoded utf-8"
            ],
            "extra_curl_options_array" => [
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1
            ]
        ]);

        $trackingData = ['Url' => $url, 'content' => $data, 'response' => $response];
        $action = "GA_COMMERCE_EVENT";
        $this->userActivitiesTracking($user_id, $action, $data);
    }
    protected function getCurrentBalanceOnGamePoint($userId)
    {
        $result = DB::select(DB::raw("select INTERNAL_REFERENCE_NO, TRANSACTION_AMOUNT FROM master_transaction_history WHERE TRANSACTION_TYPE_ID in(31) AND BALANCE_TYPE_ID IN(1,3) AND TRANSACTION_STATUS_ID IN(201) AND INTERNAL_REFERENCE_NO  NOT IN(SELECT DISTINCT(INTERNAL_REFERENCE_NO) FROM master_transaction_history WHERE TRANSACTION_TYPE_ID=32 AND TRANSACTION_STATUS_ID=202) AND INTERNAL_REFERENCE_NO  NOT IN(SELECT DISTINCT(INTERNAL_REFERENCE_NO) FROM master_transaction_history WHERE TRANSACTION_TYPE_ID=13 AND TRANSACTION_STATUS_ID=108) AND transaction_date>= DATE_SUB(NOW(), INTERVAL 60 MINUTE) AND USER_ID = " . $userId . " ORDER BY transaction_date;"));
        $pointGameValue = 0;
        if (!empty($result)) {
            $reNoArray = array();
            $bal = 0;
            foreach ($result as $record) {
                $reNoArray[] = $record->INTERNAL_REFERENCE_NO;
                $bal = $bal + $record->TRANSACTION_AMOUNT;
            }

            $result2 = DB::select(DB::raw('select SUM(TRANSACTION_AMOUNT) AS POINT_GAME_VALUE FROM game_wallet_transaction WHERE BALANCE_TYPE_ID IN(1,3) AND TRANSACTION_TYPE_ID in(11) AND INTERNAL_REFERENCE_NO IN("' . implode('","', $reNoArray) . '") AND USER_ID = ' . $userId . ' ORDER BY GAME_WALLET_TRANSACTION_ID desc;'));
            $pointGameValue1 = 0;
            if (!empty($result2)) {
                $record2 = $result2[0];
                $newData['Query2'] = $record2->POINT_GAME_VALUE;
                $pointGameValue1 = $record2->POINT_GAME_VALUE;
            }

            $result3 = DB::select(DB::raw('select SUM(TRANSACTION_AMOUNT) AS POINT_GAME_VALUE FROM game_wallet_transaction WHERE BALANCE_TYPE_ID IN(1,3) AND TRANSACTION_TYPE_ID in(12) AND INTERNAL_REFERENCE_NO IN("' . implode('","', $reNoArray) . '") AND USER_ID = ' . $userId . ' ORDER BY GAME_WALLET_TRANSACTION_ID desc;'));
            $pointGameValue2 = 0;
            if (!empty($result3)) {
                $record3 = $result3[0];
                $newData['Query3'] = $record3->POINT_GAME_VALUE;
                $pointGameValue2 = $record3->POINT_GAME_VALUE;
            }

            $pointGameValue3 = $bal - $pointGameValue1;
            $pointGameValue = $pointGameValue3 + $pointGameValue2;
            $newData['reNoArray'] = $reNoArray;
            $newData['Query1'] = $bal;
            $newData['pointGameValue3'] = $pointGameValue3;
            $newData['pointGameValue'] = $pointGameValue;
            $this->userActivitiesTracking($userId, "getCurrentBalanceOnGame", $newData);
        }
        return $pointGameValue;
    }

    protected function getCurrentBalanceOnGamePool($userId)
    {
        $result = DB::select(DB::raw('select INTERNAL_REFERENCE_NO FROM master_transaction_history WHERE TRANSACTION_TYPE_ID IN(11) AND BALANCE_TYPE_ID IN(1,3) AND INTERNAL_REFERENCE_NO  NOT IN(SELECT DISTINCT(INTERNAL_REFERENCE_NO) FROM master_transaction_history WHERE TRANSACTION_TYPE_ID=13)  AND transaction_date>= DATE_SUB(NOW(), INTERVAL 60 MINUTE) AND USER_ID = ' . $userId . '  ORDER BY transaction_date;'));
        $poolGameValue = 0;
        if (!empty($result)) {
            $reNoArray = array();

            foreach ($result as $record) {
                $reNoArray[] = $record->INTERNAL_REFERENCE_NO;
            }

            $reNoArray2 = array();
            foreach ($reNoArray as $notInMasterInternal) {
                $result2 = DB::select(DB::raw('select DISTINCT INTERNAL_REFERENCE_NO FROM rummy_play WHERE INTERNAL_REFERENCE_NO IN("' . $notInMasterInternal . '") AND USER_ID = ' . $userId . ' ORDER BY INTERNAL_REFERENCE_NO desc;'));
                if (!empty($result2)) {
                    $poolGameCount = count($result2);
                    if ($poolGameCount == 0) {
                        $reNoArray2[] = $notInMasterInternal;
                    }
                }

                $result3 = DB::select(DB::raw('select SUM(TRANSACTION_AMOUNT) AS poolGameValue FROM master_transaction_history WHERE INTERNAL_REFERENCE_NO IN("' . implode('","', $reNoArray2) . '") AND TRANSACTION_TYPE_ID IN(11) AND BALANCE_TYPE_ID IN(1,3) AND transaction_date>= DATE_SUB(NOW(), INTERVAL 60 MINUTE) AND USER_ID = ' . $userId . ' ORDER BY transaction_date'));
                \Log::info('select SUM(TRANSACTION_AMOUNT) AS poolGameValue FROM master_transaction_history WHERE INTERNAL_REFERENCE_NO IN("' . implode('","', $reNoArray2) . '") AND TRANSACTION_TYPE_ID IN(11) AND BALANCE_TYPE_ID IN(1,3) AND transaction_date>= DATE_SUB(NOW(), INTERVAL 60 MINUTE) AND USER_ID = ' . $userId . ' ORDER BY transaction_date');
                if (!empty($result3)) {
                    $record3 = $result3[0];
                    $poolGameValue = $record3->poolGameValue;
                }
                $newData['reNoArray'] = $reNoArray;
                $newData['reNoArray2'] = $reNoArray2;
                $newData['poolGameValue'] = $poolGameValue;
                $this->userActivitiesTracking($userId, "getCurrentBalanceOnGamePool", $newData);
            }
        }
        return $poolGameValue;
    }

    protected function getUserLatestLoginInfo($userId)
    {
        $trackingDetail = Tracking::select('USER_ID', 'APP_TYPE', 'DEVICE_PRIVATE_ID', 'FINGERPRINT_ID')
            ->where(['USER_ID' => $userId])
            ->orderBy('TRACKING_ID', 'DESC')
            ->first();
        return $trackingDetail;
    }

    protected function aadharVerificationStatus($userId)
    {
        $kycStatus = UserKycDetail::where([
            ['USER_ID', $userId],
            ['DOCUMENT_TYPE', 1],
            ['DOCUMENT_SUB_TYPE', 1],
            ['DOCUMENT_STATUS', 1],
            ['APPROVED_BY', "SYSTEM"]
        ])->first();
        if ($kycStatus) {
            return true;
        } else {
            return false;
        }
    }
    protected function getCurrentBalancePoker($userId)
    {
        $result = DB::select(DB::raw('select INTERNAL_REFERENCE_NO FROM vendor_wallet_transfer WHERE TRANSACTION_TYPE_ID IN (174) AND JSON_EXTRACT(TRANSACTION_AMOUNT_BREAKUP, "$.balanceType") in ("[1]","[3]","[1, 3]") AND VENDOR_ID=1 AND INTERNAL_REFERENCE_NO  NOT IN (SELECT DISTINCT(INTERNAL_REFERENCE_NO) FROM vendor_wallet_transfer WHERE TRANSACTION_TYPE_ID=175 and USER_ID=' . $userId . ') AND transaction_date>= DATE_SUB(NOW(), INTERVAL 60 MINUTE) AND USER_ID = ' . $userId . '  ORDER BY transaction_date;'));
        $poolGameValue = 0;
        if (!empty($result)) {
            $reNoArray = array();

            foreach ($result as $record) {
                $reNoArray[] = $record->INTERNAL_REFERENCE_NO;
            }
            $result2 = DB::select(DB::raw('select SUM(TRANSACTION_AMOUNT) AS poolGameValue FROM master_transaction_history WHERE INTERNAL_REFERENCE_NO IN("' . implode('","', $reNoArray) . '") AND TRANSACTION_TYPE_ID IN (174) AND BALANCE_TYPE_ID IN(1,3) AND USER_ID = ' . $userId . ' ORDER BY INTERNAL_REFERENCE_NO desc'));
            if (!empty($result2)) {
                $record2 = $result2[0];
                $poolGameValue = $record2->poolGameValue;
            }
            $newData['reNoArray'] = $reNoArray;
            $newData['poolGameValue'] = $poolGameValue;
            $this->userActivitiesTracking($userId, "getCurrentBalancePoker", $newData);
        }
        return $poolGameValue;
    }

    protected function getCurrentBalancePokerTour($userId)
    {
        $result = DB::select(DB::raw('select INTERNAL_REFERENCE_NO FROM vendor_wallet_transfer WHERE TRANSACTION_TYPE_ID IN (176,177) AND JSON_EXTRACT(TRANSACTION_AMOUNT_BREAKUP, "$.balanceType") in ("[1]","[3]","[1, 3]") AND VENDOR_ID=1 AND INTERNAL_REFERENCE_NO  NOT IN (SELECT DISTINCT(INTERNAL_REFERENCE_NO) FROM vendor_wallet_transfer WHERE TRANSACTION_TYPE_ID=185 and USER_ID=' . $userId . ') AND transaction_date>= DATE_SUB(NOW(), INTERVAL 60 MINUTE) AND USER_ID = ' . $userId . '  ORDER BY transaction_date'));
        $poolGameValue = 0;
        if (!empty($result)) {
            $reNoArray = array();

            foreach ($result as $record) {
                $reNoArray[] = $record->INTERNAL_REFERENCE_NO;
            }
            $result2 = DB::table('vendor_wallet_transfer')
                ->select(DB::raw('SUM(TRANSACTION_AMOUNT) as poolGameValue'))
                ->whereIn('INTERNAL_REFERENCE_NO', $reNoArray)
                ->whereRaw("FROM_UNIXTIME(TRANSACTION_OTHER_DATA->>'$.tournamentStartTime' * 0.001) >= NOW()")
                ->where('USER_ID', $userId)
                ->orderBy('transaction_date', 'asc')->get();


            // DB::select(DB::raw("select SUM(TRANSACTION_AMOUNT) as poolGameValue from vendor_wallet_transfer where INTERNAL_REFERENCE_NO in ('".implode("','", $reNoArray)."') AND FROM_UNIXTIME(TRANSACTION_OTHER_DATA->>'$.tournamentStartTime' * 0.001) >= NOW() and USER_ID = $userId order by transaction_date"));
            if (!empty($result2)) {
                $record2 = $result2[0];
                $poolGameValue = $record2->poolGameValue;
            }
        }
        return $poolGameValue;
    }

    protected function reconcilePlayerLedger(Int $userId, String $action, String $refNo = null, Bool $shouldPerformDBTxn = true){
        try{
            $globalConfig = GlobalConfig::whereIn('CONFIG_KEY', [
                'ledger_reset_from_date',
                'tds_ten_k_exemption'
            ])->get();
    
            // Only Reset Entries Starting after below date
            $ledgerResetFromDate = $globalConfig->where('CONFIG_KEY', 'ledger_reset_from_date')->pluck('CONFIG_VALUE')->first();
            $ledgerResetFromDate = $ledgerResetFromDate ? Carbon::parse($ledgerResetFromDate)->format('Y-m-d H:i:s') : 
                                    (date('m') <= 3 ? Carbon::now()->sub(1, 'years')->format('Y-04-01 00:00:00') : Carbon::now()->format('Y-04-01 00:00:00') );
            // End
    
    
            $tdsTenKExemptionAllowed = $globalConfig->where('CONFIG_KEY', 'tds_ten_k_exemption')->pluck('CONFIG_VALUE')->first() == 'true';
            $tdsTenKExemptionAmount = $tdsTenKExemptionAllowed ? config('poker_config.newTenKExemption') : 0;
            
            if(in_array($action, ['DEPOSIT', 'WITHDRAWAL']) && !empty($refNo)){
                $transactionRecord = PaymentTransaction::where([
                    'INTERNAL_REFERENCE_NO' =>  $refNo,
                    'USER_ID' => $userId
                ])->where('UPDATED_DATE', '>', $ledgerResetFromDate)
                ->first();
            
                $isRefAlreadyExists = PlayerLedger::where([
                'INTERNAL_REFERENCE_NO' => $refNo,
                'USER_ID' => $userId
                ])->first();
                
                if(!empty($transactionRecord) && empty($isRefAlreadyExists)){
                    
                    $previousLedgerEntry = PlayerLedger::where('USER_ID', $userId)->where('PAYMENT_TRANSACTION_CREATED_ON', '>=', $ledgerResetFromDate)->orderByDesc('PLAYER_LEDGER_ID')->first();
      
                    if(empty($previousLedgerEntry)){
                        $previousLedgerEntry = (Object)[
                        'TOTAL_DEPOSITS' => 0,
                        'TOTAL_WITHDRAWALS' => 0,
                        'TOTAL_TAXABLE_WITHDRAWALS' => 0,
                        'ELIGIBLE_WITHDRAWAL_WITHOUT_TAX' => 0,
                        'TOTAL_ELIGIBLE_WITHDRAWAL_WITHOUT_TAX' => 0,
                        'EXEMPTION_10K' => $tdsTenKExemptionAmount
                        ];
                    }
                    
                    if($action == 'DEPOSIT'){
                
                        $totalDeposits = ($previousLedgerEntry->TOTAL_DEPOSITS + $transactionRecord->PAYMENT_TRANSACTION_AMOUNT);
                        $eligibleWithdrawalWithoutTax = ($previousLedgerEntry->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX + $transactionRecord->PAYMENT_TRANSACTION_AMOUNT);
                        $totalEligibleWithdrawalWithoutTax = ($eligibleWithdrawalWithoutTax + $previousLedgerEntry->EXEMPTION_10K);
            
                        $insertDataArray = [
                        'USER_ID' => $userId,
                        'ACTION' => 'DEPOSIT',
                        'TRANSACTION_AMOUNT' => $transactionRecord->PAYMENT_TRANSACTION_AMOUNT,
                        'INTERNAL_REFERENCE_NO' => $transactionRecord->INTERNAL_REFERENCE_NO,
                        'TOTAL_DEPOSITS' => $totalDeposits,
                        'TOTAL_WITHDRAWALS' => $previousLedgerEntry->TOTAL_WITHDRAWALS,
                        'TOTAL_TAXABLE_WITHDRAWALS' => $previousLedgerEntry->TOTAL_TAXABLE_WITHDRAWALS,
                        'ELIGIBLE_WITHDRAWAL_WITHOUT_TAX' => $eligibleWithdrawalWithoutTax,
                        'EXEMPTION_10K' => $previousLedgerEntry->EXEMPTION_10K,
                        'TOTAL_ELIGIBLE_WITHDRAWAL_WITHOUT_TAX' => $totalEligibleWithdrawalWithoutTax,
                        'PAYMENT_TRANSACTION_CREATED_ON' => $transactionRecord->PAYMENT_TRANSACTION_CREATED_ON,
                        'CREATED_DATE' => date('Y-m-d H:i:s'),
                        'UPDATED_DATE' => date('Y-m-d H:i:s')
                        ];
            
                        PlayerLedger::create($insertDataArray);
            
                        return [
                        'status' => 200,
                        'message' => 'Ledger Successfully Updated Against Deposit.'
                        ];
                    
                    } elseif($action == 'WITHDRAWAL'){
                
                        $totalWithdrawals = ($previousLedgerEntry->TOTAL_WITHDRAWALS + $transactionRecord->PAYMENT_TRANSACTION_AMOUNT);
                        $totalTaxableWithdrawals = $previousLedgerEntry->TOTAL_TAXABLE_WITHDRAWALS;
                        
                        $newTotalEligibleWithdrawalWithoutTax = $previousLedgerEntry->TOTAL_ELIGIBLE_WITHDRAWAL_WITHOUT_TAX;
                        $newTenKExemption = $previousLedgerEntry->EXEMPTION_10K;
                        $newEligibleWithdrawalWithoutTax = $previousLedgerEntry->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX;
        
                        if($transactionRecord->PAYMENT_TRANSACTION_AMOUNT > $newTotalEligibleWithdrawalWithoutTax){
                        
                        $totalTaxableWithdrawals += ($transactionRecord->PAYMENT_TRANSACTION_AMOUNT - $newEligibleWithdrawalWithoutTax);
                        
                        $newTotalEligibleWithdrawalWithoutTax = 0;
                        $newTenKExemption = 0;
                        $newEligibleWithdrawalWithoutTax = 0;
                        
                        
                        }else{
                        
                        if($newEligibleWithdrawalWithoutTax > $transactionRecord->PAYMENT_TRANSACTION_AMOUNT){
                        
                            $newEligibleWithdrawalWithoutTax -= $transactionRecord->PAYMENT_TRANSACTION_AMOUNT;
                        
                        }else{
                        
                            $totalTaxableWithdrawals += ($transactionRecord->PAYMENT_TRANSACTION_AMOUNT - $newEligibleWithdrawalWithoutTax);
                            
                            $newTenKExemption -= ($transactionRecord->PAYMENT_TRANSACTION_AMOUNT - $newEligibleWithdrawalWithoutTax);
                            $newEligibleWithdrawalWithoutTax = 0;
                        
                        }
            
                        $newTotalEligibleWithdrawalWithoutTax = $newTenKExemption + $newEligibleWithdrawalWithoutTax;
                        
                        }
                    
                        // ------ Handling Negative values (if any)----
                        if($newTotalEligibleWithdrawalWithoutTax < 0){
                        $newTotalEligibleWithdrawalWithoutTax = 0;
                        $newTenKExemption = 0;
                        $newEligibleWithdrawalWithoutTax = 0;
                        }
                        
                        if($newEligibleWithdrawalWithoutTax < 0){
                        $newEligibleWithdrawalWithoutTax = 0;
                        }
            
                        if($newTenKExemption < 0){
                        $newTenKExemption = 0;
                        }
                        // ---- END -----
            
                        $insertDataArray = [
                            'USER_ID' => $userId,
                            'ACTION' => 'WITHDRAWAL',
                            'TRANSACTION_AMOUNT' => $transactionRecord->PAYMENT_TRANSACTION_AMOUNT,
                            'INTERNAL_REFERENCE_NO' => $transactionRecord->INTERNAL_REFERENCE_NO,
                            'TOTAL_DEPOSITS' => $previousLedgerEntry->TOTAL_DEPOSITS,
                            'TOTAL_WITHDRAWALS' => $totalWithdrawals,
                            'TOTAL_TAXABLE_WITHDRAWALS' => $totalTaxableWithdrawals,
                            'ELIGIBLE_WITHDRAWAL_WITHOUT_TAX' => $newEligibleWithdrawalWithoutTax,
                            'EXEMPTION_10K' => $newTenKExemption,
                            'TOTAL_ELIGIBLE_WITHDRAWAL_WITHOUT_TAX' => $newTotalEligibleWithdrawalWithoutTax,
                            'PAYMENT_TRANSACTION_CREATED_ON' => $transactionRecord->PAYMENT_TRANSACTION_CREATED_ON,
                            'CREATED_DATE' => date('Y-m-d H:i:s'),
                            'UPDATED_DATE' => date('Y-m-d H:i:s')
                        ];
        
                        PlayerLedger::create($insertDataArray);
            
                        return [
                        'status' => 200,
                        'message' => 'Ledger Successfully Updated Against Withdrawal.'
                        ];
                    }
                }
            } elseif($action == 'RESET_LEDGER') {
                $fyStartDate = $ledgerResetFromDate;
                $fyEndDate = Carbon::now()->format('Y-m-d H:i:s');

            
                // Query fetching records
                $allSuccessDepositAndWithdrawTransactions = DB::table('payment_transaction AS PT')
                ->select(
                'PT.TRANSACTION_TYPE_ID',
                'PT.PAYMENT_TRANSACTION_STATUS',
                'PT.PAYMENT_TRANSACTION_CREATED_ON',
                'PT.PAYMENT_TRANSACTION_AMOUNT',
                'PT.INTERNAL_REFERENCE_NO',
                'WTH.WITHDRAW_TDS',
                'PT.UPDATED_DATE'
                )
                ->leftJoin('withdraw_transaction_history AS WTH', 'PT.INTERNAL_REFERENCE_NO', 'WTH.INTERNAL_REFERENCE_NO')
                ->whereIn('PT.PAYMENT_TRANSACTION_STATUS', [
                    125, // deposit success from BO
                    103, // deposit success from API Webhook
                    208, // withdraw success
                    255 // this status will be fecthed to just check any withdrawal transaction should  not be initiated 
                ])
                ->whereIn('PT.TRANSACTION_TYPE_ID', [
                    10, // withdrawal
                    8, // deposit from API 
                    111 // deposit from BO
                ])
                ->where('PT.USER_ID', $userId)
                ->whereBetween('PT.UPDATED_DATE', [
                    $fyStartDate,
                    $fyEndDate
                ])
                ->oldest('PT.UPDATED_DATE')
                ->get();
                // End

                $areThereAnyInitiatedWithdraw = $allSuccessDepositAndWithdrawTransactions->whereIn('PAYMENT_TRANSACTION_STATUS', [
                    255
                ])->count() > 0;

                if($areThereAnyInitiatedWithdraw){
                    return [
                        'status' => 422,
                        'message' => 'There are withdrawals transactions in initiated status. Kindly process them first.'
                    ];
                }

                $allSuccessAffiliateTransactions = DB::table('master_transaction_history')->select(
                'TRANSACTION_TYPE_ID',
                'TRANSACTION_STATUS_ID AS PAYMENT_TRANSACTION_STATUS',
                'TRANSACTION_DATE AS PAYMENT_TRANSACTION_CREATED_ON',
                'TRANSACTION_AMOUNT AS PAYMENT_TRANSACTION_AMOUNT',
                'INTERNAL_REFERENCE_NO',
                DB::raw('NULL AS WITHDRAW_TDS'),
                'UPDATED_DATE'
                )->where([
                'USER_ID' => $userId, 
                'TRANSACTION_TYPE_ID' => 86 ,// affiliate transfer,
                'TRANSACTION_STATUS_ID' => 102, // win ok
                'BALANCE_TYPE_ID' => 1
                ])->whereBetween('UPDATED_DATE', [
                $fyStartDate,
                $fyEndDate
                ])
                ->oldest('UPDATED_DATE')
                ->get();

                $allSuccessDepositAndWithdrawTransactions = array_merge($allSuccessDepositAndWithdrawTransactions->toArray(), $allSuccessAffiliateTransactions->toArray());
                $allSuccessDepositAndWithdrawTransactions = collect($allSuccessDepositAndWithdrawTransactions)->sortBy('UPDATED_DATE');

                try{
                    if($shouldPerformDBTxn){
                        DB::beginTransaction();
                    }    
                    
                    $insertDataArray = [];
                    
                    // below vars will hold previous and new value based on the iteration
                    $totalDeposits = 0;
                    $totalWithdrawals = 0;
                    $totalTaxableWithdrawals = 0;
                    $newEligibleWithdrawalWithoutTax = 0;
                    $newTenKExemption = 0;
                    $newTotalEligibleWithdrawalWithoutTax = 0;
                    $previousValueOfTotalTaxableWithdrawals = 0;
                    // end

                    $ledgerStartEntry = PlayerLedger::where('USER_ID', $userId)->where('PAYMENT_TRANSACTION_CREATED_ON', '>=', $fyStartDate)->whereIn('ACTION', ['LEDGER_START', 'LEDGER_RESET'])->latest('PLAYER_LEDGER_ID')->first();
                    
                    foreach($allSuccessDepositAndWithdrawTransactions as $index => $trans){
                        
                        $isWithdrawTrans = $trans->TRANSACTION_TYPE_ID == 10;
                        
                        if($index == '0' && !empty($ledgerStartEntry)){
                        
                            $totalDeposits = $isWithdrawTrans ? 0 : $trans->PAYMENT_TRANSACTION_AMOUNT;
                            $totalWithdrawals = $isWithdrawTrans ? $trans->PAYMENT_TRANSACTION_AMOUNT : 0;
                            $totalTaxableWithdrawals = $isWithdrawTrans ? (($trans->WITHDRAW_TDS / 30) * 100) : 0;

                            if($isWithdrawTrans){
                                if($trans->WITHDRAW_TDS > 0 ){
                        
                                $nonTaxableAmount = round($trans->PAYMENT_TRANSACTION_AMOUNT - $totalTaxableWithdrawals, 2);
                                
                                if($nonTaxableAmount <= ($ledgerStartEntry->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX + $ledgerStartEntry->EXEMPTION_10K)){
                                    
                                    if($nonTaxableAmount >= $ledgerStartEntry->EXEMPTION_10K){
                                    $newTenKExemption = 0;

                                    $nonTaxableAmount = $nonTaxableAmount - $ledgerStartEntry->EXEMPTION_10K;
                                    
                                    if($nonTaxableAmount >= $ledgerStartEntry->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX){
                                        $newEligibleWithdrawalWithoutTax = 0;
                                    }else{
                                        $newEligibleWithdrawalWithoutTax = $ledgerStartEntry->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX - $nonTaxableAmount;
                            
                                    }

                                    }else{
                                    $newTenKExemption = $ledgerStartEntry->EXEMPTION_10K - $nonTaxableAmount;
                                    $newEligibleWithdrawalWithoutTax = $ledgerStartEntry->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX;
                                    }
                                                    
                                }elseif($nonTaxableAmount > $ledgerStartEntry->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX){
                                    $newEligibleWithdrawalWithoutTax = 0;

                                }else{
                                    $newEligibleWithdrawalWithoutTax = $ledgerStartEntry->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX ;
                                    $newTenKExemption = $ledgerStartEntry->EXEMPTION_10K;
                                    $newTotalEligibleWithdrawalWithoutTax = ($newEligibleWithdrawalWithoutTax + $newTenKExemption);
                                }
                                }else{
                                    if($trans->PAYMENT_TRANSACTION_AMOUNT <= ($ledgerStartEntry->EXEMPTION_10K + $ledgerStartEntry->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX)){
                                        
                                        if($ledgerStartEntry->EXEMPTION_10K >= $trans->PAYMENT_TRANSACTION_AMOUNT){
                                        $newTenKExemption = $ledgerStartEntry->EXEMPTION_10K - $trans->PAYMENT_TRANSACTION_AMOUNT;
                                        $newEligibleWithdrawalWithoutTax = $ledgerStartEntry->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX;
                                        }else{
                                        if($trans->PAYMENT_TRANSACTION_AMOUNT >= $ledgerStartEntry->EXEMPTION_10K ){
                                            $newTenKExemption = 0;
                                            $needToBeDeductedFromExemp = $trans->PAYMENT_TRANSACTION_AMOUNT - $ledgerStartEntry->EXEMPTION_10K;
                                            if($needToBeDeductedFromExemp >= $ledgerStartEntry->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX){
                                            $newEligibleWithdrawalWithoutTax = 0;
                                            }else{
                                            $newEligibleWithdrawalWithoutTax = $ledgerStartEntry->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX - $needToBeDeductedFromExemp;
                                            }
                                        }
                                        }
                                    }else{
                                        $newTenKExemption = 0;
                                        $newEligibleWithdrawalWithoutTax = 0;
                                    }
                                }
                            }else{
                                $newEligibleWithdrawalWithoutTax = ($ledgerStartEntry->ELIGIBLE_WITHDRAWAL_WITHOUT_TAX +  $trans->PAYMENT_TRANSACTION_AMOUNT);
                                $newTenKExemption = $ledgerStartEntry->EXEMPTION_10K;
                            }

                            if($newEligibleWithdrawalWithoutTax < 0){
                                $newEligibleWithdrawalWithoutTax = 0;
                            }

                            if($newTenKExemption < 0){
                                $newTenKExemption = 0;
                            }
                            
                            $newTotalEligibleWithdrawalWithoutTax = ($newEligibleWithdrawalWithoutTax + $newTenKExemption);

                        }elseif($index == '0'){ // setting values for first entry
                            $totalDeposits = $isWithdrawTrans ? 0 : $trans->PAYMENT_TRANSACTION_AMOUNT;
                            $totalWithdrawals = $isWithdrawTrans ? $trans->PAYMENT_TRANSACTION_AMOUNT : 0;
                            $totalTaxableWithdrawals = $isWithdrawTrans ? $trans->PAYMENT_TRANSACTION_AMOUNT : 0;
                            $newEligibleWithdrawalWithoutTax = $isWithdrawTrans ? 0 : $trans->PAYMENT_TRANSACTION_AMOUNT;
                            $newTenKExemption = $tdsTenKExemptionAmount;
                            $newTotalEligibleWithdrawalWithoutTax = ($newEligibleWithdrawalWithoutTax + $newTenKExemption);

                        }else{
                            $totalDeposits += $isWithdrawTrans ? 0 : $trans->PAYMENT_TRANSACTION_AMOUNT;
                            $totalWithdrawals += $isWithdrawTrans ? $trans->PAYMENT_TRANSACTION_AMOUNT : 0;
                            $oldTenkExemption = $newTenKExemption;
                        
                            if($isWithdrawTrans){
                                // The Withdraw trans case 
                                if($trans->PAYMENT_TRANSACTION_AMOUNT > $newTotalEligibleWithdrawalWithoutTax && !($trans->WITHDRAW_TDS > 0)){
                                    $previousValueOfTotalTaxableWithdrawals = $totalTaxableWithdrawals;
                                    $totalTaxableWithdrawals += ($trans->PAYMENT_TRANSACTION_AMOUNT - $newEligibleWithdrawalWithoutTax);
                                    
                                    $newTotalEligibleWithdrawalWithoutTax = 0;
                                    $newTenKExemption = 0;
                                    $newEligibleWithdrawalWithoutTax = 0;
                                
                                
                                }else{
                                
                                    if($newEligibleWithdrawalWithoutTax > $trans->PAYMENT_TRANSACTION_AMOUNT){
                                    
                                        $newEligibleWithdrawalWithoutTax -= $trans->PAYMENT_TRANSACTION_AMOUNT;
                                        $previousValueOfTotalTaxableWithdrawals = $totalTaxableWithdrawals;
                                    
                                    }else{
                                        $previousValueOfTotalTaxableWithdrawals = $totalTaxableWithdrawals;
                                        $totalTaxableWithdrawals += ($trans->PAYMENT_TRANSACTION_AMOUNT - $newEligibleWithdrawalWithoutTax);
                                        
                                        $newTenKExemption -= ($trans->PAYMENT_TRANSACTION_AMOUNT - $newEligibleWithdrawalWithoutTax);
                                        $newEligibleWithdrawalWithoutTax = 0;
                                    }                
                                }
                                
                                // ------ Handling Negative values (if any)----
                                if($newTotalEligibleWithdrawalWithoutTax < 0){
                                $newTotalEligibleWithdrawalWithoutTax = 0;
                                $newTenKExemption = 0;
                                $newEligibleWithdrawalWithoutTax = 0;
                                }
                                
                                if($newEligibleWithdrawalWithoutTax < 0){
                                $newEligibleWithdrawalWithoutTax = 0;
                                }
                    
                                if($newTenKExemption < 0){
                                $newTenKExemption = 0;
                                }
                                // ---- END -----

                                /*---- This logic is a fix for success then reverse withdrawal txn case ---
                                * It will add deducted execemption back to the execemption limit
                                */
                                if($trans->WITHDRAW_TDS > 0 && $newTotalEligibleWithdrawalWithoutTax > 0){
                                // Incorrect deduction case
                                $amountFromTaxPaidRevCal = ( $trans->WITHDRAW_TDS * (100/ 30) ); 
                                $amountFromTaxPaidRevCal = number_format($amountFromTaxPaidRevCal,2,'.','');
                                
                                $expectedAmountShouldBeDeducted = (number_format($trans->PAYMENT_TRANSACTION_AMOUNT,2,'.','')  -  $amountFromTaxPaidRevCal); 
                                $expectedAmountShouldBeDeducted = number_format($expectedAmountShouldBeDeducted,2,'.','');
                                
                                $amountShouldBeReturned = (number_format($newTotalEligibleWithdrawalWithoutTax,2,'.','') - $expectedAmountShouldBeDeducted);
                                $amountNeedToBeAddedIn10k = 0;
                                if($amountShouldBeReturned  > 0){ 
                                    
                                    if($oldTenkExemption > $newTenKExemption){
                                        
                                        $amountNeedToBeAddedIn10k =   $oldTenkExemption - $newTenKExemption  ;
                                        
                                        if($amountNeedToBeAddedIn10k > $amountShouldBeReturned){
                                            $amountNeedToBeAddedIn10k = $amountShouldBeReturned;
                                            
                                        }

                                        $newTenKExemption +=  $amountNeedToBeAddedIn10k;

                                        $newEligibleWithdrawalWithoutTax = ($amountShouldBeReturned - $amountNeedToBeAddedIn10k); 
                                        $newEligibleWithdrawalWithoutTax = $newEligibleWithdrawalWithoutTax < 0 ? 0 : $newEligibleWithdrawalWithoutTax;                      
                                    }else{
                                        
                                        $newEligibleWithdrawalWithoutTax = ($amountShouldBeReturned - $newTenKExemption);
                                    }
                                }
                                $totalTaxableWithdrawals = $previousValueOfTotalTaxableWithdrawals;
                                $totalTaxableWithdrawals += (($trans->PAYMENT_TRANSACTION_AMOUNT - $expectedAmountShouldBeDeducted)+$amountNeedToBeAddedIn10k);
                                }
                            }else{
                                // The deposit trans case
                                $newEligibleWithdrawalWithoutTax +=  $trans->PAYMENT_TRANSACTION_AMOUNT;
                            }
                            // End

                            $newTotalEligibleWithdrawalWithoutTax = $newEligibleWithdrawalWithoutTax + $newTenKExemption; // computing total here from all above cond...
                        }

                        array_push($insertDataArray, [
                        'USER_ID' => $userId,
                        'ACTION' => $isWithdrawTrans ? 'WITHDRAWAL' : ($trans->TRANSACTION_TYPE_ID == 86 ? 'AFFILIATE_TRANSFER' : 'DEPOSIT') ,
                        'TRANSACTION_AMOUNT' => $trans->PAYMENT_TRANSACTION_AMOUNT,
                        'INTERNAL_REFERENCE_NO' => $trans->INTERNAL_REFERENCE_NO,
                        'TOTAL_DEPOSITS' => $totalDeposits,
                        'TOTAL_WITHDRAWALS' => $totalWithdrawals,
                        'TOTAL_TAXABLE_WITHDRAWALS' => $totalTaxableWithdrawals,
                        'ELIGIBLE_WITHDRAWAL_WITHOUT_TAX' => $newEligibleWithdrawalWithoutTax,
                        'EXEMPTION_10K' => $newTenKExemption,
                        'TOTAL_ELIGIBLE_WITHDRAWAL_WITHOUT_TAX' => $newTotalEligibleWithdrawalWithoutTax,
                        'PAYMENT_TRANSACTION_CREATED_ON' => $trans->PAYMENT_TRANSACTION_CREATED_ON,
                        'CREATED_DATE' => date('Y-m-d H:i:s'),
                        'UPDATED_DATE' => date('Y-m-d H:i:s')
                        ]);

                    }
                    //dd($insertDataArray);

                    // Senity check :  if any  transaction added in between this process
                    $oldCount = $allSuccessDepositAndWithdrawTransactions->whereNotIn('PAYMENT_TRANSACTION_STATUS', [255, 102])->count();
                    
                    $newCount = DB::table('payment_transaction AS PT')->whereIn('PT.PAYMENT_TRANSACTION_STATUS', [
                        125, // deposit success from BO
                        103, // deposit success from API Webhook
                        208, // withdraw success
                    ])
                    ->whereIn('PT.TRANSACTION_TYPE_ID', [
                        10, // withdrawal
                        8, // deposit from API 
                        111 // deposit from BO
                    ])
                    ->where('PT.USER_ID', $userId)
                    ->whereBetween('PT.UPDATED_DATE', [
                        $fyStartDate,
                        $fyEndDate
                    ])
                    ->oldest('PT.UPDATED_DATE')
                    ->count();

                    if($newCount != $oldCount){
                        Log::error(['Ledger Reset : Transaction Intersect case happened.', $userId, date('Y-m-d H:i:s')]);
                        return [
                        'status' => 500,
                        'message' => 'Process Intersected.'
                        ];
                    }
                    // end

                    PlayerLedger::where('USER_ID', $userId)->whereNotIn('ACTION', ['LEDGER_START','LEDGER_RESET'])->delete();
                    PlayerLedger::insert($insertDataArray);
                    
                    if($shouldPerformDBTxn){
                        DB::commit();
                    }

                    return [
                        'status' => 200,
                        'message' => 'Ledger reset successfully.'
                    ];
                }catch(\Exception $e){
                    if($shouldPerformDBTxn){
                        DB::rollback();
                    }
                    
                    Log::error($e);
                    
                    return [
                        'status' => 500,
                        'message' => 'Something went wrong. At DB transaction level.'
                    ];
                }
            }else{
                return [
                'status' => 500,
                'message' => 'Unprocessable Entity (Params Missing or NULL)'
                ];
            }
        }catch(\Exception $e){
          Log::error($e);
    
          return [
            'status' => 500,
            'message' => 'Something went wrong.'
          ];
        }
        
    }
}
