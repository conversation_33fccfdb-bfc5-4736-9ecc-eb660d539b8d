<?php
namespace App\Exceptions;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Illuminate\Auth\AuthenticationException;
use InvalidArgumentException;
use App\Facades\RummyBaazi;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Bugsnag\BugsnagLaravel\Facades\Bugsnag;
trait ExceptionTrait
{
    public function apiException($request, $e)
    {
        try {
            $this->logging($e->getMessage() ?? $e);
            Log::error($e);
        } catch (\Exception $e) {
            $this->logging($e->getMessage());
        }

        /**
         * Log Handled Exception to Bugsnag
         */
        Bugsnag::notifyException($e);

        if ($e instanceof AuthenticationException) {
            return RummyBaazi::errorResponse(1,  [['code' => 401000, 'message' => "Unauthorized Access"]]);
        }
        if ($e instanceof ModelNotFoundException) {
            return RummyBaazi::errorResponse(1, [['code' => 404000, 'message' => "Model Not Found Exception"]]);
        }
        if ($e instanceof NotFoundHttpException) {
            return RummyBaazi::errorResponse(1, [['code' => 404000, 'message' => "Route Not Found Http Exception"]]);
        }
        if ($e instanceof InvalidArgumentException) {
            return RummyBaazi::errorResponse(1, [['code' => 500000, 'message' => "Invalid Argument Exception"]]);
        }
        if ($e instanceof MethodNotAllowedHttpException) {
            return RummyBaazi::errorResponse(1, [['code' => 404000, 'message' => "Method Not Allowed Http Exception"]]);
        }
        if ($e instanceof ValidationException) {
            return RummyBaazi::errorResponse(1, [['code' => 422000, 'message' => "Validation Failed"]]);
        }
        if ($e instanceof Exception) {
            return RummyBaazi::errorResponse(1, [['code' => 500000, 'message' => "Unknown Error"]]);
        }
        return parent::render($request, $e);
    }

    public function logging($data)
    {
        Log::info("Exception Traits: " . \json_encode($data));
    }
}
