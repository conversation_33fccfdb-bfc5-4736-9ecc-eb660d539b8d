<?php

namespace App\Mail;

use App\Models\DepositLimitUser;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Models\ResponsibleGamingRequest;

class CommonMail extends Mailable
{
    use Queueable, SerializesModels;

    private $common_content;
    private $template;
    private $userId;
    private $emailKey;
    private $mailArray;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($params)
    {
        $this->mailArray = $params;
        $this->common_content = null;
        $this->emailKey = $params['emailkey'];
        $this->userId = $params['userId'];
    }
    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        /* get email template from db  */
        $this->template = \RummyBaazi::getMailTemplate($this->emailKey);

        /* if template not found with given email_key */
        if (empty($this->template)) {
            return;
        }

        /* subject to send email */
        $subject = !empty($this->template->name) ? $this->template->name : "<PERSON>um<PERSON><PERSON><PERSON><PERSON>";

        /* setup mail content */
        $this->refactorMailView();

        return $this->from(config('rummy_config.mail.from.support'))->subject($subject)->view('mail.commonMail', ['template' => $this->template]);
    }

    private function refactorMailView()
    {
        $this->commonContent();
        method_exists($this, $this->emailKey) ? $this->{$this->emailKey}() : "";
    }

    private function commonContent()
    {
        $template = $this->template;
        $userName = $this->mailArray['username'];
        $mailContent = $template->description;
        $siteURL = config('rummy_config.cdn_url') . "/";
        $headerImgURL = $siteURL . "images/Home_logo.png";
        $downArrowImgURL = $siteURL . "images/dwnarw.png";
        $bottomLineImgURL = $siteURL . "images/email-bottom.png";
        $addCashImgURL = $siteURL . "images/add_cash.png";
        $downloadImgURL = $siteURL . "images/download_now.png";
        $playNowImgURL = $siteURL . "images/play_now.png";
        $socialFBImgURL = $siteURL . "images/facebook_icon_2.png";
        $socialTWEETImgURL = $siteURL . "images/twitter_icon_2.png";
        $socialYOUImgURL = $siteURL . "images/youtube_icon_2.png";
        $unlocked    = $siteURL . "images/mailer/unlocked.png";
        $locked        = $siteURL . "images/mailer/locked.png";
        $mailContent = str_replace('_HEADER_IMG_URL_', $headerImgURL, $mailContent);
        $mailContent = str_replace('_DOWN_ARROW_IMG_URL_', $downArrowImgURL, $mailContent);
        $mailContent = str_replace('_BOTTOM_LINE_URL_', $bottomLineImgURL, $mailContent);
        $mailContent = str_replace('_ADD_CASH_IMG_URL_', $addCashImgURL, $mailContent);
        $mailContent = str_replace('_DOWN_IMG_URL_', $downloadImgURL, $mailContent);
        $mailContent = str_replace('_PLAY_IMG_URL_', $playNowImgURL, $mailContent);
        $mailContent = str_replace('_SOCIAL_FB_ICON_', $socialFBImgURL, $mailContent);
        $mailContent = str_replace('_SOCIAL_TWEET_ICON_', $socialTWEETImgURL, $mailContent);
        $mailContent = str_replace('_SOCIAL_YOU_ICON_', $socialYOUImgURL, $mailContent);
        $mailContent = str_replace("_USER_NAME_", $userName, $mailContent);
        $mailContent = str_replace("_USERNAME_", $userName, $mailContent);
        $mailContent = str_replace('_UNLOCKED_', $unlocked, $mailContent);
        $mailContent = str_replace('_LOCKED_', $locked, $mailContent);

        $this->template = $mailContent;
    }

    private function commonWithdrawCashMailAllCases()
    {
        try {
            $temp = $this->template;
            $data = $this->mailArray;

            $temp = str_replace('_AMOUNT_', $data['amount'], $temp);
            $temp = str_replace('_WITHDRAW_REQUEST_TYPE_', $data['withdrawRequestType'], $temp);
            $temp = str_replace('_COMMON_DIV_', $data['commonDiv'], $temp);
            $this->template = $temp;
        } catch (Exception $exception) {
            Log::error($exception);
            return;
        }
    }

    private function commonWithdrawCommissionMailAllCases()
    {
        try {
            $temp = $this->template;
            $data = $this->mailArray;

            $temp = str_replace('_TRANSID_', $data['internalReferenceNumber'], $temp);
            $temp = str_replace('_AMOUNT_', $data['amount'], $temp);

            $this->template = $temp;
        } catch (Exception $exception) {
            Log::error($exception);
            return;
        }
    }

    private function commonDepositMail()
    {
        try {
            $temp = $this->template;
            $data = $this->mailArray;
            $temp = str_replace('_TRANSID_', $data['internalReferenceNumber'], $temp);
            $temp = str_replace('_AMOUNT_', $data['amount'], $temp);
            $temp = str_replace('_DATE_', date('jS M \'y', strtotime($data['transactionDate'])), $temp);
            $temp = str_replace('_TIME_', date('g:iA', strtotime($data['transactionDate'])), $temp);
            $temp = str_replace('_PAYMENTID_', $data['paymentId'], $temp);
            $temp = str_replace('_COMMON_DIV_', $data['commonDiv'], $temp);
            $this->template = $temp;
        } catch (Exception $exception) {
            Log::error($exception);
            return;
        }
    }

    private function withdrawal_reversal_approved()
    {
        return $this->commonWithdrawCashMailAllCases();
    }

    private function withdrawal_request_received()
    {
        return $this->commonWithdrawCashMailAllCases();
    }

    private function withdrawal_request_approved()
    {
        return $this->commonWithdrawCashMailAllCases();
    }

    private function withdrawal_request_rejected()
    {
        return $this->commonWithdrawCashMailAllCases();
    }

    private function commission_withdrawal_request_revert()
    {
        return $this->commonWithdrawCommissionMailAllCases();
    }

    private function commission_transfer_request_received()
    {
        return $this->commonWithdrawCommissionMailAllCases();
    }

    private function commission_withdrawal_request_received()
    {
        return $this->commonWithdrawCommissionMailAllCases();
    }

    private function deposit_successful()
    {
        return $this->commonDepositMail();
    }

    private function deposit_failed()
    {
        return $this->commonDepositMail();
    }

    private function tournament_register()
    {
        try {
            $temp = $this->template;
            $data = $this->mailArray;
            $temp = str_replace('_USER_NAME_', $data['username'], $temp);
            $temp = str_replace('_TOURNAMENTID_', $data['tournamentName'], $temp);
            $temp = str_replace('_BUYIN_', $data['buyIn'], $temp);
            $temp = str_replace('_ENTRYFEE_', $data['entryFee'], $temp);
            $temp = str_replace('_TSTARTDTIME_', $data['tournamentStart'], $temp);
            $temp = str_replace('_RSTARTDTIME_', $data['tournamentRegistrationStart'], $temp);
            $temp = str_replace('_RENDTIME_', $data['tournamentRegistrationEnd'], $temp);
            $this->template = $temp;
        } catch (Exception $exception) {
            Log::error($exception);
            return;
        }
    }

    private function refer_a_friend_bonus()
    {
        try {
            $temp = $this->template;
            $data = $this->mailArray;
            $temp = str_replace('_REFERRER_USERNME_', $data['referrer_username'], $temp);
            $temp = str_replace('_FULLNAME_', $data['full_name'], $temp);
            $temp = str_replace('_USERNAME_', $data['refferal_username'], $temp);
            $temp = str_replace('_CHIPS_', $data['chips'], $temp);
            $temp = str_replace('_CREDITEDDATE_', $data['credited_date'], $temp);
            $this->template = $temp;
        } catch (Exception $exception) {
            Log::error($exception);
            return;
        }
    }

    private function withdrawal_request_failed(){
        return $this->commonWithdrawCashMailAllCases();
    }

    private function voucher_redeem_success()
    {
        try {
            $temp = $this->template;
            $data = $this->mailArray;
            $temp = str_replace('_amount_', $data['amount'], $temp);

            $this->template = $temp;
        } catch (Exception $exception) {
            Log::error($exception);
            return;
        }
    }

    private function promo_bonus_received_on_deposit_success()
    {
        try {
            $temp = $this->template;
            $data = $this->mailArray;
            $temp = str_replace('_amount_', $data['bonusAmount'], $temp);
            $temp = str_replace('_codename_', $data['depositCode'], $temp);
            $this->template = $temp;
        } catch (Exception $exception) {
            Log::error($exception);
            return;
        }
    }
    private function tournament_registration_on_deposit_success()
    {
        try {
            $temp = $this->template;
            $data = $this->mailArray;
            $temp = str_replace('_tournamentname_', $data['tournamentName'], $temp);
            $temp = str_replace('_codename_', $data['depositCode'], $temp);
            $this->template = $temp;
        } catch (Exception $exception) {
            Log::error($exception);
            return;
        }
    }

}
