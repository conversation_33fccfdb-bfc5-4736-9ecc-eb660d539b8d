<?php

namespace App\Jobs;

use App\Traits\Models\CommonQueries;
use App\Http\Controllers\Payment\AddMoney\AddMoneyBaseController;
use App\Models\DepositGstInvoice;
use Illuminate\Support\Facades\Log;

class DepositInvoiceReconcileJob extends Job
{
    use CommonQueries;

    protected $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $invoiceData = $this->data['invoiceDataArr'];
        
        $isAlreadyProcessed = !empty(DepositGstInvoice::where('INTERNAL_REFERENCE_NO', $invoiceData['INTERNAL_REFERENCE_NO'])->first());

        if(!$isAlreadyProcessed){
            $newInoviceNo = (new AddMoneyBaseController())->generateDepositInvoiceNo($invoiceData['CREATED_DATE']);
            
            $invoiceData['INVOICE_NO'] = $newInoviceNo;
            Log::info($invoiceData);
            DepositGstInvoice::create($invoiceData);
        }
    }
}
