<?php

namespace App\Jobs;


use Illuminate\Support\Facades\Mail as Mail;

class CommonMailJob extends Job
{
    protected $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \extract($this->data);
        Mail::html($mailContent, function ($message) use ($subject, $emailId) {
            $message->from(config('rummy_config.mail.from.support'), 'RummyBaa<PERSON>');
            $message->to($emailId);
            $message->subject($subject);
        });
    }
}
