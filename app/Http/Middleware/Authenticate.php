<?php

namespace App\Http\Middleware;

use Closure;
use App\Facades\RummyBaazi;
use App\Models\User;
use Tymon\JWTAuth\Exceptions\TokenExpiredException;
use Ty<PERSON>\JWTAuth\Exceptions\TokenInvalidException;
use \Tymon\JWTAuth\Exceptions\JWTException;
use Tymon\JWTAuth\JWT;

class Authenticate
{
    /**
     * The authentication guard factory instance.
     *
     * @var \Tymon\JWTAuth\JWT
     */
    protected $jwt;

    /**
     * Create a new middleware instance.
     *
     * @param  \Tymon\JWTAuth\JWT  $auth
     * @return void
     */
    public function __construct(JWT $jwt)
    {
        $this->jwt = $jwt;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null)
    {
        dd($request->all());
        try {
            $token = $this->jwt->getToken();
            if (!$token) {
                return RummyBaazi::errorResponse(1, [['code' => 401005, 'message' => "Token not provided."]]);
            }

            $payload = $this->jwt->decode($token, true);
            if (!$payload) {
                return RummyBaazi::errorResponse(1, [['code' => 401006, 'message' => "An error while decoding token."]]);
            }
        } catch (TokenExpiredException $e) {
            return RummyBaazi::errorResponse(1, [['code' => 401002, 'message' => "Token expired."]]);
        } catch (TokenBlacklistedException $e) {
            return RummyBaazi::errorResponse(1, [['code' => 401008, 'message' => "The token has been blacklisted."]]);
        } catch (TokenInvalidException $e) {
            return RummyBaazi::errorResponse(1, [['code' => 401003, 'message' => "Invalid Token."]]);
        } catch (JWTException $e) {
            return RummyBaazi::errorResponse(1, [['code' => 401000, 'message' => "Unauthorized to access."]]);
        } catch (Exception $e) {
            return RummyBaazi::errorResponse(1, [['code' => 401006, 'message' => "An error while decoding token."]]);
        }

        // if ($user = User::where(["USER_ID" => $payload->get('sub'), "SESSION_ID" => $payload->get('jti')])->first()) {
        //     $request->auth = $user;
        // } else {
        //     return RummyBaazi::errorResponse(1, [['code' => 401007, 'message' => "Login in somewhere else."]]);
        // }

        return $next($request);
    }
}
