<?php

namespace App\Http\Middleware;

use Closure;
use App\Facades\RummyBaazi;
use App\Models\User;
use Tymon\JWTAuth\Exceptions\TokenExpiredException;
use Tymon\JWTAuth\Exceptions\TokenInvalidException;
use \Tymon\JWTAuth\Exceptions\JWTException;
use Tymon\JWTAuth\JWT;
use Auth;

class TempAuthenticate
{
    /**
     * The authentication guard factory instance.
     *
     * @var \Tymon\JWTAuth\JWT
     */
    protected $jwt;

    /**
     * Create a new middleware instance.
     *
     * @param  \Tymon\JWTAuth\JWT  $auth
     * @return void
     */
    public function __construct(JWT $jwt)
    {
        $this->jwt = $jwt;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null)
    {
        $token = $request->bearerToken();
        if (!$token) {
            return RummyBaazi::errorResponse(1, [['code' => 401005, 'message' => "Token not provided."]]);
        }

        if (!\is_numeric($token)) {
            return RummyBaazi::errorResponse(1, [['code' => 401003, 'message' => "Invalid Token."]]);
        }

        if (!($user = User::find($token))) {
            return RummyBaazi::errorResponse(1, [['code' => 401000, 'message' => "Unauthorized to access."]]);
        }

        Auth::login($user);

        return $next($request);
    }
}
