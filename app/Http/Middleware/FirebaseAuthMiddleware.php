<?php

namespace App\Http\Middleware;
use Kreait\Firebase\Auth as FirebaseAuth;
use Firebase\Auth\Token\Exception\ExpiredToken;
use Firebase\Auth\Token\Exception\InvalidSignature;
use Firebase\Auth\Token\Exception\InvalidToken;
use Firebase\Auth\Token\Exception\UnknownKey;
use InvalidArgumentException;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use App\Facades\RummyBaazi;
use Closure;

class FirebaseAuthMiddleware
{
    protected $auth;

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function __construct(FirebaseAuth $auth){
        $this->auth = $auth;
    }

    public function handle($request, Closure $next)
    {
        $token = $request->bearerToken();

        if(!$token){
            return RummyBaazi::errorResponse(1, [['code' => 401005, 'message' => "Token not provided."]]);
        }

        try {
            $verifiedIdToken = $this->auth->verifyIdToken($token);
        }catch(ExpiredToken $e){
            return RummyBaazi::errorResponse(1, [['code' => 401002, 'message' => "Token expired."]]);
        }catch (InvalidToken $e) {
            return RummyBaazi::errorResponse(1, [['code' => 401003, 'message' => "Invalid Token."]]);
        }catch (InvalidSignature $e){
            return RummyBaazi::errorResponse(1, [['code' => 401000, 'message' => "Unauthorized to access."]]);
        }catch (InvalidArgumentException $e) {
            return RummyBaazi::errorResponse(1, [['code' => 401006, 'message' => "An error while decoding token."]]);
        }catch(\Exception $e){
            return RummyBaazi::errorResponse(1, [['code' => 401006, 'message' => "An error while decoding token."]]);
        }

        return $next($request);
    }
}
