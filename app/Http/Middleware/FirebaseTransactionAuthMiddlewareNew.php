<?php

namespace App\Http\Middleware;

use Kreait\Firebase\Auth as FirebaseAuth;
use App\Traits\CommonTraits;
use App\Traits\FirebaseTransactionAuthTrait;
use Closure;

class FirebaseTransactionAuthMiddlewareNew
{
    use CommonTraits, FirebaseTransactionAuthTrait;

    protected $auth;

    /**
     * Create a new middleware instance.
     *
     * @param FirebaseAuth $auth
     */
    public function __construct(FirebaseAuth $auth)
    {
        $this->auth = $auth;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // Validate transaction authorization (banned state, KYC, time restrictions)
        $transactionValidation = $this->validateTransactionAuth($request);
        if ($transactionValidation !== null) {
            return $transactionValidation;
        }

        // Verify Firebase token
        $token = $request->bearerToken();
        $tokenValidation = $this->verifyFirebaseToken($token, $this->auth);
        if ($tokenValidation !== null) {
            return $tokenValidation;
        }

        return $next($request);
    }
}
