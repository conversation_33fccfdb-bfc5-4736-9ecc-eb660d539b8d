<?php

namespace App\Http\Resources\Ticket;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;
  
class UserTicketResource extends JsonResource
{
    public function toArray($request)
    { 

        $addMinutes = 0;

        if($this->tournament->LATE_REGISTRATION_ALLOW == 1){
            $addMinutes = $this->tournament->LATE_REGISTRATION_END_TIME;
        }

        return [
            'ticket_id' => $this->TOURNAMENT_USER_TICKET_ID,
            'source'    => $this->TICKET_SOURCE ?? '',
            'used'      => !empty($this->TICKET_STATUS) &&  $this->TICKET_STATUS == 2 ? true : false,
            'expire_on' => Carbon::parse($this->tournament->REGISTER_END_TIME)->addMinutes($addMinutes)->format('Y-m-d H:i:s'),
            'buy_in'    => !empty($this->BUYIN) ? true : false,
            // 'reward'    => '' //Todo
        ];
    }
}