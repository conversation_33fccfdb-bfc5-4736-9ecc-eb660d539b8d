<?php

namespace App\Http\Resources\Ticket;

use Illuminate\Http\Resources\Json\JsonResource;
  
class TournamentResource extends JsonResource
{
    public function toArray($request)
    { 
        return [
            
            $this->TOURNAMENT_ID.' '  => [
                'name'          => $this->TOURNAMENT_NAME,
                // 'description'   => $this->TOURNAMENT_DESC,
                'type_id'       => $this->TOURNAMENT_TYPE_ID,
                'type'          => $this->TOURNAMENT_TYPE_NAME,
                'start_date'    => $this->TOURNAMENT_START_TIME,
                'expire_on'     => $this->TOURNAMENT_END_TIME,
                'buy_in'        => $this->BUYIN + $this->ENTRY_FEE,
                'tickets'       => UserTicketResource::collection($this->userTickets)
            ]
        ];
    }
}