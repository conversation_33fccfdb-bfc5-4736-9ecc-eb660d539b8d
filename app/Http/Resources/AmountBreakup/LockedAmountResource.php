<?php

namespace App\Http\Resources\AmountBreakup;

use Illuminate\Http\Resources\Json\JsonResource;

class LockedAmountResource extends JsonResource
{
    public function toArray($request)
    {

        $displayCodeUsed="";

        if($this->CODE_USED != "" || $this->CODE_USED != NULL) {
            $displayCodeUsed = $this->CODE_USED.'('.round($this->CODE_COINS).' Coins)';
        }

        $displayCoinsToBeMade = round(($this->COINS_TO_BE_MADE)-($this->COINS_MADE_SINCE_LAST_DEPOSIT));
       
        if($displayCoinsToBeMade < 0) {
            $displayCoinsToBeMade = 0;
        }
                            
        return [
            'deposit_amount'                 => $this->DEPOSIT_AMOUNT,
            'real_bal_at_deposit'            => $this->BAL_AT_DEPOSIT,
            'code_used'                      => $displayCodeUsed,
            'coin_to_be_earn'                => $this->COINS_TO_BE_MADE,
            'coin_made_since_last'           => $this->COINS_MADE_SINCE_LAST_DEPOSIT,
            'rem_coin_to_earn'               => $displayCoinsToBeMade,
            'amount_to_be_kept_as_bal'       => $this->AMOUNT_TO_BE_KEPT,
            'amount_eligable_for_withdrawal' => 0,
            'date'                           => $this->DEPOSIT_DATE
        ];
    }

}