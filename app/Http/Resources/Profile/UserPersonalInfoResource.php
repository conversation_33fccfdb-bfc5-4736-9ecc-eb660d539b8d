<?php

namespace App\Http\Resources\Profile;

use Illuminate\Http\Resources\Json\JsonResource;

class UserPersonalInfoResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'first_name'             => $this->FIRSTNAME,
            'last_name'              => $this->LASTNAME,
            'gender'                 => $this->GENDER,
            'dob'                    => $this->DATE_OF_BIRTH,
            'mobile'                 => $this->CONTACT,
            'email'                  => $this->EMAIL_ID,
            'email_verify'           => $this->EMAIL_VERIFY,
            'fb_id'                  => $this->FB_USER_ID,
            'g_id'                   => $this->GOOGLE_ID,
            'document_updated_date'  => $this->UPDATED_DATE
        ];
    }
}