<?php

namespace App\Http\Resources\Profile;

use Illuminate\Http\Resources\Json\JsonResource;

class UserAccountDetailsResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'account_number'         => $this->ACCOUNT_NUMBER ?? '',
            'account_type'           => $this->ACCOUNT_TYPE ?? '',
            'account_holder_name'    => $this->ACCOUNT_HOLDER_NAME ?? '',
            'bank_ifsc_code'         => $this->IFSC_CODE ?? '',
            'bank_name'              => $this->BANK_NAME ?? '',
            'branch_name'            => $this->BRANCH_NAME ?? '',
            'verification_status'    => '',
            'document_updated_date'  => $this->UPDATED_DATE ?? ''
        ];
    }
}