<?php

namespace App\Http\Resources\Profile;

use Illuminate\Http\Resources\Json\JsonResource;

class UserAddressResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'address_1'             => $this->ADDRESS ?? '',
            'address_2'             => $this->STREET ?? '',
            'city'                  => $this->CITY ?? '',
            'state'                 => $this->STATE ?? '',
            'country'               => $this->COUNTRY ?? '',
            'pin_code'              => $this->PINCODE ?? '',
            'verification_status'   => $this->ACCOUNT_STATUS ?? '',
            'document_updated_date' => $this->UPDATED_DATE ?? '',
        ];
    }
}