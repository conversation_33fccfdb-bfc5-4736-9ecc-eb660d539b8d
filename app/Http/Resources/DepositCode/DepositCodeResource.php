<?php

namespace App\Http\Resources\DepositCode;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\CampaignUserSpecific;


class DepositCodeResource extends JsonResource
{ 
    public function toArray($request)
    {  
        return [
            'code'                    =>  $this->PROMO_CAMPAIGN_CODE ?? '',
            'min_deposit'             =>  $this->MINIMUM_DEPOSIT_AMOUNT ?? '',
            'expire_on'               =>  $this->END_DATE_TIME ?? '',
            'coin_criteria'           =>  (double)$this->COINS_REQUIRED ?? '',
            'desc'                    =>  $this->PROMO_CAMPAIGN_DESC ?? '',
            'tournament_name'         =>  $this->TOURNAMENT_NAME ?? '',
            'coupon_tag'              =>  !empty($this->USER_SPECIFIC_CHECK) ? 'MY OFFER' : (($this->IS_FEATURED == 1) ?  'FEATURED' : '')
        ];
    }
}