<?php

namespace App\Http\Controllers\Voucher;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Payment\AddMoney\AddMoneyBaseController;
use App\Models\BonusTransactionHistory;
use App\Models\BulkPromoCampaign;
use App\Models\PromoCampaign;
use Illuminate\Support\Facades\Log;
use DB;
use App\Models\MasterTransactionHistory;
use App\Models\UserPoint;
use App\Models\PromoRule;
use App\Models\CampaignToUser;
use App\Models\GlobalConfig;
use App\Models\MasterTransactionHistoryFppBonus;
use App\Models\MasterTransactionHistoryRewardpoints;
use App\Models\State;
use App\Models\User;

class VoucherBaseController extends AddMoneyBaseController
{
    private $apiCode;
    public function __construct()
    {
        $this->apiCode = 1;
    }
    protected function validateAndRedeemGiftVoucher($code, $userId)
    {
        try {
            DB::beginTransaction();

            $transactionVariablesArr = config('rummy_config.transactionVariables');
            $userAmountLock = UserPoint::select(
                'USER_TOT_BALANCE'
            )
                ->where('USER_ID', $userId)
                ->where('COIN_TYPE_ID', $transactionVariablesArr['depositCoinTypeID'])
                ->lockForUpdate()
                ->first();
            $bulkCodeDetails = $this->getBulkCodeDetails($code);
            if ($bulkCodeDetails) {
                if ($bulkCodeDetails->USER_ID == 0 && $bulkCodeDetails->STATUS == 1) {
                    $promoCodeDetails = $this->getPromoCodeDetails($bulkCodeDetails->PROMO_CAMPAIGN_ID);
                    if ($promoCodeDetails) {
                        /* Add promo code to promo_clicks table */
                        $PROMO_CAMPAIGN_ID = $promoCodeDetails->PROMO_CAMPAIGN_ID;
                        $this->addPromoCodeCheckCount($PROMO_CAMPAIGN_ID);
                        /*Check for expiry of promo campaign*/
                        $current_date = date('Y-m-d');
                        if ($promoCodeDetails->END_DATE_TIME < $current_date) {
                            $this->userActivitiesTracking($userId, "PROMO_CAMPAIGN_EXPIRED - " . $code, ["error" => "Promo Campaign Expired"]);
                            $this->errorBag[] = [
                                'code' => 422127,
                                'message' => 'Promo Campaign Expired'
                            ];
                            return $this->errorResponse(1, $this->errorBag, 422);
                        }
                        $isPromoValid = false;
                        if ($promoCodeDetails->STATUS == 1 && $promoCodeDetails->PROMO_STATUS_ID == 1) {
                            $isPromoValid = true;
                        }
                        if (!$isPromoValid) {
                            $this->userActivitiesTracking($userId, "GIFT_VOUCHER_REDEMPTION_FAILED - " . $code, ["error" => "Gift Card status is not enabled"]);
                            $this->errorBag[] = [
                                'code' => 422127,
                                'message' => 'Invalid Gift Card'
                            ];
                            return $this->errorResponse(1, $this->errorBag, 422);
                        }
                        $userDetails = $this->getUserDetails($userId);
                        if ($promoCodeDetails->PARTNER_ID != 10001) {
                            if ($promoCodeDetails->PARTNER_ID != $userDetails->PARTNER_ID) {
                                $this->errorBag[] = [
                                    'code' => 422029,
                                    'message' => 'Code is valid for a specific partner only'
                                ];
                                return $this->errorResponse(1, $this->errorBag, 422);
                            }
                        }
                        $promoRule = PromoRule::select('PROMO_PAYMENT_TYPE_ID', 'S_PROMO_CHIPS', 'S_PROMO_CHIP_VALUE', 'MULTIPLE_CODE_USES', 'VOUCHER_USAGE', 'KYC_CHECK')->where('PROMO_CAMPAIGN_ID', $promoCodeDetails->PROMO_CAMPAIGN_ID)->first();
                        //Check if user will get promo balance
                        if ($promoRule->S_PROMO_CHIPS && empty($promoRule->S_PROMO_CHIP_VALUE)) {
                            $this->userActivitiesTracking($userId, "GIFT_VOUCHER_REDEMPTION_FAILED - " . $code, ["error" => "Gift Card Amount is not defined"]);
                            $this->errorBag[] = [
                                'code' => 422129,
                                'message' => 'Amount not defined.'
                            ];
                            return $this->errorResponse($this->apiCode, $this->errorBag, 422);
                        }
                        //Check for Gift Voucher allowed usages
                        if (!in_array($promoRule->VOUCHER_USAGE, ['1', '2', '3', '4', '5', '6'])) {
                            $this->userActivitiesTracking($userId, "GIFT_VOUCHER_REDEMPTION_FAILED - " . $code, ["error" => "Gift Card VOUCHER_USAGE is not is defined list"]);
                            $this->errorBag[] = [
                                'code' => 422128,
                                'message' => 'Invalid usage detected.'
                            ];
                            return $this->errorResponse($this->apiCode, $this->errorBag, 422);
                        }
                        //Fetch total transaction count of the user
                        $transactionCount = $this->getUserTotaltransactions($userId);
                        //Check for No Depsoit
                        if ($promoRule->VOUCHER_USAGE == 1 || $promoRule->VOUCHER_USAGE == 3) {
                            //$promoRule->VOUCHER_USAGE == 1 is for Sign Ups + No deposit
                            //$promoRule->VOUCHER_USAGE == 3 is for No deposits only
                            if ($transactionCount) {
                                $this->userActivitiesTracking($userId, "GIFT_VOUCHER_REDEMPTION_FAILED - " . $code, ["error" => "Gift Card can redeemed by new users only."]);
                                $this->errorBag[] = [
                                    'code' => 422127,
                                    'message' => 'Gift card can redeemed by new users only.'
                                ];
                                return $this->errorResponse($this->apiCode, $this->errorBag, 422);
                            }
                        }
                        //Check for Min 1 Deposit
                        if ($promoRule->VOUCHER_USAGE == 2 || $promoRule->VOUCHER_USAGE == 4) {
                            //$promoRule->VOUCHER_USAGE == 2 is for Sign Ups + Min 1 deposit
                            //$promoRule->VOUCHER_USAGE == 4 is for Min 1 deposit only
                            if (!$transactionCount) {
                                $this->userActivitiesTracking($userId, "GIFT_VOUCHER_REDEMPTION_FAILED - " . $code, ["error" => "Gift card can be redeemed after making minimum 1 successful deposit."]);
                                $this->errorBag[] = [
                                    'code' => 422127,
                                    'message' => 'Gift card can be redeemed after making minimum 1 successful deposit.'
                                ];
                                return $this->errorResponse($this->apiCode, $this->errorBag, 422);
                            }
                        }
                        //Check for KYC Completion
                        if ($promoRule->KYC_CHECK == 1) {
                            $user = User::find($userId);
                            if ($user->KYC_REMAINING_STEPS != 0) {
                                $this->userActivitiesTracking($userId, "GIFT_VOUCHER_REDEMPTION_FAILED - " . $code, ["error" => "Gift card can be redeemed after KYC completion."]);
                                $this->errorBag[] = [
                                    'code' => 422127,
                                    'message' => 'Gift card can be redeemed after KYC completion.'
                                ];
                                return $this->errorResponse($this->apiCode, $this->errorBag, 422);
                            }
                        }
                        //$promoRule->VOUCHER_USAGE == 5 is for All Depsoits Only and No Signups
                        //$promoRule->VOUCHER_USAGE == 6 is for All i.e. sign ups and Deposits
                        /* check for max usage count */
                        $usageCount = $this->getPromocodeUsagecount($promoCodeDetails->PROMO_CAMPAIGN_ID);
                        if ($usageCount >= $promoCodeDetails->EXP_USER_MAX) {
                            $this->userActivitiesTracking($userId, "GIFT_VOUCHER_REDEMPTION_FAILED - " . $code, ["error" => "Gift voucher already used maximum allowed times"]);
                            $this->errorBag[] = [
                                'code' => 422127,
                                'message' => 'Gift voucher already used maximum allowed times'
                            ];
                            return $this->errorResponse($this->apiCode, $this->errorBag, 422);
                        }
                        //Check if multiple codes of same campaign is restricted to use
                        if (!$promoRule->MULTIPLE_CODE_USES) {
                            $promoCodeUsed = $this->promoCodeUsageCheck($promoCodeDetails->PROMO_CAMPAIGN_ID, $userId);
                            if ($promoCodeUsed) {
                                $this->userActivitiesTracking($userId, "GIFT_VOUCHER_REDEMPTION_FAILED - " . $code, ["error" => "Gift voucher associated with this campaign is already redeemed."]);
                                $this->errorBag[] = [
                                    'code' => 422127,
                                    'message' => 'Gift voucher associated with this campaign is already redeemed.'
                                ];
                                return $this->errorResponse($this->apiCode, $this->errorBag, 422);
                            }
                        }
                        $this->userActivitiesTracking($userId, "GIFT_VOUCHER_VALIDATIONS_PASSED - " . $code, ["code" => $code]);
                        return $this->redeemVoucher($code, $userId, $promoRule, $promoCodeDetails, $transactionCount, $userDetails);
                    } else {
                        $this->userActivitiesTracking($userId, "INVALID_GIFT_CARD - " . $code, ["error" => "Invalid Gift Card"]);
                        $this->errorBag[] = [
                            'code' => 422127,
                            'message' => 'Invalid Gift Card'
                        ];
                        return $this->errorResponse($this->apiCode, $this->errorBag, 422);
                    }
                } else {
                    $this->userActivitiesTracking($userId, "ALREADY_REDEEMED - " . $code, ["error" => "Already redeemed"]);
                    $this->errorBag[] = [
                        'code' => 422127,
                        'message' => 'Already redeemed'
                    ];
                    return $this->errorResponse($this->apiCode, $this->errorBag, 422);
                }
            } else {
                $this->userActivitiesTracking($userId, "DOES_NOT_EXIST - " . $code, ["error" => "Does not exist"]);
                $this->errorBag[] = [
                    'code' => 422127,
                    'message' => 'Does not exist'
                ];
                return $this->errorResponse($this->apiCode, $this->errorBag, 422);
            }
        } catch (\Exception $e) {
            DB::rollback();
            $this->userActivitiesTracking($userId, 'GIFT_VOUCHER_REDEMPTION_FAILED - ' . $code, ['error' => $e]);
            Log::error($e);
            return $this->errorResponse($this->apiCode, array([
                'code' => 422128,
                'message' => 'Someting went wrong. Unable to allocate promo balance to user.'
            ]));
        }
    }

    protected function getBulkCodeDetails($code)
    {
        $bulkCodeDetails = BulkPromoCampaign::where([
            ['BULK_UNIQUE_CODE', '=', $code]
        ])->first();
        if ($bulkCodeDetails) {
            return $bulkCodeDetails;
        } else {
            return false;
        }
    }

    protected function getPromoCodeDetails($PROMO_CAMPAIGN_ID)
    {
        $current_date = date('Y-m-d H:i:s');
        $promoCodeDetails = PromoCampaign::where([
            ['PROMO_CAMPAIGN_ID', '=', $PROMO_CAMPAIGN_ID],
            ['START_DATE_TIME', '<=', $current_date],
            ['END_DATE_TIME', '>=', $current_date],
            ['PROMO_CAMPAIGN_TYPE_ID', '=', 10]
        ])->orderBy('PROMO_CAMPAIGN_ID', 'desc')->limit(1)->first();
        if ($promoCodeDetails) {
            return $promoCodeDetails;
        } else {
            return false;
        }
    }

    protected function redeemVoucher($code, $userId, $promoRule, $promoCampaign, $transactionCount,  $userDetails)
    {
        $processBonus = true;
        DB::beginTransaction();
        try {

            // Pending Bonus functionality removed due to milestones intoduced to promote KYC

            // $globalConfig = GlobalConfig::whereIn('CONFIG_KEY', ['doc_validation', 'kyc_method'])->get();
            // $kycMethod = $globalConfig->where('CONFIG_KEY', 'kyc_method')->pluck('CONFIG_VALUE')->first();
            // if ($kycMethod == 'manual') {
            //     if ($userDetails->KYC_REMAINING_STEPS != 0 && !$this->aadharVerificationStatus($userId) ) {
            //         $processBonus = false;
            //     }
            // } else if (!$this->aadharVerificationStatus($userId)) {
            //     $processBonus = false;
            // }

            if (!$processBonus) {
                $otherDetails['PROMO_CAMPAIGN_CODE'] = $promoCampaign->PROMO_CAMPAIGN_CODE;
                $otherDetails['PROMO_CAMPAIGN_ID'] = $promoCampaign->PROMO_CAMPAIGN_ID;
                $otherDetails['PROMO_CAMPAIGN_TYPE_ID'] = $promoCampaign->PROMO_CAMPAIGN_TYPE_ID;
                $otherDetails['PROMO_PAYMENT_TYPE_ID'] = $promoRule->PROMO_PAYMENT_TYPE_ID;
                $this->userActivitiesTracking($userId, 'GIFT_VOUCHER_BONUS_ADDED_TO_PENDING_AS_AADHAR_NOT_VERIFIED - ' . $code, [$promoRule, $promoCampaign]);
            }

            $txnDate = date('Y-m-d H:i:s');
            BulkPromoCampaign::where('BULK_UNIQUE_CODE', $code)
                ->update([
                    'USER_ID' => $userId,
                    'STATUS' => 3
                ]);

            $transactionVariablesArr = config('rummy_config.transactionVariables');
            $balanceTypeID = $transactionVariablesArr['promoBalanceTypeId'];;
            $promoPendingStatusId = $transactionVariablesArr['promoPendingStatusId'];
            $voucherPromoTransactionTypeId = "";
            $voucherAmount = 0;
            $internalReferenceNo = "";
            $successBag = array();
            $sendVoucherRedemptionBonusEmail = false;

            if ($promoRule->S_PROMO_CHIPS) {
                $coinTypeID = "";
                $mthTable = "";
                $columnToBeUpdated = "USER_PROMO_BALANCE";
                $status = "";
                $balanceType = "";
                \Log::info($promoRule->PROMO_PAYMENT_TYPE_ID);
                if ($promoRule->PROMO_PAYMENT_TYPE_ID == 1) {
                    //Real Cash Bonus (Promo)
                    $coinTypeID = $transactionVariablesArr['depositCoinTypeID'];
                    $voucherPromoTransactionTypeId = $transactionVariablesArr['cbVoucherBonusTransactionTypeId'];
                    $status = "3";
                    $balanceType = "Unlocked";
                    $bonusType = "Real Cash Bonus";
                    $mthTable = new MasterTransactionHistory();
                } elseif ($promoRule->PROMO_PAYMENT_TYPE_ID == 2) {
                    //Locked Bonus (Unclaimed)
                    $coinTypeID = $transactionVariablesArr['fppBonusCoinTypeID'];
                    $voucherPromoTransactionTypeId = $transactionVariablesArr['cbVoucherBonusTransactionTypeId'];
                    $status = "1";
                    $balanceType = "Locked";
                    $bonusType = "Locked Bonus";
                    $mthTable = new MasterTransactionHistoryFppBonus();
                } elseif ($promoRule->PROMO_PAYMENT_TYPE_ID == 3) {
                    //Real Cash Chips (Withdrawable)
                    $coinTypeID = $transactionVariablesArr['depositCoinTypeID'];
                    $voucherPromoTransactionTypeId = $transactionVariablesArr['cbVoucherRCCTransactionTypeId'];
                    $status = "3";
                    $balanceType = "Win";
                    $bonusType = "Real Cash Chips";
                    $mthTable = new MasterTransactionHistory();
                    $columnToBeUpdated = "USER_WIN_BALANCE";
                } elseif ($promoRule->PROMO_PAYMENT_TYPE_ID == 12) {
                    //Reward Points
                    $coinTypeID = $transactionVariablesArr['rewardPointsCoinTypeID'];
                    $voucherPromoTransactionTypeId = $transactionVariablesArr['cbVoucherBonusTransactionTypeId'];
                    $status = "3";
                    $balanceType = "Reward points";
                    $bonusType = "Reward Point";
                    $mthTable = new MasterTransactionHistoryRewardpoints();
                }

                $voucherAmount = $promoRule->S_PROMO_CHIP_VALUE;
                $successBag['bonus_amount'] = $voucherAmount;
                $successBag['bonus_type'] = $bonusType;
                $expiry_date_db = $promoCampaign->BONUS_EXPIRY_INTERVAL_DAYS;
                $redeem_date = date("Y-m-d h:i:s");
                $expiry_date = date('Y-m-d h:i:s', strtotime("+" . $expiry_date_db . " days", strtotime($redeem_date)));

                $CampaignToUser = new CampaignToUser();
                $CampaignToUser->USER_ID = $userId;
                $CampaignToUser->PROMO_CAMPAIGN_ID = $promoCampaign->PROMO_CAMPAIGN_ID;
                $CampaignToUser->CAMPAIGN_CODE = $promoCampaign->PROMO_CAMPAIGN_CODE;
                $CampaignToUser->BONUSE_TYPE = $balanceType;
                $CampaignToUser->BONUS_AMOUNT = $voucherAmount;
                $CampaignToUser->REDEEM_DATE = $redeem_date;
                $CampaignToUser->EXPIRY_DATE = $expiry_date;
                $CampaignToUser->BONUS_STATUS = $status;
                $CampaignToUser->STATUS = $status;
                $CampaignToUser->COIN_TYPE_ID = $coinTypeID;

                if ($promoRule->PROMO_PAYMENT_TYPE_ID == 2) {
                    $CampaignToUser->BONUS_RELEASE_PER = $promoCampaign->BONUS_RELEASE_PER;
                }
                $CampaignToUser->CAMPAIGN_NAME = $promoCampaign->PROMO_CAMPAIGN_CODE;
                $CampaignToUser->save();

                $userCurrentRealBalance = UserPoint::where([
                    'COIN_TYPE_ID' => $coinTypeID,
                    'USER_ID' => $userId
                ])->limit(1)->first();

                \Log::info($coinTypeID);
                \Log::info($userCurrentRealBalance);

                $micro_date = explode(" ", microtime());
                $micro_date = substr($micro_date[0], 2);
                $internalReferenceNo = "091" . $userId . $micro_date . rand(10, 99);

                if ($processBonus) {
                    $mthTable->USER_ID = $userId;
                    $mthTable->BALANCE_TYPE_ID = $balanceTypeID;
                    $mthTable->TRANSACTION_STATUS_ID = $transactionVariablesArr['promoOkSuccessPaymentStatus'];
                    $mthTable->TRANSACTION_TYPE_ID = $voucherPromoTransactionTypeId;
                    $mthTable->TRANSACTION_AMOUNT = $voucherAmount;
                    $mthTable->TRANSACTION_DATE = $txnDate;
                    $mthTable->INTERNAL_REFERENCE_NO = $internalReferenceNo;
                    $mthTable->CURRENT_TOT_BALANCE = $userCurrentRealBalance->USER_TOT_BALANCE;
                    $mthTable->CLOSING_TOT_BALANCE = ($userCurrentRealBalance->USER_TOT_BALANCE + $voucherAmount);
                    $mthTable->PARTNER_ID = $userDetails->PARTNER_ID;

                    if ($promoRule->PROMO_PAYMENT_TYPE_ID == 2) {
                        $mthTable->PROMO_CAMPAIGN_ID =    $promoCampaign->PROMO_CAMPAIGN_ID;
                    }
                    $mthTable->save();

                    DB::select(
                        'update user_points SET
                                    `VALUE` = ?,
                                    `' . $columnToBeUpdated . '` = `' . $columnToBeUpdated . '` + ?,
                                    `USER_TOT_BALANCE` = `USER_WIN_BALANCE` + `USER_PROMO_BALANCE` + `USER_DEPOSIT_BALANCE`,
                                    `UPDATED_DATE` = ?
                        WHERE
                            `USER_ID` = ? AND
                            `COIN_TYPE_ID` = ?
                        ',
                        [
                            $voucherAmount,
                            $voucherAmount,
                            $txnDate,
                            $userId,
                            $coinTypeID
                        ]
                    );
                    $sendVoucherRedemptionBonusEmail = true;
                } else {
                    $otherDetails['BENIFITS']['RCB']['COINS_REQUIRED'] = $promoCampaign->COINS_REQUIRED;
                    $otherDetails['BENIFITS']['RCB']['MONEY_OPTION'] = $promoCampaign->MONEY_OPTION;
                }
            }

            DB::commit();

            if ($sendVoucherRedemptionBonusEmail) {
                $user = User::select('USERNAME', 'EMAIL_ID', 'EMAIL_VERIFY')->where('USER_ID', $userId)->first();
                if ($user->EMAIL_VERIFY == 1) {
                    $this->sendMail([
                        "email" => $user->EMAIL_ID,
                        "username" => $user->USERNAME,
                        "emailkey" => 'voucher_redeem_success',
                        "userId" => $userId,
                        "amount" => $voucherAmount
                    ]);
                }
            }

            $successBag['status'] = "Success";
            $successBag['txnid'] = $internalReferenceNo;
            $successBag['created_date'] = $txnDate;
            $successBag['updated_date'] = $txnDate;
            $successBag['voucher_code'] = $code;
            $successBag['voucher_code_description'] = $promoCampaign->PROMO_CAMPAIGN_DESC;

            if (!$processBonus) {
                $successBag['bonus_status'] = "Pending";

                $bonusTransactionRecord = new BonusTransactionHistory();
                $bonusTransactionRecord->USER_ID = $userId;
                $bonusTransactionRecord->BALANCE_TYPE_ID = $balanceTypeID;
                $bonusTransactionRecord->TRANSACTION_STATUS_ID = $promoPendingStatusId;
                $bonusTransactionRecord->TRANSACTION_TYPE_ID = $voucherPromoTransactionTypeId;
                $bonusTransactionRecord->TRANSACTION_AMOUNT = $voucherAmount;
                $bonusTransactionRecord->TRANSACTION_DATE =  date('Y-m-d H:i:s');
                $bonusTransactionRecord->INTERNAL_REFERENCE_NO = $internalReferenceNo;
                $bonusTransactionRecord->PARTNER_ID = $userDetails->PARTNER_ID;
                $bonusTransactionRecord->OTHER_DETAILS = $otherDetails;
                $bonusTransactionRecord->save();

                $kycMethod = GlobalConfig::where('CONFIG_KEY', 'kyc_method')->pluck('CONFIG_VALUE')->first();
                $digilockerUrl = '';
                if ($kycMethod != 'manual') {
                    $digilockerUrlResponse = $this->generateDigilockerUrl($userId);
                    if ($digilockerUrlResponse) {
                        if ($digilockerUrlResponse == "Max KYC Attempt Reached") {
                            $successBag['digilocker_url_error'] = "Max KYC Attempt Reached";
                            $this->userActivitiesTracking($userId, "GIFT_VOUCHER_KYC_INITIATE_ERROR - " . $code, ["message" => "Max KYC Attempy Reached."]);
                        } elseif ($digilockerUrlResponse == "Aadhar Already Verified") {
                            $successBag['digilocker_url_error'] = "Aadhar Already Verified";
                            $this->userActivitiesTracking($userId, "GIFT_VOUCHER_KYC_INITIATE_ERROR - " . $code, ["message" => "KYC Already Done"]);
                        } else {
                            $digilockerUrl = $digilockerUrlResponse;
                        }
                    } else {
                        $successBag['digilocker_url_error'] = "There was some error in generating digilocker URL";
                    }
                }
                $successBag['release_document'] = $kycMethod == 'manual' ? "kyc" : "aadhaar";
                $successBag['digilocker_url'] = $digilockerUrl;
                $state = State::where('StateName', $userDetails->STATE)->first();
                if ($state) {
                    $successBag['state_data']['state_id'] = $state->StateID;
                    $successBag['state_data']['state_name'] = $userDetails->STATE;
                }
            }

            $this->userActivitiesTracking($userId, "GIFT_VOUCHER_REDEMPTION_SUCCESSUL - " . $code, ["message" => "Gift Card Redemption was successful."]);
            return $this->successResponse($this->apiCode, $successBag);
        } catch (\Exception $e) {
            DB::rollback();
            \Log::error($e);
            $this->userActivitiesTracking($userId, 'GIFT_VOUCHER_REDEMPTION_FAILED - ' . $code, ['error' => $e]);
            Log::error($e);
            return $this->errorResponse($this->apiCode, array([
                'code' => 422128,
                'message' => 'Someting went wrong. Unable to allocate promo balance to user.'
            ]));
        }
    }
}
