<?php

namespace App\Http\Controllers\Voucher;

use App\Http\Controllers\Voucher\VoucherBaseController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class VoucherController extends VoucherBaseController
{
    public function redeem(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|regex:/^[a-zA-Z0-9_]*$/|max:30'
        ], [
            'code.regex' => 'Only alphabets, numbers, and underscores allowed in promo code',
            'code.max' => 'Maximum 30 characters allowed in promo code'
        ]);
        if ($validator->fails()) {
            $errorCode = ["code" => 422127];
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $this->errorBag[] = [
                    'code' => $errorCode[$key],
                    'message' => $error[0]
                ];
            }
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        $userId = getUserId();
        /*Banned state check*/
        if ($bannedState = $this->isUserInBannedState($userId)) {
            $this->userActivitiesTracking($userId, "GIFT_VOUCHER_DENIED - " . $request->code, ["error" => 'User is from banned state']);
            return $this->errorResponse(1, [[
                'code' => 422104,
                'message' => "User is from banned state"
            ]]);
        }
        /*Banned state check end*/
        $this->userActivitiesTracking($userId, "INTIATED_CHECKING_GIFT_VOUCHER - " . $request->code, ["request" => $request->all()]);
        $codeResult = $this->validateAndRedeemGiftVoucher($request->code, $userId);
        return $codeResult;
    }
}
