<?php

namespace App\Http\Controllers\Payment\Withdraw;

use App\Http\Controllers\Controller;
use App\RummyBaazi\RummyBaazi;
use App\Traits\AuthenticationTraits;
use Illuminate\Http\Request;
use App\Models\MasterTransactionHistory;
use App\Models\UserPoint;
use App\Models\UserAccountDetail;
use App\Models\PaymentTransaction;
use App\Models\WithdrawTransactionHistory;
use App\Models\CashoutApproveTransaction;
use App\Models\WithdrawalCriteria;
use App\Models\User;
use DateTime;
use Validator;
use Auth;
use DB;
use Illuminate\Support\Facades\Log;
use App\Models\DepositLimitUser;
use App\Models\PaymentUserSetting;
use App\Models\GlobalConfig;
use App\Models\EmailerPreventedEmail;
use App\Models\PlayerLedger;
use App\Models\UserKycDetail;
use Carbon\Carbon;

class WithdrawController extends Controller
{
    use AuthenticationTraits;

    private $tdsRateOnCash;
    private $feeCutRateOnDepositAmount;
    private $cardNetbankingUpiPaymentProviderType;
    private $depositOkSuccessPaymentStatus;
    private $transactionSuccessPaymentStatus;
    private $realCashPurchaseTransactionTypes;
    private $refNo = "";

    public function __construct()
    {
        $this->tdsRateOnCash = config('rummy_config.transactionVariables.tdsRateOnCash');
        $this->feeCutRateOnDepositAmount = config('rummy_config.transactionVariables.feeCutRateOnDepositAmount');
        $this->cardNetbankingUpiPaymentProviderType = config('rummy_config.transactionVariables.cardNetbankingUpiPaymentProviderType');
        $this->depositOkSuccessPaymentStatus = config('rummy_config.transactionVariables.depositOkSuccessPaymentStatus');
        $this->transactionSuccessPaymentStatus = config('rummy_config.transactionVariables.transactionSuccessPaymentStatus');
        $this->realCashPurchaseTransactionTypes = config('rummy_config.transactionVariables.realCashPurchaseTransactionTypes');
    }

    const REAL_CASH_COIN_TYPE = 1;
    const WIN_BALANCE_TYPE = 3;
    const DEPOSIT_BALANCE_TYPE = 1;
    const WITHDRAW_BY_ONLINE = 2;
    const WITHDRAW_PENDING_TRANSACTION_STATUS = 109;
    const WITHDRAW_INITIATED_TRANSACTION_STATUS = 255;
    const WITHDRAW_FAILED_TRANSACTION_STATUS = 216;
    const TDS_TAX_OK_TRANSACTION_TYPE = 80;
    const WITHDRAW_PROCESS_FEE_TRANSACTION_TYPE = 74;
    const WITHDRAW_TRANSACTION_TYPE = 10;
    const WITHDRAW_REVERTED_TRANSACTION_TYPE = 66;
    const ADMIN_PARTNER_ID = 10001;
    const APPROVED_BY_CASHFREE = 1;
    const GREEN_FLAG = 2;
    const RED_FLAG = 3;
    const SUSPICIOUS_USER_FLAG = 4;
    const YELLOW_FLAG = 1;
    const WITHDRAW_REJECT_TRANSACTION_TYPE = 75;
    const WITHDRAW_REJECTED_TRANSACTION_STATUS = 112;
    const WITHDRAW_REVERTED_TRANSACTION_STATUS = 203;
    const WITHDRAW_APPROVED_TRANSACTION_STATUS = 208;
    const APPS = [18 => 'CARDBAAZI_ONLINE', 19 => 'RUMMYBAAZI_ONLINE'];

    /*
    * --------------------------------------------------------------
    * withdrawRevert : Revert a pending withdraw
    * @param Illuminate\Http\Request
    * @return JSON
    * <AUTHOR> Aloney <<EMAIL>>
    * --------------------------------------------------------------
    */

    public function withdrawRevert(Request $request)
    {
        $apiCode = 1;

        $errorCodes = array(
            'type' => 422048,
            'txn_id' => 422049,
            'otp' => 422006
        );

        $validation = Validator::make($request->all(), [
            'type' => 'required|string|in:cash,commission',
            'txn_id' => 'required|string',
            'otp' => 'required|numeric|digits:6'
        ]);

        if ($validation->fails()) {
            $errors = $validation->errors()->toArray();
            $errorBag = array();
            foreach ($errors as $field => $error) {
                array_push($errorBag, [
                    'code' => $errorCodes[$field],
                    'message' => $error[0]
                ]);
            }
            return $this->errorResponse($apiCode, $errorBag);
        }


        /*
        * ------------------------------------------------------------------------------------
        * CHECK OTP VERIFICATION
        * ------------------------------------------------------------------------------------
        */
        $otpSent = $this->verifyMobileOtp(\RummyBaazi::user()->CONTACT, $request->otp, getUserId(), 'VERIFY_WITHDRAW_REVERT_OTP');

        if ($otpSent->response->type != 'success') {
            return $this->errorResponse($apiCode, array([
                'code' => 422006,
                'message' => 'OTP Verification Error : ' . $otpSent->response->message
            ]));
        }

        DB::beginTransaction();

        try {


            $this->userActivitiesTracking(getUserId(), "Initiate Revert Request", [
                'userId' => getUserId(),
                'internalRefNo' => $request->txn_id
            ]);

            /*
            * ------------------------------------------------------------------------------------
            * CHECK IF RECORDS EXISTS WITH THE ALREADY APPROVED, REVERT OR REJECTED STATUS
            * ------------------------------------------------------------------------------------
            */

            $chkIfExists = WithdrawTransactionHistory::where([
                'USER_ID' => getUserId(),
                'INTERNAL_REFERENCE_NO' => $request->txn_id
            ])
                ->where('TRANSACTION_STATUS_ID', self::WITHDRAW_PENDING_TRANSACTION_STATUS)
                ->limit(1)
                ->count();
            
            $user = \RummyBaazi::user();


            if ($chkIfExists == 1 && $user->USER_FLAG != self::SUSPICIOUS_USER_FLAG) {

                $transaction = WithdrawTransactionHistory::select(
                    'BALANCE_TYPE_ID',
                    'WITHDRAW_AMOUNT AS TRANSACTION_AMOUNT',
                    'AMOUNT_BREAKUP_DETAILS',
                    'WITHDRAW_TYPE',
                    'DISCOUNTED_WITHDRAW_CHARGE',
                    'WITHDRAW_CHARGE',
                    'APP_TYPE'
                )
                    ->where([
                        'USER_ID' => getUserId(),
                        'INTERNAL_REFERENCE_NO' => $request->txn_id
                    ])->first();


                if (collect($transaction)->isNotEmpty()) {

                    $userCurrentRealBalance = $this->userCurrentBalance(self::REAL_CASH_COIN_TYPE, getUserId());

                    $newDepositBalance = 0;
                    $newWinBalance = 0;

                    $amountBreakupDetail = json_decode($transaction->AMOUNT_BREAKUP_DETAILS);


                    if ($transaction->BALANCE_TYPE_ID == 2 || $transaction->BALANCE_TYPE_ID == 1) {
                        $newDepositBalance = $transaction->TRANSACTION_AMOUNT;
                    } else {
                        $newWinBalance = $transaction->TRANSACTION_AMOUNT;
                    }

                    if (!empty($amountBreakupDetail->DEDUCTED_FROM_WIN) && !empty($amountBreakupDetail->DEDUCTED_FROM_DEPOSIT)) {
                        if (
                            $amountBreakupDetail->DEDUCTED_FROM_WIN > 0 &&
                            $amountBreakupDetail->DEDUCTED_FROM_WIN != ''
                        ) {
                            $newWinBalance = number_format(round($amountBreakupDetail->DEDUCTED_FROM_WIN, 2), 2, '.', '');
                        }

                        if (
                            $amountBreakupDetail->DEDUCTED_FROM_DEPOSIT > 0 &&
                            $amountBreakupDetail->DEDUCTED_FROM_DEPOSIT != ''
                        ) {
                            $newDepositBalance = number_format(round($amountBreakupDetail->DEDUCTED_FROM_DEPOSIT, 2), 2, '.', '');
                        }
                    }



                    /*
                    * ------------------------------------------------------------------------------------
                    * UPDATE WITHDRAW TRANSACTION HISTORY FOR REVERTED STATUS
                    * ------------------------------------------------------------------------------------
                    */
                    WithdrawTransactionHistory::where([
                        'USER_ID' => getUserId(),
                        'INTERNAL_REFERENCE_NO' => $request->txn_id
                    ])->update(['TRANSACTION_STATUS_ID' => self::WITHDRAW_REVERTED_TRANSACTION_STATUS]);


                    /*
                    * ------------------------------------------------------------------------------------
                    * UPDATE PAYMENT TRANSACTION FOR REVERTED STATUS
                    * ------------------------------------------------------------------------------------
                    */
                    PaymentTransaction::where([
                        'USER_ID' => getUserId(),
                        'INTERNAL_REFERENCE_NO' => $request->txn_id
                    ])->update(['PAYMENT_TRANSACTION_STATUS' => self::WITHDRAW_REVERTED_TRANSACTION_STATUS]);

                    /*
                    * ------------------------------------------------------------------------------------
                    * INSERTING INTO MASTER TRANSACTION HISTORY
                    * ------------------------------------------------------------------------------------
                    */
                    $newClosingTotalBalance = ($userCurrentRealBalance->total + $transaction->TRANSACTION_AMOUNT);
                    $masterTransaction = new MasterTransactionHistory();
                    $masterTransaction->USER_ID                = getUserId();
                    $masterTransaction->BALANCE_TYPE_ID        = $transaction->BALANCE_TYPE_ID;
                    $masterTransaction->TRANSACTION_STATUS_ID  = self::WITHDRAW_REVERTED_TRANSACTION_STATUS;
                    $masterTransaction->TRANSACTION_TYPE_ID    = self::WITHDRAW_REVERTED_TRANSACTION_TYPE;
                    $masterTransaction->TRANSACTION_AMOUNT     = $transaction->TRANSACTION_AMOUNT;
                    $masterTransaction->TRANSACTION_DATE       = date('Y-m-d H:i:s');
                    $masterTransaction->INTERNAL_REFERENCE_NO  = $request->txn_id;
                    $masterTransaction->CURRENT_TOT_BALANCE    = $userCurrentRealBalance->total;
                    $masterTransaction->CLOSING_TOT_BALANCE    = $newClosingTotalBalance;
                    $masterTransaction->PARTNER_ID             = self::ADMIN_PARTNER_ID;
                    $masterTransaction->save();

                    /*
                    * ------------------------------------------------------------------------------------
                    * UPDATING USER POINTS ONE BY ONE IF MORE RECORDS
                    * ------------------------------------------------------------------------------------
                    */
                    DB::select(
                        'update user_points SET
                                    `VALUE` = ?,
                                    `USER_WIN_BALANCE` = `USER_WIN_BALANCE` + ?,
                                    `USER_DEPOSIT_BALANCE` = `USER_DEPOSIT_BALANCE` + ?,
                                    `USER_TOT_BALANCE` = `USER_WIN_BALANCE` + `USER_PROMO_BALANCE` + `USER_DEPOSIT_BALANCE`,
                                    `UPDATED_DATE` = ?
                        WHERE
                            `USER_ID` = ? AND
                            `COIN_TYPE_ID` = ?
                        ',
                        [
                            $transaction->TRANSACTION_AMOUNT,
                            $newWinBalance,
                            $newDepositBalance,
                            date('Y-m-d H:i:s'),
                            getUserId(),
                            self::REAL_CASH_COIN_TYPE
                        ]
                    );

                    /*Sending Mail*/
                    $user = \RummyBaazi::user();
                    DB::commit();
                    // CB-823 (if - else logic only)
                    $checkEmailId = EmailerPreventedEmail::where('EMAIL_ID', $user->EMAIL_ID)->get();
                    if (count($checkEmailId) > 0) {
                        Log::info("Emails not sent as email id of user " . $user->EMAIL_ID . " is in bounce restricted email list. Transaction ID: " . $request->txn_id);
                    } else {
                        if ($user->EMAIL_VERIFY == 1) {
                            $this->sendMail([
                                "email" => $user->EMAIL_ID,
                                "username" => $user->USERNAME,
                                "emailkey" => 'withdrawal_reversal_approved',
                                "userId" => getUserId(),
                                "amount" => $transaction->TRANSACTION_AMOUNT,
                                "withdrawRequestType" => "",
                                'commonDiv' => "" 
                            ]);
                        }
                    }

                    /*Hitting clever tap event*/
                    $this->trackingTableData = DB::table('tracking')
                        ->select('DEVICE_MODEL', 'OPERATING_SYSTEM')
                        ->where('USER_ID', $user->USER_ID)
                        ->orderByDesc('TRACKING_ID')
                        ->limit(1)
                        ->first();

                    $this->createCleverTapEvent([
                        'userId' => $user->USER_ID,
                        'event' => 'WITHDRAWAL',
                        'evtData' => [
                            'USERNAME' => $user->USERNAME,
                            'STATUS' => 'REVERTED',
                            'DEVICE' => $this->trackingTableData->DEVICE_MODEL . '(' . $this->trackingTableData->OPERATING_SYSTEM . ')',
                            'TIMESTAMP' => time(),
                            'USER_ID' => (string)$user->USER_ID,
                            'AMOUNT' => (float)$transaction->TRANSACTION_AMOUNT,
                            'TDS' => (float)$transaction->WITHDRAW_TDS,
                            'FEE'   => (float)$transaction->FEE,
                            'ACTUAL_CHARGE' => (float)$transaction->WITHDRAW_CHARGE,
                            'DISCOUNTED_CHARGE' => (float)($transaction->DISCOUNTED_WITHDRAW_CHARGE),
                            'WITHDRAW_TYPE' => $transaction->WITHDRAW_TYPE == 1 ? 'INSTANT' : 'STANDARD',
                            'app_type' => $transaction->APP_TYPE
                        ]
                    ]);

                    /*end*/
                    return $this->successResponse($apiCode);
                } else {
                    return $this->errorResponse($apiCode, array([
                        'code' => 422057,
                        'message' => 'There is not a pending withdraw transaction with this reference no.'
                    ]));
                }
            } else {
                return $this->errorResponse($apiCode, array([
                    'code' => 422057,
                    'message' => $user->USER_FLAG == self::SUSPICIOUS_USER_FLAG ? 'Unable to revert withdraw. Please contact support for more information' : 'There is not a pending withdraw transaction with this reference no.'
                ]));
            }
        } catch (\Exception $e) {
            DB::rollBack();
            $this->userActivitiesTracking(getUserId(), "WITHDRAW_REVERT_DB_TRANSACTION_FAILED", $e->getMessage());
            Log::error($e);
            return $this->errorResponse($apiCode, array([
                'code' => 500000,
                'message' => 'Internal Server Error.'
            ]));
        }
    }

    /*
    * -------------------------------------------------------------
    * withdrawCheck : check for the user's withdraw eligibility
    * @param Illuminate\Http\Request
    * @return JSON
    * <AUTHOR> Aloney <<EMAIL>>
    * -------------------------------------------------------------
    */

    public function withdrawCheck(Request $request)
    {
        $apiCode = 1;
        $withdrawUnderMaintenance = GlobalConfig::where('CONFIG_KEY', 'withdraw_under_maintenance')->where('CONFIG_VALUE', 1)->exists();
        if ($withdrawUnderMaintenance) {
            $errorBag = array([
                'code' => 503002,
                'message' => 'Withdraw under maintenance'
            ]);
            return $this->errorResponse($apiCode, $errorBag, 503);
        }

        $user = \RummyBaazi::user();
        $errorCodes = array(
            'amount' => 422046,
            'type' => 422048,
            'txn_type' => 422049
        );

        $validation = Validator::make($request->all(), [
            'amount' => ($request->txn_type == 'transfer') ? 'required|numeric|min:1|max:999999' : 'required|numeric|min:' . config('rummy_config.MINIMUM_WITHDRAW_AMOUNT') . '|max:999999',
            'type' => 'required|string|in:cash',
            'txn_type' => 'required|string|in:1,2,withdraw'
        ]);

        if($request->txn_type == 'withdraw') {
            $request->merge([
                'txn_type' => '2'
            ]);
        }

        if ($validation->fails()) {
            $errors = $validation->errors()->toArray();
            $errorBag = array();
            foreach ($errors as $field => $error) {
                array_push($errorBag, [
                    'code' => $errorCodes[$field],
                    'message' => $error[0]
                ]);
            }
            return $this->errorResponse($apiCode, $errorBag);
        }

        $perDayTransactionAmountLimit = $user->USER_FLAG == self::YELLOW_FLAG ? config('rummy_config.instantWithdrawMaxCapping.yellowPerDay') : config('rummy_config.instantWithdrawMaxCapping.greenPerDay');
        $perTransactionAmountLimit = $user->USER_FLAG == self::YELLOW_FLAG ? config('rummy_config.instantWithdrawMaxCapping.yellowPerTrans') : config('rummy_config.instantWithdrawMaxCapping.greenPerTrans');

        $totalWithdraw = $request->amount + $this->getDailyTotalInstantWithdrawAmount();

        if((
            ($user->USER_FLAG == self::YELLOW_FLAG &&
                config('rummy_config.instantPayoutFlagConsider.yellow') &&
                $totalWithdraw > config('rummy_config.instantWithdrawMaxCapping.yellowPerDay') &&
                $request->amount > config('rummy_config.instantWithdrawMaxCapping.yellowPerTrans')
            )
            ||
            ($user->USER_FLAG == self::GREEN_FLAG &&
                config('rummy_config.instantPayoutFlagConsider.green') &&
                $totalWithdraw > config('rummy_config.instantWithdrawMaxCapping.greenPerDay') &&
                $request->amount > config('rummy_config.instantWithdrawMaxCapping.greenPerTrans')
            )
            || $user->USER_FLAG == self::RED_FLAG
            || $user->USER_FLAG == self::SUSPICIOUS_USER_FLAG
        ) && $request->txn_type == 1
        ) {
            $errorBag = [
                [
                    'code' => 422058,
                    'message' => $totalWithdraw > $perDayTransactionAmountLimit ? 'Your daily withdrawal amount limit has exhausted. You can start withdrawing again after 12AM' : "Instant withdrawals are not applicable on transactions of Rs. $perTransactionAmountLimit or more"
                ]
            ];

            return $this->errorResponse($apiCode, $errorBag);
        }

        $appTypeCode = $request->app_type;
        $appName = self::APPS[$appTypeCode] ?? 'DEFAULT';

        if (GlobalConfig::where('CONFIG_KEY', 'withdraw_status')->pluck('CONFIG_VALUE')->first() != 'active') {
            $errorBag = array([
                'code' => 422132,
                'message' => 'Withdrawal facility is temporarily not available, please try after few hours.'
            ]);
            return $this->errorResponse($apiCode, $errorBag);
        }

        if ($this->isWithdrawTransLimitExceed($request->txn_type)) {
            $errorBag = array([
                'code' => 422134,
                'message' => 'Daily withdrawal transactions limit reached.'
            ]);

            return $this->errorResponse($apiCode, $errorBag);
        }

        // if($this->getProfileStatus(getUserId()) != '0'){
        //     $errorBag = array([
        //                     'code' => 422047,
        //                     'message' => 'KYC not completed.'
        //                 ]);
        //     return $this->errorResponse($apiCode, $errorBag);
        // }

        /*  Pan and address proof required as per lifetime withdraw limit */
        $availableDocuments = UserKycDetail::where('USER_ID', $user->USER_ID)->where('DOCUMENT_STATUS', 1)->pluck('DOCUMENT_TYPE')->toArray();

        if (count($availableDocuments) < config('rummy_config.DOC_PROOF.' . $appName . '.DOC_COUNT')) {

            $requiredDocsWithName = config('rummy_config.REQUIRED_DOCUMENT_TYPES');

            $requiredDocs = array_keys($requiredDocsWithName);

            $diff = array_diff($requiredDocs, $availableDocuments);

            $requiredDocsWithName = array_filter($requiredDocsWithName, function ($key) use ($diff) {
                if (in_array($key, $diff)) return true;
            }, ARRAY_FILTER_USE_KEY);

            $lifeTimeWithdraw =  $this->getUserTotalWithdraw($user->USER_ID);
            if (($lifeTimeWithdraw + $request->amount) > config('rummy_config.DOC_PROOF.' . $appName . '.LIFE_TIME_WITHDRAW')) {

                $errorBag = array([
                    'code' => 422154,
                    'message' => 'Kindly provide ' . implode('/', $requiredDocsWithName) . ' for further withdrawal.',
                    'required_doc' => implode('/', $requiredDocsWithName)
                ]);
                return $this->errorResponse($apiCode, $errorBag);
            }
        }

        $userCurrentBalance = $this->userCurrentBalance(self::REAL_CASH_COIN_TYPE);


        if (
            !$this->isWithdrawableBalanceIsGreaterThanRequestedAmount($request->amount, $userCurrentBalance)
        ) {
            $errorBag = array([
                'code' => 422050,
                'message' => 'Insufficiant Balance.'
            ]);
            return $this->errorResponse($apiCode, $errorBag);
        }

        // $dailyWithdrawLimit = DepositLimitUser::select('WITHDRAW_LIMIT')
        //                         ->where('USER_ID', getUserId())
        //                         ->pluck('WITHDRAW_LIMIT')
        //                         ->first() 
        //                         ?? 
        //                         config('rummy_config.transactionVariables.dailyWithdrawLimit');


        // $eligibleWithdrawAmount = $dailyWithdrawLimit - $this->getDailyTotalWithdrawAmount();

        // if($eligibleWithdrawAmount < $request->amount){
        //     $errorBag = array([
        //                     'code' => 422127,
        //                     'message' => 'Daily withdraw limit reached.'
        //                 ]);
        //     return $this->errorResponse($apiCode, $errorBag);
        // }

        $firstTimeDepositChk = PaymentTransaction::where(['USER_ID' => getUserId()])
            ->whereIn('TRANSACTION_TYPE_ID', array_values($this->realCashPurchaseTransactionTypes))
            ->whereIn('PAYMENT_TRANSACTION_STATUS', [
                $this->depositOkSuccessPaymentStatus,
                $this->transactionSuccessPaymentStatus
            ])->limit(1)->count();

        if ($firstTimeDepositChk == 0) {
            $errorBag = array([
                'code' => 422052,
                'message' => 'You have not done any deposit transaction yet.'
            ]);
            return $this->errorResponse($apiCode, $errorBag);
        }

        try {

            //---------------------------------------------------------
            // Create the ledger of user if not exist 
            // Only case occur, When user direct previous direct comes for withdraw
            //----------------------------------------------------------
            $this->createNewLedger(getUserId());


            $successBag = array(
                'tds_required' => false,
                'amount' => (float) $request->amount,
                'taxable_amount' => 0,
                'non_taxable_amount' => (float) $request->amount,
                'tds' => 0,
                'fee' => 0,
                'exempted_amount' => 0,
                'tds_percent' => 0,
                'withdraw_fee' => 0,
                'is_withdraw_fee_exempted' => false
            );

            //---------------------------------------------------------
            // Function name is TDS cal but it does not deduct the tds,
            // only calculates the withdraw fee
            //----------------------------------------------------------
            $applyTdsCalculation = $this->applyTdsCalculation($userCurrentBalance, $request->amount);
            $applyWithdrawFee = $this->applyWithdrawFee($request->amount, getUserId(), $request->txn_type);




            if ($applyTdsCalculation) {
                $taxApplicableAmount = ($request->amount - $applyTdsCalculation['nonTaxableAmount']);

                $successBag['fee'] = round($applyTdsCalculation['withdrawFee'], 2) ?? 0;
                $successBag['amount'] = round(($applyTdsCalculation['paidAmount']), 2);
                //if($applyTdsCalculation['deductedFromWin'] >= 10000){
                $successBag['tds_percent'] = $this->tdsRateOnCash;
                $successBag['tds'] = round($applyTdsCalculation['withdrawTds'], 2);
                $successBag['taxable_amount'] = round(($taxApplicableAmount > 0 ? $taxApplicableAmount : 0), 2);
                //}
                $successBag['non_taxable_amount'] = $applyTdsCalculation['nonTaxableAmount'] > $request->amount ? $request->amount : $applyTdsCalculation['nonTaxableAmount'];

                if($applyTdsCalculation['nonTaxableAmount'] > 0){
                    $applyWithdrawFeeSuggestion = $this->applyWithdrawFee($applyTdsCalculation['nonTaxableAmount'], getUserId(), $request->txn_type);
                    $successBag['non_taxable_amount_withdraw_fee'] = round($applyWithdrawFeeSuggestion['withdraw_fee'], 2) ?? 0;
                }
            }

            if($applyWithdrawFee) {
                $successBag['withdraw_fee'] = round($applyWithdrawFee['withdraw_fee'], 2) ?? 0;
                $successBag['is_withdraw_fee_exempted'] = $applyWithdrawFee['is_withdraw_fee_exempted'];
                $successBag['amount'] = round(($applyTdsCalculation['paidAmount'] - ($applyWithdrawFee['is_withdraw_fee_exempted'] ? 0 : $applyWithdrawFee['withdraw_fee'])), 2);
            }

            return $this->successResponse($apiCode, $successBag);
        } catch (\Exception $e) {
            Log::error($e);
            $errorBag = array([
                'code' => 500000,
                'message' => 'Internal Server Error'
            ]);

            return $this->errorResponse($apiCode, $errorBag);
        }
    }

    /*
    * -------------------------------------------------------------
    * withdrawOrTransfer : withdraw or transfer CASH and COMMISSION
    * balance for eligible user.
    * @param Illuminate\Http\Request
    * @return JSON
    * <AUTHOR> Aloney <<EMAIL>>
    * -------------------------------------------------------------
    */
    public function withdrawOrTransfer(Request $request)
    {
        $apiCode = 1;
        $errorCodes = array(
            'amount' => 422046,
            'type' => 422048,
            'txn_type' => 422049,
            'otp' => 422006,
            'txn_via' => 422153
        );

        $request['txn_via'] = isset($request['txn_via']) ? $request['txn_via'] : 'banktransfer';

        $validation = Validator::make($request->all(), [
            'amount' => ($request->txn_type == 'transfer') ? 'required|numeric|min:1|max:999999' : 'required|numeric|min:' . config('rummy_config.MINIMUM_WITHDRAW_AMOUNT') . '|max:999999',
            'type' => 'required|string|in:cash,commission',
            'txn_type' => 'required|string|in:1,2,withdraw',
            'otp' => 'required_if:txn_type,withdraw|numeric|digits:6'
            // 'txn_via' => 'required|string|in:upi,banktransfer'
        ]);

        if($request->txn_type == 'withdraw') {
            $request->merge([
                'txn_type' => '2'
            ]);
        }
        if ($validation->fails()) {
            $errors = $validation->errors()->toArray();
            $errorBag = array();
            foreach ($errors as $field => $error) {
                array_push($errorBag, [
                    'code' => $errorCodes[$field],
                    'message' => $error[0]
                ]);
            }
            return $this->errorResponse($apiCode, $errorBag);
        }

        // Check to disable withdraw for paytm bank
        if($request->txn_via == 'banktransfer'){
            $bankIfscPytm = UserAccountDetail::where('USER_ID', getUserId())->where('STATUS', 1)->where('IFSC_CODE', config('rummy_config.DISABLED_IFSC'))->exists();
            if ($bankIfscPytm) {
                $errorBag = array([
                    'code' => 422155,
                    'message' => 'This bank is temporarily disabled.'
                ]);
                return $this->errorResponse($apiCode, $errorBag, 422);
            }
        }

        if (GlobalConfig::where('CONFIG_KEY', 'withdraw_status')->pluck('CONFIG_VALUE')->first() != 'active') {
            $errorBag = array([
                'code' => 422132,
                'message' => 'Withdrawal facility is temporarily not available, please try after few hours.'
            ]);
            return $this->errorResponse($apiCode, $errorBag);
        }

        if ($request->txn_type == '1' || $request->txn_type == '2') {
            /*
            * ------------------------------------------------------------------------------------
            * CHECK OTP VERIFICATION
            * ------------------------------------------------------------------------------------
            */
            try {
                $otpSent = $this->verifyMobileOtp(\RummyBaazi::user()->CONTACT, $request->otp, getUserId(), 'VERIFY_WITHDRAW_OTP');
                if ($otpSent->response->type != 'success') {
                    return $this->errorResponse($apiCode, array([
                        'code' => 422006,
                        'message' => 'OTP Verification Error : ' . $otpSent->response->message
                    ]));
                }
            } catch (\Exception $e) {
                return $this->errorResponse($apiCode, array([
                    'code' => 422006,
                    'message' => 'OTP Verification Error : request failed'
                ]));
            }
        }

        $errorMsgs = array(
            422050 => 'Insufficiant Balance.',
            422045 => 'Bank Account Details Not Found.',
            422052 => 'One time Deposit Is Required',
            422053 => 'User Balance Mismatch',
            422055 => 'Amount Transfer Failed From Cash Free.',
            422051 => 'Error While Performing Withdraw Transaction.',
            422047 => 'KYC not completed.'
        );


        if ($request->type == 'cash' && ($request->txn_type == '1' || $request->txn_type == '2')) {

            if ($this->isWithdrawTransLimitExceed($request->txn_type)) {
                $errorBag = array([
                    'code' => 422134,
                    'message' => 'Daily withdrawal transactions limit reached.'
                ]);

                return $this->errorResponse($apiCode, $errorBag);
            }

            $statusCode = $this->withdrawCash($request);

            if ($statusCode == 10001) {
                $successBag = array(
                    "status" => "pending",
                    "amount" => $request->amount,
                    "txnid" => $this->refNo
                );
                return $this->successResponse($apiCode, $successBag);
            } else {
                $errorBag = array([
                    'code' => $statusCode,
                    'message' => $errorMsgs[$statusCode],
                    "amount" => $request->amount,
                    'txnid' => $this->refNo
                ]);
                return $this->errorResponse($apiCode, $errorBag);
            }
        } else {
            $errorBag = array([
                'code' => 422048,
                'message' => 'Invalid Type Chosen.'
            ]);
            return $this->errorResponse($apiCode, $errorBag);
        }
    }

    /*
    * ---------------------------------------------------------------------------
    * withdrawCash : withdraw a real cash balance; used in withdarwOrTransfer.
    * @param Illuminate\Http\Request
    * @return Int (code of response)
    * <AUTHOR> Aloney <<EMAIL>>
    * -----------------------------------------------------------------------------
    */
    private function withdrawCash(Request $request)
    {
        $userCurrentRealBalance = $this->userCurrentBalance(self::REAL_CASH_COIN_TYPE);
        $internalReferenceNumber = $this->internalReferenceNumber(113);
        $this->refNo = $internalReferenceNumber;

        if (!$this->isWithdrawableBalanceIsGreaterThanRequestedAmount($request->amount, $userCurrentRealBalance)) {
            return 422050; // Insufficiant Balance
        }

        $amountAfterDeductingRequestedAmountFromTotal = $userCurrentRealBalance->total - $request->amount;

        $firstTimeDepositChk = PaymentTransaction::where(['USER_ID' => getUserId()])
            ->whereIn('TRANSACTION_TYPE_ID', array_values($this->realCashPurchaseTransactionTypes))
            ->whereIn('PAYMENT_TRANSACTION_STATUS', [
                $this->depositOkSuccessPaymentStatus,
                $this->transactionSuccessPaymentStatus
            ])->limit(1)->count();

        if ($firstTimeDepositChk == 0) {
            return 422052; //You have not done any deposit transaction yet.
        }

        // if($this->getProfileStatus(getUserId()) != '0'){
        //     return 422047;// KYC not completed
        // }

        $amountAvailabelFromWin      = ($userCurrentRealBalance->win >= $request->amount  ? $request->amount : $userCurrentRealBalance->win);

        $amountAvailabelFromDeposit  = $request->amount - $amountAvailabelFromWin;

        $amountWillLeftInCurrentDeposit = $userCurrentRealBalance->deposit - $amountAvailabelFromDeposit;
        $amountWillLeftInCurrentWin = $userCurrentRealBalance->win - $amountAvailabelFromWin;
        $amountWillLeftInCurrentTotal = $amountWillLeftInCurrentWin + $amountWillLeftInCurrentDeposit + $userCurrentRealBalance->promo;

        if (!(abs($amountWillLeftInCurrentTotal - $amountAfterDeductingRequestedAmountFromTotal) < 0.00001)) {
            return 422053; // User Balance Mismatch
        }

        $isUserFlaggedRed = false;

        if ($amountAvailabelFromDeposit != 0) {
            $isUserFlaggedRed = true;
        }

        DB::beginTransaction();

        try {

            /*
            * ------------------------------------------------------------------
            * APPLYING TDS AND INSERTING DATA INTO WITHDRAW TRANSACTION HISTORY
            * ------------------------------------------------------------------
            */

            $applyTdsCalculation = $this->applyTdsCalculation($userCurrentRealBalance, $request->amount);
            $applyWithdrawFee = $this->applyWithdrawFee($request->amount, getUserId(), $request->txn_type);

            if (!$applyTdsCalculation) {
                return 422053; // User Balance Mismatch
            }

            /*
            * ----------------------------------------------------------------
            * INSERTING PAYMENT USER SETTINGS
            * ----------------------------------------------------------------
            */
            $paymentUserSetting = [
                'USER_ID' => getUserId(),
                'INTERNAL_REFERENCE_NO' => $internalReferenceNumber,
                'CHECKING_STATUS' => 1,
                'CHECKING_COMMENT' => "",
                'STATUS' => "",
                'CREATED_DATE' => date('Y-m-d H:i:s'),
                'UPDATED_DATE' => date('Y-m-d H:i:s')
            ];

            PaymentUserSetting::create($paymentUserSetting);
            /*
            * ----------------------------------------------------------------
            * INSERTING WITHDRAW TRANSACTION HISTORY
            * ----------------------------------------------------------------
            */

            $masterTransaction = new WithdrawTransactionHistory();
            $masterTransaction->USER_ID                = getUserId();
            $masterTransaction->BALANCE_TYPE_ID        = $applyTdsCalculation['balanceTypeId'];
            $masterTransaction->TRANSACTION_STATUS_ID  = self::WITHDRAW_PENDING_TRANSACTION_STATUS;
            $masterTransaction->TRANSACTION_TYPE_ID    = self::WITHDRAW_TRANSACTION_TYPE;
            $masterTransaction->WITHDRAW_AMOUNT        = $request->amount;
            $masterTransaction->WITHDRAW_TDS           = $applyTdsCalculation['withdrawTds'];
            $masterTransaction->WITHDRAW_FEE           = $applyTdsCalculation['withdrawFee'];
            $masterTransaction->PAYABLE_AMOUNT         = $applyTdsCalculation['paidAmount'] - ($applyWithdrawFee['is_withdraw_fee_exempted'] ? 0 : $applyWithdrawFee['withdraw_fee']);
            $masterTransaction->FEE_WAIVED             = 0;
            $masterTransaction->PAID_AMOUNT            = $applyTdsCalculation['paidAmount'] - ($applyWithdrawFee['is_withdraw_fee_exempted'] ? 0 : $applyWithdrawFee['withdraw_fee']);
            $masterTransaction->INTERNAL_REFERENCE_NO  = $internalReferenceNumber;
            $masterTransaction->GATEWAY_REFERENCE_NO   = $internalReferenceNumber;
            $masterTransaction->WITHDRAW_TYPE          = $request->txn_type; // 1--- for instant withdraw, 2---- for standard withdraw
            $masterTransaction->TRANSACTION_DATE       = date('Y-m-d H:i:s');
            $masterTransaction->APPROVED_AT            = "";
            $masterTransaction->APPROVED_BY            = "Normal";
            $masterTransaction->APPROVE_TYPE           = "";
            $masterTransaction->GATEWAY_STATUS         = "";
            $masterTransaction->CHECKING_STATUS        = "";
            $masterTransaction->AMOUNT_BREAKUP_DETAILS = json_encode([
                'DEDUCTED_FROM_WIN' => $applyTdsCalculation['deductedFromWin'],
                'DEDUCTED_FROM_DEPOSIT' => $applyTdsCalculation['deductedFromDeposit'],
                'APP_TYPE' => $request->app_type ?? null,
                'APP_INSTANCE_ID' => $request->app_instance_id ?? null,
                'TXN_VIA' => $request->txn_via
            ]);
            $masterTransaction->CHECKING_COMMENT       = ($isUserFlaggedRed ? "System has raised a red flag as fees is being deducted for this withdrawal." : "");
            $masterTransaction->APP_TYPE       = $request->app_type;
            $masterTransaction->WITHDRAW_CHARGE = $applyWithdrawFee['withdraw_fee'];
            $masterTransaction->DISCOUNTED_WITHDRAW_CHARGE = $applyWithdrawFee['is_withdraw_fee_exempted'] ? 0 : $applyWithdrawFee['withdraw_fee'];
            $masterTransaction->WITHDRAW_CHARGE_VALUE = $applyWithdrawFee['withdraw_charge_value']; 
            $masterTransaction->save();

            $withdrawTxnHistory = $masterTransaction;


            /*
            * ----------------------------------------------------------------
            * INSERTING INTO PAYMENT TRANSACTION
            * ----------------------------------------------------------------
            */
            $paymentTransaction = new PaymentTransaction();
            $paymentTransaction->USER_ID                          = getUserId();
            $paymentTransaction->PAYMENT_PROVIDER_ID              = $this->cardNetbankingUpiPaymentProviderType;
            $paymentTransaction->TRANSACTION_TYPE_ID              = self::WITHDRAW_TRANSACTION_TYPE;
            $paymentTransaction->PAYMENT_TRANSACTION_AMOUNT       = $request->amount;
            $paymentTransaction->PAYMENT_TRANSACTION_STATUS       = self::WITHDRAW_PENDING_TRANSACTION_STATUS;
            $paymentTransaction->PAYMENT_TRANSACTION_CREATED_BY   = getUserId();
            $paymentTransaction->PAYMENT_TRANSACTION_CREATED_ON   = date('Y-m-d H:i:s');
            $paymentTransaction->INTERNAL_REFERENCE_NO            = $internalReferenceNumber;
            $paymentTransaction->WITHDRAW_BY                      = self::WITHDRAW_BY_ONLINE;
            $paymentTransaction->WITHDRAW_TYPE                    = "Normal";
            $paymentTransaction->CHECKING_COMMENT                 = ($isUserFlaggedRed ? "System has raised a red flag as fees is being deducted for this withdrawal." : "");
            $paymentTransaction->APP_TYPE                         = $request->app_type;
            $paymentTransaction->save();




            /*
            * ----------------------------------------------------------------
            * INSERTING INTO MASTER TRANSACTION HISTORY
            * ----------------------------------------------------------------
            */

            $masterTransaction = new MasterTransactionHistory();
            $masterTransaction->USER_ID                = getUserId();
            $masterTransaction->BALANCE_TYPE_ID        = $applyTdsCalculation['balanceTypeId'];
            $masterTransaction->TRANSACTION_STATUS_ID  = self::WITHDRAW_PENDING_TRANSACTION_STATUS;
            $masterTransaction->TRANSACTION_TYPE_ID    = self::WITHDRAW_TRANSACTION_TYPE;
            $masterTransaction->TRANSACTION_AMOUNT     = $request->amount;
            $masterTransaction->TRANSACTION_DATE       = date('Y-m-d H:i:s');
            $masterTransaction->INTERNAL_REFERENCE_NO  = $internalReferenceNumber;
            $masterTransaction->CURRENT_TOT_BALANCE    = $userCurrentRealBalance->total;
            $masterTransaction->CLOSING_TOT_BALANCE    = $amountWillLeftInCurrentTotal;
            $masterTransaction->PARTNER_ID             = self::ADMIN_PARTNER_ID;
            $masterTransaction->save();

            /*
            * ----------------------------------------------------------------
            * UPDATING USER POINTS FOR RUMMY CASH WITHDRAW
            * ----------------------------------------------------------------
            */
            DB::select(
                'update user_points SET
                            `VALUE` = ?,
                            `USER_WIN_BALANCE` = `USER_WIN_BALANCE` - ?,
                            `USER_DEPOSIT_BALANCE` = `USER_DEPOSIT_BALANCE` - ?,
                            `USER_TOT_BALANCE` = `USER_WIN_BALANCE` + `USER_PROMO_BALANCE` + `USER_DEPOSIT_BALANCE`,
                            `UPDATED_DATE` = ?
                WHERE
                    `USER_ID` = ? AND
                    `COIN_TYPE_ID` = ?
                ',
                [
                    $request->amount,
                    $amountAvailabelFromWin,
                    $amountAvailabelFromDeposit,
                    date('Y-m-d H:i:s'),
                    getUserId(),
                    self::REAL_CASH_COIN_TYPE
                ]
            );

            /*
            * ----------------------------------------------------------------
            * TODO INSERT INTO payment_user_settings
            * ----------------------------------------------------------------
            */


            if ($isUserFlaggedRed && config('rummy_config.convertIntoRedFlag')) {
                User::where('USER_ID', getUserId())->update(['USER_FLAG' => 3]);
            }

            /*
            * ----------------------------------------------------------------
            * INSERTING USER ACTIVITY FOR RUMMY CASH WITHDRAW
            * ----------------------------------------------------------------
            */
            $trackingAction     = "Withdraw Successfully";
            $trackingData       = array("user_id" => getUserId(), "amount" => $request->amount);
            $this->userActivitiesTracking(getUserId(), $trackingAction, $trackingData);

            /*
            * -----------------------------------------------------------------------
            * INITIATING FOR AUTO PAYMENT APPROVAL IF USER IS GREEN OR YELLOW FLAGGED
            * -----------------------------------------------------------------------
            */
            $user = \RummyBaazi::user();

            /*Hitting clever tap event*/
            $this->trackingTableData = DB::table('tracking')
                ->select('DEVICE_MODEL', 'OPERATING_SYSTEM')
                ->where('USER_ID', $user->USER_ID)
                ->orderByDesc('TRACKING_ID')
                ->limit(1)
                ->first();

            $this->createCleverTapEvent([
                'userId' => $user->USER_ID,
                'event' => 'WITHDRAWAL',
                'evtData' => [
                    'USERNAME' => $user->USERNAME,
                    'STATUS' => 'PENDING',
                    'DEVICE' => $this->trackingTableData->DEVICE_MODEL . '(' . $this->trackingTableData->OPERATING_SYSTEM . ')',
                    'TIMESTAMP' => time(),
                    'USER_ID' => (string)$user->USER_ID,
                    'AMOUNT' => (float)$request->amount,
                    'TDS' => (float)$applyTdsCalculation['withdrawTds'],
                    'FEE'   => (float)$applyTdsCalculation['withdrawFee'],
                    'ACTUAL_CHARGE' => (float)$applyWithdrawFee['withdraw_fee'],
                    'DISCOUNTED_CHARGE' => (float)($applyWithdrawFee['is_withdraw_fee_exempted'] ? 0 : $applyWithdrawFee['withdraw_fee']),
                    'WITHDRAW_TYPE' => $request->txn_type == 1 ? 'INSTANT' : 'STANDARD',
                    'app_type' => $withdrawTxnHistory->APP_TYPE
                ]
            ]);

            /*end*/

            if (
                (
                    ($user->USER_FLAG == self::YELLOW_FLAG &&
                        config('rummy_config.instantPayoutFlagConsider.yellow') &&
                        ($request->amount + $this->getDailyTotalInstantWithdrawAmount()) <= config('rummy_config.instantWithdrawMaxCapping.yellowPerDay') &&
                        $request->amount <= config('rummy_config.instantWithdrawMaxCapping.yellowPerTrans')
                    )
                    ||
                    ($user->USER_FLAG == self::GREEN_FLAG &&
                        config('rummy_config.instantPayoutFlagConsider.green') &&
                        ($request->amount + $this->getDailyTotalInstantWithdrawAmount()) <= config('rummy_config.instantWithdrawMaxCapping.greenPerDay') &&
                        $request->amount <= config('rummy_config.instantWithdrawMaxCapping.greenPerTrans')
                    )
                )
                &&
                $applyTdsCalculation['withdrawFee'] <= 0
                &&
                $request->txn_type == '1' //Process Withdraw Directly In Case Of Instant Withdraw Only
            ) {
                $isDirectTransferedSuccessfully = $this->initiatePaymentRequestForGreenFlagUser($internalReferenceNumber, $request);

                if ($isDirectTransferedSuccessfully) {
                    /*Sending Mail*/

                    // CB-823 (if - else logic only)
                    $checkEmailId = EmailerPreventedEmail::where('EMAIL_ID', $user->EMAIL_ID)->get();
                    if (count($checkEmailId) > 0) {
                        Log::info("Emails not sent as email id of user " . $user->EMAIL_ID . " is in bounce restricted email list. Transaction ID: " . $internalReferenceNumber);
                    } else {
                        if ($user->EMAIL_VERIFY == 1) {
                            $commonDiv = $this->getCommonDiv($withdrawTxnHistory);
                            $this->sendMail([
                                "email" => $user->EMAIL_ID,
                                "username" => $user->USERNAME,
                                "emailkey" => 'withdrawal_request_approved',
                                "userId" => getUserId(),
                                "amount" => number_format($request->amount, 2, '.', ''),
                                "withdrawRequestType" => $withdrawTxnHistory->WITHDRAW_TYPE == 1 ? 'Instant Withdrawal Request' : 'Standard Withdrawal Request',
                                'commonDiv' => $commonDiv                                
                            ]);
                        }
                    }

                    DB::commit();
                    return 10001; // Transaction Pending
                }

                /*-------------------------- UPDATING THE WITHDRAW TRANSACTION HISTORY STATUS TO REJECTED -----------*/
                $this->rejectWithdraw($internalReferenceNumber, $user);  
                /**
                 * Update Withdraw Status to Fail If Cashfree Transafer is not successfull.
                 */

                /*-------------------------- UPDATING THE WITHDRAW TRANSACTION HISTORY STATUS TO FAILED -----------*/
                // WithdrawTransactionHistory::where('INTERNAL_REFERENCE_NO', $internalReferenceNumber)
                // ->update([
                //     'TRANSACTION_STATUS_ID' => self::WITHDRAW_FAILED_TRANSACTION_STATUS
                // ]);
            
                /*-------------------------- UPDATING THE MASTER TRANSACTION HISTORY STATUS TO FAILED -----------*/
                // MasterTransactionHistory::where('INTERNAL_REFERENCE_NO', $internalReferenceNumber)
                // ->update([
                //     'TRANSACTION_STATUS_ID' => self::WITHDRAW_FAILED_TRANSACTION_STATUS
                // ]);
            
                /*-------------------------- UPDATING THE PAYMENT TRANSACTION STATUS TO FAILED -----------*/
                // PaymentTransaction::where('INTERNAL_REFERENCE_NO', $internalReferenceNumber)
                // ->update([
                //     'PAYMENT_TRANSACTION_STATUS' => self::WITHDRAW_FAILED_TRANSACTION_STATUS
                // ]);
            
                /*Sending Mail*/

                // CB-823 (if - else logic only)
                $checkEmailId = EmailerPreventedEmail::where('EMAIL_ID', $user->EMAIL_ID)->get();

                if (count($checkEmailId) > 0) {
                    Log::info("Emails not sent as email id of user " . $user->EMAIL_ID . " is in bounce restricted email list. Transaction ID: " . $internalReferenceNumber);
                } else if ($user->EMAIL_VERIFY == 1) {
                    $commonDiv = $this->getCommonDiv($withdrawTxnHistory);
                    $this->sendMail([
                        "email" => $user->EMAIL_ID,
                        "username" => $user->USERNAME,
                        "emailkey" => 'withdrawal_request_failed',
                        "userId" => getUserId(),
                        "amount" => number_format($request->amount, 2, '.', ''),
                        "withdrawRequestType" => $withdrawTxnHistory->WITHDRAW_TYPE == 1 ? 'instant withdrawal request' : 'standard withdrawal request',
                        'commonDiv' => $commonDiv 
                    ]);
                }

                /*Hitting CleverTap*/
                $this->createCleverTapEvent([
                    'userId' => $user->USER_ID,
                    'event' => 'WITHDRAWAL',
                    'evtData' => [
                        'USERNAME' => $user->USERNAME,
                        'STATUS' => 'FAILED',
                        'DEVICE' => $this->trackingTableData->DEVICE_MODEL . '(' . $this->trackingTableData->OPERATING_SYSTEM . ')',
                        'TIMESTAMP' => time(),
                        'USER_ID' => (string)$user->USER_ID,
                        'AMOUNT' => (float)$request->amount,
                        'TDS' => (float)$applyTdsCalculation['withdrawTds'],
                        'FEE'   => (float)$applyTdsCalculation['withdrawFee'],
                        'ACTUAL_CHARGE' => (float)$applyWithdrawFee['withdraw_fee'],
                        'DISCOUNTED_CHARGE' => (float)($applyWithdrawFee['is_withdraw_fee_exempted'] ? 0 : $applyWithdrawFee['withdraw_fee']),
                        'WITHDRAW_TYPE' => $request->txn_type == 1 ? 'INSTANT' : 'STANDARD',
                        'app_type' => $withdrawTxnHistory->APP_TYPE
                    ]
                ]);

                $this->pushEventToGA($internalReferenceNumber, self::WITHDRAW_FAILED_TRANSACTION_STATUS);

                DB::commit();
                return 422055; //Amount Transfer Failed From Cash Free
            }

            /**
             * In Case Of Standard Withdraw or Yellow flag & red flag limit Put Withdraw In Pending Status For Processing from BO
             */

            /*Sending Mail*/
            DB::commit();

            // CB-823 (if - else logic only)
            $checkEmailId = EmailerPreventedEmail::where('EMAIL_ID', $user->EMAIL_ID)->get();
            if (count($checkEmailId) > 0) {
                Log::info("Emails not sent as email id of user " . $user->EMAIL_ID . " is in bounce restricted email list. Transaction ID: " . $internalReferenceNumber);
            } else if ($user->EMAIL_VERIFY == 1) {
                $commonDiv = $this->getCommonDiv($withdrawTxnHistory);
                $this->sendMail([
                    "email" => $user->EMAIL_ID,
                    "username" => $user->USERNAME,
                    "emailkey" => 'withdrawal_request_received',
                    "userId" => getUserId(),
                    "amount" => number_format($request->amount, 2, '.', ''),
                    "withdrawRequestType" => $withdrawTxnHistory->WITHDRAW_TYPE == 1 ? 'Instant Withdrawal Request' : 'Standard Withdrawal Request',
                    'commonDiv' => $commonDiv 
                ]);
            }
            
            return 10001; // Transaction Pending
        } catch (\Exception $e) {
            DB::rollBack();
            $this->userActivitiesTracking(getUserId(), 'CASH_WITHDRAW_DB_TRANSACTION_FAILED', $e->getMessage());
            Log::error($e);
            return 422051; // Error While Performing Withdraw Transaction
        }
    }


    /*
    * ---------------------------------------------------------------------------------------
    * initiatePaymentRequestForGreenFlagUser : if user is green or yellow flagged then initiate request
    * for instant appoval of payment to payment gateway.
    * @param $internalReferenceNumber
    * @return Int (code of response)
    * <AUTHOR> Aloney <<EMAIL>>
    * --------------------------------------------------------------------------------------
    */

    private function initiatePaymentRequestForGreenFlagUser(String $internalReferenceNumber, Request $request)
    {


        $allProcessFlag = true; // to ckeck till end

        $transactionAmount = WithdrawTransactionHistory::where([
            'USER_ID' => getUserId(),
            'TRANSACTION_TYPE_ID' => self::WITHDRAW_TRANSACTION_TYPE,
            'TRANSACTION_STATUS_ID' => self::WITHDRAW_PENDING_TRANSACTION_STATUS,
            'INTERNAL_REFERENCE_NO' => $internalReferenceNumber
        ])->sum('PAYABLE_AMOUNT');


        $userAccountDetails = UserAccountDetail::where('USER_ID', getUserId())->first();

        /*-------------------------- UPDATING THE WITHDRAW TRANSACTION HISTORY STATUS TO INITIATED -----------*/
        WithdrawTransactionHistory::where('INTERNAL_REFERENCE_NO', $internalReferenceNumber)
            ->update([
                'TRANSACTION_STATUS_ID' => self::WITHDRAW_INITIATED_TRANSACTION_STATUS,
                'APPROVE_TYPE' => 'cashfree_greenflag',
                'GATEWAY_REFERENCE_NO' => $internalReferenceNumber,
                'APPROVE_DATE' => date('Y-m-d H:i:s')
            ]);

        /*-------------------------- UPDATING THE MASTER TRANSACTION HISTORY STATUS TO INITIATED -----------*/
        MasterTransactionHistory::where('INTERNAL_REFERENCE_NO', $internalReferenceNumber)
            ->update(['TRANSACTION_STATUS_ID' => self::WITHDRAW_INITIATED_TRANSACTION_STATUS]);

        /*-------------------------- UPDATING THE PAYMENT TRANSACTION STATUS TO INITIATED -----------*/
        PaymentTransaction::where('INTERNAL_REFERENCE_NO', $internalReferenceNumber)
            ->update([
                'APPROVE_BY_CASHFREE_STATUS' => self::APPROVED_BY_CASHFREE,
                'TRANSFER_ID' => $internalReferenceNumber,
                'PAYMENT_TRANSACTION_STATUS' => self::WITHDRAW_INITIATED_TRANSACTION_STATUS
            ]);



        /*-------------------------- PREPARING PAY LOAD FOR CASH FREE -----------------------------*/
        $authUser = \RummyBaazi::user();
        $cashFreePayLoad = array(
            'USER_ID'               =>  getUserId(),
            'EMAIL_ID'              =>  $authUser->EMAIL_ID,
            'API_URL'               =>  config('rummy_config.cashfree.payoutUrl'),
            'CLIENT_ID'             =>  config('rummy_config.cashfree.payoutClientId'),
            'CLIENT_SECRET'         =>  config('rummy_config.cashfree.payoutClientSec'),
            'TRANSFER_ID'           =>  $internalReferenceNumber,
            'INTERNAL_REFERENCE_NO'  => $internalReferenceNumber,
            'ACCOUNT_HOLDER_NAME'   =>  $userAccountDetails->ACCOUNT_HOLDER_NAME,
            'ACCOUNT_NUMBER'        =>  $userAccountDetails->ACCOUNT_NUMBER,
            'IFSC_CODE'             =>  $userAccountDetails->IFSC_CODE,
            'TRANSFER_AMOUNT'       =>  $transactionAmount,
            'CONTACT'               =>  $authUser->CONTACT,
            'ADDRESS'               =>  $authUser->ADDRESS,
            'UPI_ID'                =>  $userAccountDetails->UPI_ID,
            'TXN_VIA'               =>  $request->txn_via
        );

        /*------------------------------ end ------------------------------------------------------*/

        /*-------------------------- PREPARING TRACKING DATA FOR USER ACTIVITY TRACKING -----------*/
        $trackingData['USER_ID']                =   getUserId();
        $trackingData['AMOUNT']                 =   $transactionAmount;
        $trackingData['INTERNAL_REFERENCE_NO']  =   $internalReferenceNumber;
        $trackingData['TRANSFER_ID']            =   $internalReferenceNumber;
        $trackingData['PAYMENT_STATUS']         =   self::WITHDRAW_PENDING_TRANSACTION_STATUS;
        $trackingData["ACCOUNT_HOLDER_NAME"]    =   $userAccountDetails->ACCOUNT_HOLDER_NAME;
        $trackingData["ACCOUNT_NUMBER"]         =   $userAccountDetails->ACCOUNT_NUMBER;
        $trackingData["IFSC_CODE"]              =   $userAccountDetails->IFSC_CODE;
        $trackingData["UPI_ID"]                 =   $userAccountDetails->UPI_ID;
        $trackingData["TXN_VIA"]                =   $request->txn_via;
        /*------------------------------ end ------------------------------------------------------*/

        //----------------------------------------------------------
        // COMMITING DB TRANSACTION BEFORE INITIATING TO CASH FREE
        //-----------------------------------------------------------
        DB::commit();

        $withDrawData = WithdrawTransactionHistory::where([
            'INTERNAL_REFERENCE_NO' =>  $internalReferenceNumber,
            'TRANSACTION_TYPE_ID' => self::WITHDRAW_TRANSACTION_TYPE
        ])->first();
        /*Hitting CleverTap*/
        $this->createCleverTapEvent([
            'userId' => $authUser->USER_ID,
            'event' => 'WITHDRAWAL',
            'evtData' => [
                'USERNAME' => $authUser->USERNAME,
                'STATUS' => 'INITIATED',
                'DEVICE' => $this->trackingTableData->DEVICE_MODEL . '(' . $this->trackingTableData->OPERATING_SYSTEM . ')',
                'TIMESTAMP' => time(),
                'USER_ID' => (string)$authUser->USER_ID,
                'AMOUNT' => (float)$withDrawData->WITHDRAW_AMOUNT,
                'TDS' => (float)$withDrawData->WITHDRAW_TDS,
                'FEE'   => (float)$withDrawData->WITHDRAW_FEE,
                'WITHDRAW_TYPE' => $request->txn_type == 1 ? 'INSTANT' : 'STANDARD',
                'app_type' => $withDrawData->APP_TYPE
            ]
        ]);

        /*
        *--------------------------------------
        *   LEDGER WITHDRAW
        *--------------------------------------
        */
        //$this->updatePlayerLedgerWithdraw($internalReferenceNumber, self::WITHDRAW_INITIATED_TRANSACTION_STATUS);

        /*
        *--------------------------------------
        *   Updating Player Ledger
        *--------------------------------------
        */
        if($request->type == 'cash'){
            $this->reconcilePlayerLedger( getUserId(), 'WITHDRAWAL', $internalReferenceNumber);
        }
        /*
        *--------------------------------------
        *   Updating Player Ledger End
        *--------------------------------------
        */

        /*
        *--------------------------------------
        *   SENDING REQUEST TO PAYMENT GATEWAY
        *--------------------------------------
        */
        $cashFreeResponse = $this->initiateRequestToCashFree($cashFreePayLoad);

        if (($cashFreeResponse['status'] == 'success') || ($cashFreeResponse['status'] == 'pending')) {
            $trackingData['PAYMENT_STATUS'] = self::WITHDRAW_PENDING_TRANSACTION_STATUS;
            $trackingData['CASHFREE_STATUS'] = $cashFreeResponse["status"];
            $trackingData['CASHFREE_SUB_CODE'] = $cashFreeResponse["subCode"];
            $trackingData['CASHFREE_MESSAGE'] = $cashFreeResponse["message"];
            $trackingAction = 'Green flag user direct withdrawl pending';
        } else {
            $trackingData['PAYMENT_STATUS'] = self::WITHDRAW_FAILED_TRANSACTION_STATUS;
            $trackingData['CASHFREE_STATUS'] = $cashFreeResponse["status"];
            $trackingData['CASHFREE_SUB_CODE'] = $cashFreeResponse["subCode"];
            $trackingData['CASHFREE_MESSAGE'] = $cashFreeResponse["message"];
            $trackingAction = 'Green flag user direct withdrawl failed';

            $allProcessFlag = false;
        }

        /*
        *---------------------------------------------------
        *   INSERTING DATA IN CASH OUT APPROVE TRANSACTION
        *---------------------------------------------------
        */
        $gatewayRequest = json_encode($trackingData);
        $gatewayResponse = json_encode($cashFreeResponse);

        $cashoutApproveTransaction = new CashoutApproveTransaction();

        $cashoutApproveTransaction->USER_ID                          = $trackingData['USER_ID'];
        $cashoutApproveTransaction->TRANSACTION_AMOUNT               = $trackingData['AMOUNT'];
        $cashoutApproveTransaction->INTERNAL_REFERENCE_NO            = $trackingData['INTERNAL_REFERENCE_NO'];
        $cashoutApproveTransaction->GATEWAY_REFERENCE_NO             = $trackingData['TRANSFER_ID'];
        $cashoutApproveTransaction->ACCOUNT_HOLDER_NAME              = $trackingData['ACCOUNT_HOLDER_NAME'];
        $cashoutApproveTransaction->ACCOUNT_NUMBER                   = $trackingData['ACCOUNT_NUMBER'];
        $cashoutApproveTransaction->IFSC_CODE                        = $trackingData['IFSC_CODE'];
        $cashoutApproveTransaction->GATEWAY_STATUS                   = $trackingData['CASHFREE_STATUS'];
        $cashoutApproveTransaction->GATEWAY_REQUEST                  = $gatewayRequest;
        $cashoutApproveTransaction->GATEWAY_RESPONSE                 = $gatewayResponse;
        $cashoutApproveTransaction->GATEWAY_MESSAGE                  = $trackingData['CASHFREE_MESSAGE'];
        $cashoutApproveTransaction->TYPE                             = "cashfree_greenflag";
        $cashoutApproveTransaction->REQUEST_DATE                     = date('Y-m-d H:i:s');
        $cashoutApproveTransaction->save();

        /*
        *---------------------------------------------------
        *   INSERTING INTO USER ACITIVITY TRACKING
        *---------------------------------------------------
        */
        $this->userActivitiesTracking(getUserId(), $trackingAction, $trackingData);
        return $allProcessFlag;
    }


    /*
    * -----------------------------------------------------------------------------------
    * initiateRequestToCashFree : initiate a request to cash free api for direct transfer
    * @param Array $cashFreePayLoad
    * @return Array subCode,status and message
    * <AUTHOR> Aloney <<EMAIL>>
    * ----------------------------------------------------------------------------------
    */
    private function initiateRequestToCashFree(array $cashFreePayLoad)
    {

        $basePayOutUrl = config('rummy_config.cashfree.payoutUrl');

        $headers = array(
            "X-Client-Id: " . config('rummy_config.cashfree.payoutClientId'),
            "X-Client-Secret: " . config('rummy_config.cashfree.payoutClientSec'),
        );

        $cashFreeAuthorizeUrl =  $basePayOutUrl . "/authorize";

        try {

            $authorizeResp = $this->postCurl($cashFreeAuthorizeUrl, $headers);

            if ($authorizeResp['status'] == "SUCCESS") {
                try {
                    $cashFreeTokenVerificationUrl    = $basePayOutUrl . "/verifyToken";
                    $accessToken            = $authorizeResp["data"]["token"];
                    $accessTokenheaders     = array("Authorization: Bearer $accessToken");
                    $verifyTokenResponse    = $this->postCurl($cashFreeTokenVerificationUrl, $accessTokenheaders);

                    if ($verifyTokenResponse['status'] == 'SUCCESS') {
                        try {
                            $cashFreeGetBalanceUrl    = $basePayOutUrl . "/getBalance";
                            $getBalanceResponse = $this->getCurl($cashFreeGetBalanceUrl, $accessTokenheaders);

                            if ($getBalanceResponse['status'] == 'SUCCESS') {
                                if ($cashFreePayLoad["TRANSFER_AMOUNT"] <= $getBalanceResponse["data"]["availableBalance"]) {
                                    try {
                                        $cashFreeGetBeneficiaryUrl = $basePayOutUrl . "/getBeneficiary/" . getUserId();
                                        $getBeneficiaryResponse = $this->getCurl($cashFreeGetBeneficiaryUrl, $accessTokenheaders);

                                        /*------------------------------ Preparing Baneficiary Data -------------------------*/
                                        $authUser = \RummyBaazi::user();
                                        $addBeneficiaryData["beneId"]            = getUserId();
                                        $addBeneficiaryData["name"]              = $cashFreePayLoad["ACCOUNT_HOLDER_NAME"];
                                        $addBeneficiaryData["email"]             = $authUser->EMAIL_ID;
                                        $addBeneficiaryData["phone"]             = $authUser->CONTACT;
                                        $addBeneficiaryData["bankAccount"]       = $cashFreePayLoad["ACCOUNT_NUMBER"];
                                        $addBeneficiaryData["ifsc"]              = $cashFreePayLoad["IFSC_CODE"];
                                        $addBeneficiaryData["address1"]          = $authUser->ADDRESS;
                                        $addBeneficiaryData["vpa"]               = $cashFreePayLoad["UPI_ID"];
                                        /*------------------------------ End ------------------------------------------------*/

                                        /*------------------------------ Preparing Amount Transfer Data -------------------------*/
                                        $tranferBeneficiaryData["beneId"]        = getUserId();
                                        $tranferBeneficiaryData["amount"]        = $cashFreePayLoad["TRANSFER_AMOUNT"];
                                        $tranferBeneficiaryData["transferId"]    = $cashFreePayLoad["TRANSFER_ID"];
                                        $tranferBeneficiaryData["transferMode"]  = $cashFreePayLoad["TXN_VIA"];
                                        $tranferBeneficiaryData["remarks"]       = "cash free transfer to " . getUserId();
                                        /*----------------------------------- End ------------------------------------------------*/

                                        if ($getBeneficiaryResponse['status'] == 'SUCCESS') {
                                            if (($getBeneficiaryResponse["data"]["bankAccount"] == $addBeneficiaryData["bankAccount"]) && ($getBeneficiaryResponse["data"]["ifsc"] == $addBeneficiaryData["ifsc"]) && ($getBeneficiaryResponse["data"]["name"] == $addBeneficiaryData["name"]) && ($getBeneficiaryResponse["data"]["vpa"] == $addBeneficiaryData["vpa"])) {
                                                /*
                                                *-----------------------------
                                                *   BANEFICIARY EXISTS
                                                *-----------------------------
                                                */
                                                try {
                                                    /*
                                                    *-----------------------------
                                                    *   TRANSFERING AMOUNT
                                                    *-----------------------------
                                                    */

                                                    $cashFreeTransferRequestUrl = $basePayOutUrl . "/requestTransfer";
                                                    $transferBeneficiaryResponse = $this->postCurl($cashFreeTransferRequestUrl, $accessTokenheaders, $tranferBeneficiaryData);

                                                    $this->userActivitiesTracking(getUserId(), 'REQUESTED_WITHDRAW_FROM_CASHFREE_TRANSFER', ['responseFromCashfree' => $transferBeneficiaryResponse]);

                                                    if (strtoupper($transferBeneficiaryResponse['status']) == 'SUCCESS') {
                                                        return [
                                                            'status' => 'success',
                                                            'message' => 'Amount Transfered successfully.',
                                                            'subCode' => $transferBeneficiaryResponse['subCode']
                                                        ];
                                                    } elseif (strtoupper($transferBeneficiaryResponse['status']) == 'PENDING') {
                                                        return [
                                                            'status' => 'pending',
                                                            'message' => 'Cash Free Transfer Amount Pending (On Baneficiary Already Exists): ' . $transferBeneficiaryResponse['message'],
                                                            'subCode' => $transferBeneficiaryResponse['subCode']
                                                        ];
                                                    } elseif (strtoupper($transferBeneficiaryResponse['status']) == '') {
                                                        return [
                                                            'status' => 'pending',
                                                            'message' => 'Cash Free Transfer Amount Pending (On Baneficiary Already Exists): No response getway timeout maybe',
                                                            'subCode' => $transferBeneficiaryResponse['subCode']
                                                        ];
                                                    } else {
                                                        return [
                                                            'status' => 'failed',
                                                            'message' => 'Cash Free Transfer Amount Failed (On Baneficiary Already Exists): ' . $transferBeneficiaryResponse['message'],
                                                            'subCode' => $transferBeneficiaryResponse['subCode']
                                                        ];
                                                    }
                                                } catch (\Exception $e) {
                                                    return [
                                                        'status' => 'failed',
                                                        'message' => 'Cash Free Transfer Amount CURL Failed (On Baneficiary Already Exists): ' . $e->getMessage(),
                                                        'subCode' => 000
                                                    ];
                                                }
                                            } else {
                                                /*
                                                *-----------------------------------------------------------------------------------
                                                *   IN CASE BANEFICIARY EXISTS BUT CURRENT DETAILS DOES NOT MATCH WITH THE ADDED ONE
                                                *   THEN FIRST REMOVING THE BANEFICIARY FROM CASH FREE AND ADDING IT AGAIN WITH NEW
                                                *   DETAILS AND DOING THE TRANSFER PROCESS.
                                                *-----------------------------------------------------------------------------------
                                                */
                                                try {
                                                    $cashFreeRemoveBaneficiaryUrl            = $basePayOutUrl . "/removeBeneficiary";
                                                    $removeBeneficiaryData["beneId"]         = getUserId();
                                                    $removeBeneficiaryResponse = $this->postCurl($cashFreeRemoveBaneficiaryUrl, $accessTokenheaders, $removeBeneficiaryData);

                                                    $this->userActivitiesTracking(getUserId(), 'REQUESTED_TO_REMOVE_BENI_FROM_CASHFREE', ['responseFromCashfree' => $removeBeneficiaryResponse]);

                                                    if ($removeBeneficiaryResponse['status'] == 'SUCCESS') {
                                                        /*
                                                        *-----------------------------
                                                        *   BANEFICIARY ADDING AGAIN
                                                        *-----------------------------
                                                        */
                                                        try {
                                                            $cashFreeAddBeneficiaryUrl = $basePayOutUrl . "/addBeneficiary";
                                                            $addBeneficiaryResponse = $this->postCurl($cashFreeAddBeneficiaryUrl, $accessTokenheaders, $addBeneficiaryData);

                                                            $this->userActivitiesTracking(getUserId(), 'REQUESTED_TO_ADD_BENI_AGAIN_TO_CASHFREE', ['responseFromCashfree' => $addBeneficiaryResponse]);


                                                            if ($addBeneficiaryResponse['status'] == 'SUCCESS') {
                                                                try {
                                                                    /*
                                                                    *-----------------------------
                                                                    *   TRANSFERING AMOUNT
                                                                    *-----------------------------
                                                                    */

                                                                    $cashFreeTransferRequestUrl = $basePayOutUrl . "/requestTransfer";
                                                                    $transferBeneficiaryResponse = $this->postCurl($cashFreeTransferRequestUrl, $accessTokenheaders, $tranferBeneficiaryData);

                                                                    $this->userActivitiesTracking(getUserId(), 'REQUESTED_WITHDRAW_FROM_CASHFREE_TRANSFER', ['responseFromCashfree' => $transferBeneficiaryResponse]);

                                                                    if (strtoupper($transferBeneficiaryResponse['status']) == 'SUCCESS') {
                                                                        return [
                                                                            'status' => 'success',
                                                                            'message' => 'Amount Transfered successfully.',
                                                                            'subCode' => $transferBeneficiaryResponse['subCode']
                                                                        ];
                                                                    } elseif (strtoupper($transferBeneficiaryResponse['status']) == 'PENDING') {
                                                                        return [
                                                                            'status' => 'pending',
                                                                            'message' => 'Cash Free Transfer Amount Pending (On Baneficiary Already Exists): ' . $transferBeneficiaryResponse['message'],
                                                                            'subCode' => $transferBeneficiaryResponse['subCode']
                                                                        ];
                                                                    } elseif (strtoupper($transferBeneficiaryResponse['status']) == '') {
                                                                        return [
                                                                            'status' => 'pending',
                                                                            'message' => 'Cash Free Transfer Amount Pending (On Baneficiary Already Exists): No response getway timeout maybe',
                                                                            'subCode' => $transferBeneficiaryResponse['subCode']
                                                                        ];
                                                                    } else {
                                                                        return [
                                                                            'status' => 'failed',
                                                                            'message' => 'Cash Free Transfer Amount Failed (On Baneficiary After Removal): ' . $transferBeneficiaryResponse['message'],
                                                                            'subCode' => $transferBeneficiaryResponse['subCode']
                                                                        ];
                                                                    }
                                                                } catch (\Exception $e) {
                                                                    return [
                                                                        'status' => 'failed',
                                                                        'message' => 'Cash Free Transfer Amount CURL Failed (On Baneficiary After Removal) : ' . $e->getMessage(),
                                                                        'subCode' => 000
                                                                    ];
                                                                }
                                                            } else {
                                                                return [
                                                                    'status' => 'failed',
                                                                    'message' => 'Cash Free Add Beneficiary Failed (On Baneficiary After Removal): ' . $addBeneficiaryResponse['message'],
                                                                    'subCode' => $addBeneficiaryResponse['subCode']
                                                                ];
                                                            }
                                                        } catch (\Exception $e) {
                                                            return [
                                                                'status' => 'failed',
                                                                'message' => 'Cash Free Add Beneficiary CURL Failed (On Baneficiary After Removal) : ' . $e->getMessage(),
                                                                'subCode' => 000
                                                            ];
                                                        }
                                                    } else {
                                                        return [
                                                            'status' => 'failed',
                                                            'message' => 'Cash Free Beneficiary Removal Failed: ' . $removeBeneficiaryResponse['message'],
                                                            'subCode' => $removeBeneficiaryResponse['subCode']
                                                        ];
                                                    }
                                                } catch (\Exception $e) {
                                                    return [
                                                        'status' => 'failed',
                                                        'message' => 'Cash Free Beneficiary Removal CURL Failed: ' . $e->getMessage(),
                                                        'subCode' => 000
                                                    ];
                                                }
                                            }
                                        } else {

                                            if ($getBeneficiaryResponse['subCode'] == 404) {
                                                /*
                                                *----------------------------------------------------
                                                *   ADDING NEW BANEFICIARY AND TRANSFERING THE AMOUNT
                                                *----------------------------------------------------
                                                */
                                                try {

                                                    $cashFreeAddBeneficiaryUrl = $basePayOutUrl . "/addBeneficiary";
                                                    $addBeneficiaryResponse = $this->postCurl($cashFreeAddBeneficiaryUrl, $accessTokenheaders, $addBeneficiaryData);

                                                    $this->userActivitiesTracking(getUserId(), 'REQUESTED_TO_ADD_NEW_BENI_TO_CASHFREE', ['responseFromCashfree' => $addBeneficiaryResponse]);

                                                    if ($addBeneficiaryResponse['status'] == 'SUCCESS') {
                                                        try {
                                                            /*
                                                            *-----------------------------
                                                            *   TRANSFERING AMOUNT
                                                            *-----------------------------
                                                            */

                                                            $cashFreeTransferRequestUrl = $basePayOutUrl . "/requestTransfer";
                                                            $transferBeneficiaryResponse = $this->postCurl($cashFreeTransferRequestUrl, $accessTokenheaders, $tranferBeneficiaryData);

                                                            $this->userActivitiesTracking(getUserId(), 'REQUESTED_WITHDRAW_FROM_CASHFREE_TRANSFER', ['responseFromCashfree' => $transferBeneficiaryResponse]);

                                                            if (strtoupper($transferBeneficiaryResponse['status']) == 'SUCCESS') {
                                                                return [
                                                                    'status' => 'success',
                                                                    'message' => 'Amount Transfered successfully.',
                                                                    'subCode' => $transferBeneficiaryResponse['subCode']
                                                                ];
                                                            } elseif (strtoupper($transferBeneficiaryResponse['status']) == 'PENDING') {
                                                                return [
                                                                    'status' => 'pending',
                                                                    'message' => 'Cash Free Transfer Amount Pending : ' . $transferBeneficiaryResponse['message'],
                                                                    'subCode' => $transferBeneficiaryResponse['subCode']
                                                                ];
                                                            } elseif (strtoupper($transferBeneficiaryResponse['status']) == '') {
                                                                return [
                                                                    'status' => 'pending',
                                                                    'message' => 'Cash Free Transfer Amount Pending : No response getway timeout maybe',
                                                                    'subCode' => ''
                                                                ];
                                                            } else {
                                                                return [
                                                                    'status' => 'failed',
                                                                    'message' => 'Cash Free Transfer Amount Failed : ' . $transferBeneficiaryResponse['message'],
                                                                    'subCode' => $transferBeneficiaryResponse['subCode']
                                                                ];
                                                            }
                                                        } catch (\Exception $e) {
                                                            return [
                                                                'status' => 'failed',
                                                                'message' => 'Cash Free Transfer Amount CURL Failed : ' . $e->getMessage(),
                                                                'subCode' => 000
                                                            ];
                                                        }
                                                    } else {
                                                        return [
                                                            'status' => 'failed',
                                                            'message' => 'Cash Free Add Beneficiary Failed : ' . $addBeneficiaryResponse['message'],
                                                            'subCode' => $addBeneficiaryResponse['subCode']
                                                        ];
                                                    }
                                                } catch (\Exception $e) {
                                                    return [
                                                        'status' => 'failed',
                                                        'message' => 'Cash Free Add Beneficiary CURL Failed : ' . $e->getMessage(),
                                                        'subCode' => 000
                                                    ];
                                                }
                                            }

                                            return [
                                                'status' => 'failed',
                                                'message' => 'Cash Free Get Beneficiary Failed : ' . $getBeneficiaryResponse['message'],
                                                'subCode' => $getBeneficiaryResponse['subCode']
                                            ];
                                        }
                                    } catch (\Exception $e) {
                                        return [
                                            'status' => 'failed',
                                            'message' => 'Cash Free Get Beneficiary CURL Failed : ' . $e->getMessage(),
                                            'subCode' => 000
                                        ];
                                    }
                                } else {
                                    return [
                                        'status' => 'failed',
                                        'message' => 'Cash Free Get Balance Failed : Insufficiant Balance',
                                        'subCode' => 000
                                    ];
                                }
                            } else {
                                return [
                                    'status' => 'failed',
                                    'message' => 'Cash Free Get Balance Failed : ' . $getBalanceResponse['message'],
                                    'subCode' => $getBalanceResponse['subCode']
                                ];
                            }
                        } catch (\Exception $e) {
                            return [
                                'status' => 'failed',
                                'message' => 'Cash Free Get Balance CURL Failed : ' . $e->getMessage(),
                                'subCode' => 000
                            ];
                        }
                    } else {
                        return [
                            'status' => 'failed',
                            'message' => 'Cash Free Token Varification Failed : ' . $verifyTokenResponse['message'],
                            'subCode' => $verifyTokenResponse['subCode']
                        ];
                    }
                } catch (\Exception $e) {
                    return [
                        'status' => 'failed',
                        'message' => 'Cash Free Token Varification CURL Failed : ' . $e->getMessage(),
                        'subCode' => 000
                    ];
                }
            } else {
                return [
                    'status' => 'failed',
                    'message' => 'Cash Free Authorization Failed : ' . $authorizeResp['message'],
                    'subCode' => $authorizeResp['subCode']
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'failed',
                'message' => 'Cash Free Authorization CURL Failed : ' . $e->getMessage(),
                'subCode' => 000
            ];
        }
    }

    /*
    * ---------------------------------------------------------------------------------------
    * applyTdsCalculation : procedure for applying TDS and FEE on cash withdraw
    * @param userCurrentBalance, $requestedAmount
    * @return void
    * <AUTHOR> Aloney <<EMAIL>>
    * -----------------------------------------------------------------------------------------
    */

    private function applyTdsCalculation(Object $userCurrentBalance, Float $requestedAmount)
    {

        $balanceTypeId = 0;
        $user = \RummyBaazi::user();
        $previousLedgerResult   =   PlayerLedger::select('TOTAL_ELIGIBLE_WITHDRAWAL_WITHOUT_TAX')
            ->where('USER_ID', getUserId())
            ->latest('PLAYER_LEDGER_ID')
            ->limit(1)
            ->first(); // Check if TDS is applicable - Get previous player ledger data                         


        if (collect($previousLedgerResult)->isNotEmpty()) {

            //------------------------------------------------------------------------
            // CHECK INSTSTANT OR NORMAL WITHDRAW IF NORMAL THEN GET PENDING WITHDRAWS
            //------------------------------------------------------------------------
            if (
                (
                    ($user->USER_FLAG == self::YELLOW_FLAG &&
                        config('rummy_config.instantPayoutFlagConsider.yellow') &&
                        ($requestedAmount + $this->getDailyTotalInstantWithdrawAmount()) <= config('rummy_config.instantWithdrawMaxCapping.yellowPerDay') &&
                        $requestedAmount <= config('rummy_config.instantWithdrawMaxCapping.yellowPerTrans')
                    )
                    ||
                    ($user->USER_FLAG == self::GREEN_FLAG &&
                        config('rummy_config.instantPayoutFlagConsider.green') &&
                        ($requestedAmount + $this->getDailyTotalInstantWithdrawAmount()) <= config('rummy_config.instantWithdrawMaxCapping.greenPerDay') &&
                        $requestedAmount <= config('rummy_config.instantWithdrawMaxCapping.greenPerTrans')
                    )
                )
            ) {
                $userPendingWithdrawal = 0;
            } else {
                $userPendingWithdrawal  =   WithdrawTransactionHistory::where([
                    'USER_ID' => getUserId(),
                    'TRANSACTION_STATUS_ID' => self::WITHDRAW_PENDING_TRANSACTION_STATUS,
                    'TRANSACTION_TYPE_ID' => self::WITHDRAW_TRANSACTION_TYPE
                ])->sum('WITHDRAW_AMOUNT');
            }
            //------------------------------
            // END INSTANT CHECK
            //------------------------------

            $eligibleWithoutTax = ($previousLedgerResult->TOTAL_ELIGIBLE_WITHDRAWAL_WITHOUT_TAX ?? 0) - $userPendingWithdrawal;

            $eligibleWithoutTax = ($eligibleWithoutTax > 0 ? $eligibleWithoutTax : 0);

            $tax = 0;

            if ($requestedAmount > $eligibleWithoutTax) {

                $taxApplicableAmount = $requestedAmount - $eligibleWithoutTax;

                $tax =  (($taxApplicableAmount * $this->tdsRateOnCash) / 100);
            }


            $winTDSComponent = ($userCurrentBalance->win > $tax ? $tax : $userCurrentBalance->win);

            $winNormalComponent = 0;

            if (($userCurrentBalance->win - $winTDSComponent) > 0) {
                if ($userCurrentBalance->win > $requestedAmount) {
                    $winNormalComponent = $requestedAmount - $winTDSComponent;
                } else {
                    $winNormalComponent = $userCurrentBalance->win - $winTDSComponent;
                }
            }

            $depositTDSComponent = $tax - $winTDSComponent;


            $feeApplicableAmount = ($requestedAmount - ($winTDSComponent + $winNormalComponent + $depositTDSComponent));

            $depositFeeComponent = (($feeApplicableAmount * $this->feeCutRateOnDepositAmount) / 100);

            $depositNormalComponent = $feeApplicableAmount - $depositFeeComponent;

            $withdrawFee = 0;

            if ($winTDSComponent != 0) {
                $balanceTypeId = self::WIN_BALANCE_TYPE;
                $withdrawTds = $winTDSComponent;
                $paidAmount  = $requestedAmount - ($withdrawTds + $withdrawFee);
            }

            if ($winNormalComponent != 0) {
                $balanceTypeId = self::WIN_BALANCE_TYPE;
                $withdrawTds = $winTDSComponent;
                $withdrawTds = ($winTDSComponent > 0 ? $winTDSComponent : 0);
                $paidAmount  = $requestedAmount - ($withdrawTds + $withdrawFee);
            }
            if ($depositTDSComponent != 0) {
                $balanceTypeId = self::DEPOSIT_BALANCE_TYPE;
                $withdrawTds = $depositTDSComponent;
                $withdrawFee = $depositFeeComponent;
                $paidAmount  = $requestedAmount - ($withdrawTds + $withdrawFee);
            }
            if ($depositFeeComponent != 0) {
                $balanceTypeId = self::DEPOSIT_BALANCE_TYPE;
                $withdrawFee = $depositFeeComponent;
                $withdrawTds = $depositTDSComponent + $winTDSComponent;
                $paidAmount  = $requestedAmount - ($withdrawTds + $withdrawFee);
            }
            if ($depositNormalComponent != 0) {
                $balanceTypeId = self::DEPOSIT_BALANCE_TYPE;
                $withdrawFee = $depositFeeComponent;
                $withdrawTds = $depositTDSComponent + $winTDSComponent;
                $paidAmount  = $requestedAmount - ($withdrawTds + $withdrawFee);
            }

            if ($winNormalComponent >= $depositNormalComponent) {
                $balanceTypeId = self::WIN_BALANCE_TYPE;
            } else {
                $balanceTypeId = self::DEPOSIT_BALANCE_TYPE;
            }

            return [
                'balanceTypeId' => $balanceTypeId,
                'withdrawTds' => $withdrawTds,
                'withdrawFee' => $withdrawFee,
                'paidAmount' => $paidAmount,
                'nonTaxableAmount' => $eligibleWithoutTax,
                'deductedFromDeposit' => ($depositNormalComponent + $depositFeeComponent + $depositTDSComponent),
                'deductedFromWin' => ($winNormalComponent + $winTDSComponent),
                'feeApplicableAmount' => $feeApplicableAmount
            ];
        } else {
            return false;
        }
    }

    /**
     * function to calculate withdraw fee & check user is exempted from withdraw fee or not
     * 
     * @param int $amount
     * @param $userId
     */
    private function applyWithdrawFee($amount, $userId, $txnType)
    {
        try {
            $result = [];

            $configValue = GlobalConfig::whereIn('CONFIG_KEY', $txnType == 1 ? [
                'instant_withdraw_fee_type',
                'instant_withdraw_fee_val',
                'is_instant_withdraw_fee_waived_off'
            ] : [
                'standard_withdraw_fee_type',
                'standard_withdraw_fee_val',
                'is_standard_withdraw_fee_waived_off'
            ])->get();

            $userSetting = \DB::table('user_settings')->where('USER_ID', $userId)->first();

            $globalFeeWaiveConfiguration =  $configValue->where(
                'CONFIG_KEY', $txnType == 1 ?
                 'is_instant_withdraw_fee_waived_off' : 'is_standard_withdraw_fee_waived_off'
                )->pluck('CONFIG_VALUE')->first();

            $result['is_withdraw_fee_exempted'] = (bool)(
                $globalFeeWaiveConfiguration ?  $globalFeeWaiveConfiguration : (
                    $txnType == 1 ? $userSetting->IS_INSTANT_WITHDRAW_FEE_EXEMPTED : $userSetting->IS_STANDARD_WITHDRAW_FEE_EXEMPTED
                )
            );
            
            $withdrawFeeType = $configValue->where(
                'CONFIG_KEY', $txnType == 1 ?
                 'instant_withdraw_fee_type' : 'standard_withdraw_fee_type'
                )->pluck('CONFIG_VALUE')->first();

            $withdrawFeeValue = $configValue->where(
                'CONFIG_KEY', $txnType == 1 ?
                 'instant_withdraw_fee_val' : 'standard_withdraw_fee_val'
                )->pluck('CONFIG_VALUE')->first();
            
            $withdrawFeeValue = $txnType == 1 ? (
                $userSetting->INSTANT_WITHDRAW_FEE_PER !=0 ? $userSetting->INSTANT_WITHDRAW_FEE_PER : $withdrawFeeValue
            ) : (
                $userSetting->STANDARD_WITHDRAW_FEE_PER !=0 ? $userSetting->STANDARD_WITHDRAW_FEE_PER : $withdrawFeeValue
            );

            $result['withdraw_fee'] = $withdrawFeeType == 1 ? ($withdrawFeeValue*(int)$amount/100) : $withdrawFeeValue;
            $result['withdraw_charge_value'] = $withdrawFeeType == 1 ? "$withdrawFeeValue%" : $withdrawFeeValue;
            return $result;
        } catch(\Exception $e) {
            Log::error($e);
            return false;
        }
    }




    /*--------------Some Basic Functions ----------------------*/
    private function isWithdrawableBalanceIsGreaterThanRequestedAmount(Float $requestedAmount, Object $userCurrentBalance)
    {
        $amountToBeKept = WithdrawalCriteria::select('AMOUNT_TO_BE_KEPT')->where('USER_ID', getUserId())
            ->latest('WITHDRAWAL_CRITERIA_ID')
            ->pluck('AMOUNT_TO_BE_KEPT')
            ->first() ?? 0;

        /* Withdrawal balances made configurable */
        if (config('rummy_config.DEPOSIT_WITHDRAWAL')) {
            $withdrawAbleBalance = ($userCurrentBalance->win + $userCurrentBalance->deposit) - $amountToBeKept;
        } else {
            /* withdrawable amount  */
            $withdrawAbleBalance =  $userCurrentBalance->win - $amountToBeKept;
        }

        if ($withdrawAbleBalance < $requestedAmount) {
            return false;
        } else {
            return true;
        }
    }

    private function userCurrentBalance(Int $coinType)
    {
        return UserPoint::select(
            'USER_DEPOSIT_BALANCE AS deposit',
            'USER_PROMO_BALANCE AS promo',
            'USER_WIN_BALANCE AS win',
            'USER_TOT_BALANCE AS total'
        )
            ->where('USER_ID', getUserId())
            ->where('COIN_TYPE_ID', $coinType)
            ->first();
    }

    private function internalReferenceNumber(Int $transferType)
    {
        $t = microtime(true);
        $micro = sprintf("%06d", ($t - floor($t)) * 1000000);
        $d = new DateTime(date('Y-m-d H:i:s.' . $micro, $t));
        $timeInMicro = $d->format("ymdHisu");

        $internalRefNo = $transferType . getUserId();
        $internalRefNo = $internalRefNo . mt_rand(100, 999);
        $internalRefNo = $internalRefNo . $timeInMicro;
        $internalRefNo = $internalRefNo . mt_rand(100, 999);

        return $internalRefNo;
    }


    private function postCurl($endpoint, $headers, $params = [])
    {
        $postFields = json_encode($params);
        array_push($headers, 'Content-Type: application/json', 'Content-Length: ' . strlen($postFields));
        $endpoint = $endpoint . "?";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $returnData = curl_exec($ch);
        curl_close($ch);

        if ($returnData != "") {
            return json_decode($returnData, true);
        }
        return NULL;
    }

    private function getCurl($endpoint, $headers)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $returnData = curl_exec($ch);
        curl_close($ch);
        if ($returnData != "") {
            return json_decode($returnData, true);
        }
        return NULL;
    }

    private function getDailyTotalInstantWithdrawAmount()
    {
        $dailyTotalAmount = 0;

        $dailyTotalAmount = WithdrawTransactionHistory::where('USER_ID', getUserId())
            ->where('TRANSACTION_TYPE_ID', self::WITHDRAW_TRANSACTION_TYPE)
            ->where('TRANSACTION_STATUS_ID', self::WITHDRAW_APPROVED_TRANSACTION_STATUS)
            ->where('APPROVE_TYPE', 'cashfree_greenflag')
            ->whereBetween('TRANSACTION_DATE', [
                date('Y-m-d 00:00:00'),
                date('Y-m-d 23:59:59')
            ])->sum('WITHDRAW_AMOUNT');

        return $dailyTotalAmount;
    }

    private function getDailyTotalWithdrawAmount()
    {
        $dailyTotalAmount = 0;

        $dailyTotalAmount = PaymentTransaction::where('USER_ID', getUserId())
            ->where('TRANSACTION_TYPE_ID', self::WITHDRAW_TRANSACTION_TYPE)
            ->whereIn('PAYMENT_TRANSACTION_STATUS', [
                self::WITHDRAW_APPROVED_TRANSACTION_STATUS,
                self::WITHDRAW_PENDING_TRANSACTION_STATUS,
                self::WITHDRAW_INITIATED_TRANSACTION_STATUS
            ])
            ->whereBetween('PAYMENT_TRANSACTION_CREATED_ON', [
                date('Y-m-d 00:00:00'),
                date('Y-m-d 23:59:59')
            ])->sum('PAYMENT_TRANSACTION_AMOUNT');

        return $dailyTotalAmount;
    }

    private function isWithdrawTransLimitExceed($withdrawType)
    {
        $userId = getUserId();

        if ($this->isThePlayerIsWinning($userId))
            $limit = config('rummy_config.noOfWithdrawDayTransactionLimit.winning');
        else
            $limit = config('rummy_config.noOfWithdrawDayTransactionLimit.loosing');


        $transactionDetails = WithdrawTransactionHistory::where([
            'TRANSACTION_TYPE_ID' => 10,
            'USER_ID' => $userId
        ])->whereIn('TRANSACTION_STATUS_ID', [
            self::WITHDRAW_PENDING_TRANSACTION_STATUS,
            self::WITHDRAW_INITIATED_TRANSACTION_STATUS,
            self::WITHDRAW_APPROVED_TRANSACTION_STATUS
        ])
            ->whereDate('TRANSACTION_DATE', date('Y-m-d'))
            ->get();

        $withdrawCountOfRequestedType = $transactionDetails->where('WITHDRAW_TYPE', $withdrawType)->count();

        $withdrawCountAllowedOfRequestedType = \DB::table('user_settings')->where('USER_ID', $userId)->value($withdrawType == 1 ? 'INSTANT_WITHDRAW_DAILY_COUNT' : 'STANDARD_WITHDRAW_DAILY_COUNT');

        $withdrawCountAllowedOfRequestedType = ($withdrawCountAllowedOfRequestedType == 0 ? GlobalConfig::where('CONFIG_KEY', ($withdrawType == 1 ? 'instant_withdraw_daily_count' : 'standard_withdraw_daily_count'))->value('CONFIG_VALUE') : $withdrawCountAllowedOfRequestedType);
        
        $transCount = $transactionDetails->count();

        return $transCount >= $limit || $withdrawCountOfRequestedType >= $withdrawCountAllowedOfRequestedType;
    }

    /**
     * This function will check if the provided user is wininng or loosing.
     * @param $userId
     * @return Boolean
     */

    protected function isThePlayerIsWinning($userId)
    {
        $lifeTimeDeposit = $this->getUserLifeTimeDeposit($userId);
        $lifeTimeWithdraw = $this->getUserTotalWithdraw($userId);
        $userCurrentTotalBalance = $this->getUserCurrentTotalBalance($userId);


        $winningVal = (($lifeTimeDeposit - $lifeTimeWithdraw - $userCurrentTotalBalance) / $lifeTimeDeposit);


        $winningVal = (1 - $winningVal);

        if ($winningVal >= 1) { //Changed from 0.9 to accomodate users who are at borderline of being a winner
            return true;
        } else {
            return false;
        }
    }

    /**
     * This function will provide user's life time deposit.
     * @param $userId
     * @return double
     */
    protected function getUserLifeTimeDeposit($userId)
    {
        return PaymentTransaction::select(DB::raw('IFNULL(SUM(PAYMENT_TRANSACTION_AMOUNT),1) as total_deposit'))
            ->where('USER_ID', $userId)
            ->whereIn('PAYMENT_TRANSACTION_STATUS', ['103', '125'])
            ->whereIn('TRANSACTION_TYPE_ID', ['8', '61', '62', '83', '111'])
            ->first()->total_deposit;
    }

    /**
     * This function will provide user's total withdrawal except commission.
     * @param $userId
     * @return double
     */
    protected function getUserTotalWithdraw($userId)
    {
        return PaymentTransaction::select(DB::raw('IFNULL(SUM(PAYMENT_TRANSACTION_AMOUNT),0) as total_withraw'))
            ->where('USER_ID', $userId)
            ->where('PAYMENT_TRANSACTION_STATUS', "208")
            ->where('TRANSACTION_TYPE_ID', '10')
            ->first()->total_withraw;
    }

    /**
     * This function will provide current user balance (PROMO+WIN+DEPOSIT)
     * @param $userId
     * @return double
     */
    protected function getUserCurrentTotalBalance($userId)
    {
        return UserPoint::select('USER_TOT_BALANCE')
            ->where('USER_ID', $userId)
            ->where('COIN_TYPE_ID', "1")
            ->first()->USER_TOT_BALANCE ?? 0;
    }
    private function pushEventToGA(String $internalRefNo, int $txnStatus = 208)
    {
        $data = WithdrawTransactionHistory::where([
            'INTERNAL_REFERENCE_NO' => $internalRefNo,
            'TRANSACTION_STATUS_ID' => $txnStatus
        ])->first();
        $withdrawStatusLabel = '';
        switch ($txnStatus) {
            case self::WITHDRAW_APPROVED_TRANSACTION_STATUS:
                $withdrawStatusLabel = 'withdrawal_successful';
                break;
            case self::WITHDRAW_FAILED_TRANSACTION_STATUS:
                $withdrawStatusLabel = 'withdrawal_failed';
                break;
            default:
                $withdrawStatusLabel = 'withdrawal_successful';
                break;
        }

        $txnStatusLabel = \App\Models\TransactionStatus::select('TRANSACTION_STATUS_DESCRIPTION')->find($txnStatus);

        if (!empty($data)) {

            $url = config('rummy_config.google_analytics.ga4_url');

            try {

                $details = json_decode($data->AMOUNT_BREAKUP_DETAILS, true);

                $appInstanceId = $details['APP_INSTANCE_ID'] ?? '';
                $appType = $details['APP_TYPE'] ?? '';

                if (!empty($appType) || in_array($appType, [1, 2, 4])) { // 2 Android, 1 2, 4 Web Dashboard

                    if ($appType == 2) {
                        $url = str_replace(array('<FIREBASE_APP_ID>', '<API_SECRET>'), array(config('rummy_config.google_analytics.android.firebase_app_id'), config('rummy_config.google_analytics.android.app_secret')), $url);
                    } else if ($appType == 1) {
                        $url = str_replace(array('<FIREBASE_APP_ID>', '<API_SECRET>'), array(config('rummy_config.google_analytics.ios.firebase_app_id'), config('rummy_config.google_analytics.ios.app_secret')), $url);
                    } else {
                        $url = str_replace(array('<FIREBASE_APP_ID>', '<API_SECRET>'), array(config('rummy_config.google_analytics.web.gaTrackingId'), config('rummy_config.google_analytics.web.gaTrackingWeb_SerectKey')), $url);
                    }
                    if ($appType != 4) :
                        $formData = [
                            "url" => $url,
                            "form_params" => [
                                'app_instance_id' => $appInstanceId,
                                'user_id' => (string) $data->USER_ID,
                                'events' => [
                                    [
                                        'name' => $withdrawStatusLabel,
                                        'params' => [
                                            'app_instance_id' => $appInstanceId,
                                            'user_ID' => (string) $data->USER_ID,
                                            'hit_timestamp' => date("F d Y H:i A D Y H:i:s"),
                                            'clicked_item_text' => 'Withdraw Button',
                                            'selected_amount' => (string) $data->WITHDRAW_AMOUNT,
                                            'amount_withdrawn' => (string) $data->PAID_AMOUNT,
                                            'withdraw_transaction_id' => (string) $data->INTERNAL_REFERENCE_NO,
                                            'withdraw_status' => $txnStatusLabel->TRANSACTION_STATUS_DESCRIPTION,
                                            'user_login_status' => 'true',
                                            'user_registration_status' => 'true'
                                        ]
                                    ]
                                ]
                            ],
                            "headers" => [
                                "Content-Type" => "application/json"
                            ]
                        ];
                    else :
                        $formData = [
                            "url" => $url,
                            "form_params" => [
                                'client_id' => $appInstanceId,
                                'user_id' => (string) $data->USER_ID,
                                'events' => [
                                    [
                                        'name' => $withdrawStatusLabel,
                                        'params' => [
                                            'client_id' => $appInstanceId,
                                            'user_ID' => (string) $data->USER_ID,
                                            'hit_timestamp' => date("F d Y H:i A D Y H:i:s"),
                                            'clicked_item_text' => 'Withdraw Button',
                                            'selected_amount' => (string) $data->WITHDRAW_AMOUNT,
                                            'amount_withdrawn' => (string) $data->PAID_AMOUNT,
                                            'withdraw_transaction_id' => (string) $data->INTERNAL_REFERENCE_NO,
                                            'withdraw_status' => $txnStatusLabel->TRANSACTION_STATUS_DESCRIPTION,
                                            'user_login_status' => 'true',
                                            'user_registration_status' => 'true'
                                        ]
                                    ]
                                ]
                            ],
                            "headers" => [
                                "Content-Type" => "application/json"
                            ]
                        ];
                    endif;
                    $response = $this->ApiCallCurl($formData);
                    $this->userActivitiesTracking(getUserId(), "GA4_EVENT_TRACKING", [
                        'request' => $formData,
                        'response' => $response
                    ]);
                }
            } catch (\Exception $exception) {
                Log::error($exception);
            }
        }
    }

    public function withdrawStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'txn_id' => 'required|alpha_num',
        ], [
            'txn_id.required' => 'Transaction ID is required',
            'txn_id.alpha_num' => 'Only alphabets and numbers are allowed in transaction ID'
        ]);
        if ($validator->fails()) {
            $errorCode = ['txn_id' => 422088];
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $this->errorBag[] = [
                    'code' => $errorCode[$key],
                    'message' => $error[0]
                ];
            }
            return $this->errorResponse(1, $this->errorBag, 422);
        }

        try {

            /** Fetch Withdraw Data From Withdraw Transaction History Instead Of Payment Transaction */
            $withdrawResponse = WithdrawTransactionHistory::leftJoin('transaction_status as ts', 'withdraw_transaction_history.TRANSACTION_STATUS_ID', '=', 'ts.TRANSACTION_STATUS_ID')
            ->select(
                'ts.TRANSACTION_STATUS_DESCRIPTION as status',
                'withdraw_transaction_history.INTERNAL_REFERENCE_NO as txnid',
                'withdraw_transaction_history.WITHDRAW_AMOUNT as amount',
                'withdraw_transaction_history.WITHDRAW_TDS as tds',
                'withdraw_transaction_history.TRANSACTION_DATE as created_date',
                'withdraw_transaction_history.UPDATED_ON as updated_date',
                'withdraw_transaction_history.TRANSACTION_TYPE_ID as TRANSACTION_TYPE',
                'withdraw_transaction_history.USER_ID as user_id',
                'withdraw_transaction_history.PAYABLE_AMOUNT as withdrawable_amount',
                'withdraw_transaction_history.WITHDRAW_CHARGE as actual_withdraw_fee',
                'withdraw_transaction_history.DISCOUNTED_WITHDRAW_CHARGE as discounted_withdraw_fee'
            )
            ->where('withdraw_transaction_history.USER_ID', getUserId())
            ->where('withdraw_transaction_history.INTERNAL_REFERENCE_NO', $request->txn_id)
            ->where("withdraw_transaction_history.TRANSACTION_TYPE_ID", 10)
            ->first();

            if($withdrawResponse) {
                $successBag['status'] = $withdrawResponse->status;
                $successBag['txnid'] = $withdrawResponse->txnid;
                $successBag['amount'] = $withdrawResponse->amount;
                $successBag['tds'] = $withdrawResponse->tds;
                $successBag['created_date'] = $withdrawResponse->created_date;
                $successBag['updated_date'] = $withdrawResponse->updated_date;
                $successBag['withdraw_fee'] = $withdrawResponse->actual_withdraw_fee;
                $successBag['is_withdraw_fee_waivedoff'] = $withdrawResponse->discounted_withdraw_fee == 0;
                $successBag['discounted_withdraw_fee'] = $withdrawResponse->discounted_withdraw_fee;
                $successBag['total_withdrawable_amount'] = $withdrawResponse->withdrawable_amount;
                return $this->successResponse(1, $successBag);
            } else{
                //return error if no results found for the supplied transaction id
                $this->errorBag[] = [
                    'code' => 422088,
                    'message' => 'No Results found'
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            }

        } catch (\Exception $e) {
            Log::error($e);
            $errorBag = array([
                'code' => 500000,
                'message' => 'Internal Server Error'
            ]);

            return $this->errorResponse(1, $errorBag);
        }
    }

    public function remainingWithdrawCount($userId, $withdrawType){
        $checkFeeIsWaivedOffOrNot = \DB::table('user_settings')->where('USER_ID', $userId)
			->select('IS_INSTANT_WITHDRAW_FEE_EXEMPTED', 'IS_STANDARD_WITHDRAW_FEE_EXEMPTED', 'INSTANT_WITHDRAW_DAILY_COUNT', 'STANDARD_WITHDRAW_DAILY_COUNT', 'STANDARD_WITHDRAW_FEE_PER', 'INSTANT_WITHDRAW_FEE_PER')
			->first();

			$withdrawCountTypeWise = WithdrawTransactionHistory::where('USER_ID', $userId)->whereIn('TRANSACTION_STATUS_ID', [109, 208, 255])
			->whereBetween('TRANSACTION_DATE', [Carbon::now()->format('Y-m-d 00:00:00'), Carbon::now()])
			->select(\DB::raw('count(WITHDRAW_TRANSACTTION_ID) as WITHDRAW_COUNT'), 'WITHDRAW_TYPE')
			->groupBy('WITHDRAW_TYPE')
			->get();

			$configData = GlobalConfig::whereIn('CONFIG_KEY', [
				'instant_withdraw_daily_count',
				'instant_withdraw_fee_type',
				'instant_withdraw_fee_val',
				'standard_withdraw_daily_count',
				'standard_withdraw_fee_val',
				'standard_withdraw_fee_type',
				'is_instant_withdraw_disabled',
				'standard_withdraw_processing_time',
				'is_instant_withdraw_fee_waived_off',
				'is_standard_withdraw_fee_waived_off'
			])->pluck('CONFIG_VALUE', 'CONFIG_KEY')
			->toArray();

            if($withdrawType == 1){
                $dailyWithdrawCountRemaining = ($checkFeeIsWaivedOffOrNot->INSTANT_WITHDRAW_DAILY_COUNT>0 ? $checkFeeIsWaivedOffOrNot->INSTANT_WITHDRAW_DAILY_COUNT : $configData['instant_withdraw_daily_count']) - ($withdrawCountTypeWise->where('WITHDRAW_TYPE', 1)->pluck('WITHDRAW_COUNT')->first());
                $dailyWithdrawCountRemaining = $dailyWithdrawCountRemaining > 0 ?$dailyWithdrawCountRemaining : 0;
            } else {
                $dailyWithdrawCountRemaining = ($checkFeeIsWaivedOffOrNot->STANDARD_WITHDRAW_DAILY_COUNT>0 ? $checkFeeIsWaivedOffOrNot->STANDARD_WITHDRAW_DAILY_COUNT : $configData['standard_withdraw_daily_count']) - ($withdrawCountTypeWise->where('WITHDRAW_TYPE', 2)->pluck('WITHDRAW_COUNT')->first());
                $dailyWithdrawCountRemaining = $dailyWithdrawCountRemaining > 0 ?$dailyWithdrawCountRemaining : 0;
            }
            return $dailyWithdrawCountRemaining;
    }

    public function rejectWithdraw($internalReferenceNo, $user)
    {
        DB::beginTransaction();

        try{

            $transactionData = WithdrawTransactionHistory::where('INTERNAL_REFERENCE_NO', $internalReferenceNo)->where('USER_ID', $user->USER_ID)->first();

            if (!empty($transactionData)) {

                $userCurrentRealBalance = $this->userCurrentBalance(self::REAL_CASH_COIN_TYPE, $user->USER_ID);

                //update user points

                if (!empty($transactionData->AMOUNT_BREAKUP_DETAILS)) {
                    $amountBreakupData = json_decode($transactionData->AMOUNT_BREAKUP_DETAILS);
                    $deductedFromWin = $amountBreakupData->DEDUCTED_FROM_WIN;
                    $deductedFromDeposit = $amountBreakupData->DEDUCTED_FROM_DEPOSIT;

                    DB::select(
                    'update user_points SET
                                    `VALUE` = ?,
                                    `USER_WIN_BALANCE` = `USER_WIN_BALANCE` + ?,
                                    `USER_DEPOSIT_BALANCE` = `USER_DEPOSIT_BALANCE` + ?,
                                    `USER_TOT_BALANCE` = `USER_WIN_BALANCE` + `USER_PROMO_BALANCE` + `USER_DEPOSIT_BALANCE`
                                    WHERE
                            `USER_ID` = ? AND
                            `COIN_TYPE_ID` = ?
                        ',
                    [
                        $deductedFromDeposit + $deductedFromWin,
                        $deductedFromWin,
                        $deductedFromDeposit,
                        $user->USER_ID,
                        self::REAL_CASH_COIN_TYPE
                    ]
                    );
                }

                $currentTotBalance = $userCurrentRealBalance->total;
                $newTotBalance = $currentTotBalance + $transactionData->WITHDRAW_AMOUNT;


                $masterTransaction = new MasterTransactionHistory();
                $masterTransaction->USER_ID                = $user->USER_ID;
                $masterTransaction->BALANCE_TYPE_ID        = $transactionData->BALANCE_TYPE_ID;
                $masterTransaction->TRANSACTION_STATUS_ID  = self::WITHDRAW_REJECTED_TRANSACTION_STATUS;
                $masterTransaction->TRANSACTION_TYPE_ID    = self::WITHDRAW_REJECT_TRANSACTION_TYPE;
                $masterTransaction->TRANSACTION_AMOUNT     = $transactionData->WITHDRAW_AMOUNT;
                $masterTransaction->TRANSACTION_DATE       = date('Y-m-d H:i:s');
                $masterTransaction->INTERNAL_REFERENCE_NO  = $internalReferenceNo;
                $masterTransaction->CURRENT_TOT_BALANCE    = $currentTotBalance;
                $masterTransaction->CLOSING_TOT_BALANCE    = $newTotBalance;
                $masterTransaction->PARTNER_ID             = $user->PARTNER_ID;
                $masterTransaction->save();

                $updateWithdrawTrans = WithdrawTransactionHistory::where(['USER_ID' => $user->USER_ID, 'INTERNAL_REFERENCE_NO' => $internalReferenceNo])
                ->update(['TRANSACTION_STATUS_ID' => self::WITHDRAW_REJECTED_TRANSACTION_STATUS, 'APPROVED_BY' => 'ADMIN', 'APPROVE_TYPE' => 'normal']);

                //update status into payment transaction
                $updatePaymentTrans = PaymentTransaction::where(['USER_ID' => $user->USER_ID, 'INTERNAL_REFERENCE_NO' => $internalReferenceNo])
                ->update(['PAYMENT_TRANSACTION_STATUS' => self::WITHDRAW_REJECTED_TRANSACTION_STATUS]);

                DB::commit();
                // Update Player Ledger
                $this->reconcilePlayerLedger($user->USER_ID, 'RESET_LEDGER');
                return $updatePaymentTrans;
            }
        }
            catch(\Exception $e){
            DB::rollBack();
            Log::error($e);
        }
    }

    private function getCommonDiv($withdrawTxnHistory)
    {
        $commonDiv = '<div style="border-radius: 8px; background: #f5f5f5; padding: 30px">';
        $commonDiv = $commonDiv .'<p
        style="
            color: #595959;
            font-size: 16px;
            font-weight: 700;
            margin: 0px;
            padding-bottom: 10px;
        "
        >
        Transaction ID:
        <span
            style="color: #595959; font-size: 15px; font-weight: 400"
        >
        '.$withdrawTxnHistory->INTERNAL_REFERENCE_NO.'</span
        >
        </p>';
        $commonDiv = $commonDiv .'<p
        style="
            color: #595959;
            font-size: 16px;
            font-weight: 700;
            margin: 0px;
            padding-bottom: 10px;
        "
        >
        Amount: ₹
        <span
            style="color: #595959; font-size: 15px; font-weight: 400"
        >
            '.number_format($withdrawTxnHistory->WITHDRAW_AMOUNT, 2, '.', '').'</span
        >
        </p>';
        $commonDiv = $commonDiv .'<p
        style="
            color: #595959;
            font-size: 16px;
            font-weight: 700;
            margin: 0px;
            padding-bottom: 10px;
        "
        >
        Withdrawal TDS @30%: ₹
        <span
            style="color: #595959; font-size: 15px; font-weight: 400"
            >'.number_format($withdrawTxnHistory->WITHDRAW_TDS, 2, '.', '').'</span
        >
        </p>';

        if($withdrawTxnHistory->DISCOUNTED_WITHDRAW_CHARGE == 0 && $withdrawTxnHistory->WITHDRAW_CHARGE > 0){
            $commonDiv = $commonDiv .'<p
            style="
                color: #595959;
                font-size: 16px;
                font-weight: 700;
                margin: 0px;
                padding-bottom: 10px;
            "
            >
            Withdrawal fee: ₹
            <span
                style="color: #595959; font-size: 15px; font-weight: 400"
                >'.'<s>'.number_format($withdrawTxnHistory->WITHDRAW_CHARGE, 2, '.', '').'</s>'.'</span
            >
            </p>';
        } else if($withdrawTxnHistory->DISCOUNTED_WITHDRAW_CHARGE != $withdrawTxnHistory->WITHDRAW_CHARGE){
            $commonDiv = $commonDiv .'<p
            style="
                color: #595959;
                font-size: 16px;
                font-weight: 700;
                margin: 0px;
                padding-bottom: 10px;
            "
            >
            Withdrawal fee: ₹
            <span
                style="color: #595959; font-size: 15px; font-weight: 400"
                >'.'<s>'.number_format($withdrawTxnHistory->WITHDRAW_CHARGE, 2, '.', '').'</s>'.'</span
            > 
            ₹<span
                style="color: #595959; font-size: 15px; font-weight: 400"
                >'.number_format($withdrawTxnHistory->DISCOUNTED_WITHDRAW_CHARGE, 2, '.', '').'</span
            >
            </p>';
        } else {
            $commonDiv = $commonDiv .'<p
            style="
                color: #595959;
                font-size: 16px;
                font-weight: 700;
                margin: 0px;
                padding-bottom: 10px;
            "
            >
            Withdrawal fee: ₹
            <span
                style="color: #595959; font-size: 15px; font-weight: 400"
                >'.number_format($withdrawTxnHistory->DISCOUNTED_WITHDRAW_CHARGE, 2, '.', '').'</span
            >
            </p>';
        }
        
        if($withdrawTxnHistory->WITHDRAW_TYPE == 1){
            $commonDiv = $commonDiv .'<p
            style="
                color: #595959;
                font-size: 16px;
                font-weight: 700;
                margin: 0px;
                padding-bottom: 10px;
            "
            >
            Daily instant withdrawals:
            <span
                style="color: #595959; font-size: 15px; font-weight: 400"
            >
                '.$this->remainingWithdrawCount(getUserId(), $withdrawTxnHistory->WITHDRAW_TYPE).'</span
            >
            left
            </p>';
        } else {
            $commonDiv = $commonDiv .'<p
        style="
            color: #595959;
            font-size: 16px;
            font-weight: 700;
            margin: 0px;
            padding-bottom: 10px;
        "
        >
        Daily standard withdrawals:
        <span
            style="color: #595959; font-size: 15px; font-weight: 400"
        >
            '.$this->remainingWithdrawCount(getUserId(), $withdrawTxnHistory->WITHDRAW_TYPE).'</span
        >
        left
        </p>';
        }
        
        $commonDiv = $commonDiv .'<p
        style="
            color: #595959;
            font-size: 16px;
            font-weight: 700;
            margin: 0px;
            padding-bottom: 10px;
        "
        >
        Total Amount: ₹
        <span
            style="color: #595959; font-size: 15px; font-weight: 400"
        >
        '.number_format($withdrawTxnHistory->PAID_AMOUNT, 2, '.', '').'</span
        >
        </p>';
        $commonDiv = $commonDiv .'</div>';
        return $commonDiv;
    }
}
