<?php

namespace App\Http\Controllers\Payment\AddMoney;

use App\Http\Controllers\Controller;
use App\Models\BonusTransactionHistory;
use App\Models\CampaignTournamentMapping;
use App\Models\PromoCampaign;
use \App\Models\PromoRule;
use App\Models\PaymentGateways;
use App\Models\PromoClicks;
use App\Models\CampaignUserSpecific;
use App\Models\DepositLimitUser;
use App\Models\DepositLimitSetting;
use App\Models\MasterTransactionHistory;
use App\Models\MasterTransactionHistoryFppBonus;
use App\Models\CampaignToUser;
use App\Models\PaymentTransaction;
use App\Models\Tournament;
use App\Models\User;
use App\Models\TournamentUserTicket;
use App\Models\UserPoint;
use Carbon\Carbon;
use DB;
use Illuminate\Database\Eloquent\Model;
use App\Models\GlobalConfig;
use App\Helpers\InvoidAesCipher;
use App\Models\PaymentGatewayFeeSetting;
use App\Models\UserwisePaymentGatewayfeeSetting;
use Illuminate\Support\Facades\Log;
use App\Models\DepositGstInvoice;
use App\Models\State;
use App\Models\TournamentsReinRummy;
use App\Models\Tracking;

class AddMoneyBaseController extends Controller
{

    /**
     * @var int[] campaign_type_id
     * 1. for Payment
     * 6. First Time Depositor
     */
    protected $campaign_type_ids_tour_ticket = [1, 6];

    /**
     * Payment -> Add Money Module: Validation of amount and deposit code before proceeding to add money.
     * <AUTHOR> Aroraa <<EMAIL>>
     */
    /* Function to get details of promo code */
    protected function getPromoDetails($deposit_code)
    {
        $current_date = date('Y-m-d H:i:s');
        $todayWeekDay = date('w');

        $userSegmentId = DB::table('segment_to_user')->where('USER_ID', getUserId())->pluck('SEGMENT_ID')->toArray();

        $userSegmentId = implode(",", $userSegmentId);
        $userVisibilityIds = $this->getUserEligiblityIds(getUserId());
        $promoCodeDetails = PromoCampaign::select('promo_campaign.*')
        ->where([
            ['START_DATE_TIME', '<=', $current_date],
            ['END_DATE_TIME', '>=', $current_date]
        ])
        ->where('promo_campaign.PROMO_CAMPAIGN_TYPE_ID', 1)
        ->where('PROMO_CAMPAIGN_CODE', $deposit_code)
        ->whereRaw("case when FREQUENCY<>0 then FIND_IN_SET($todayWeekDay, FREQUENCY_WEEKDAYS) else 1=1 end") // Check FREQUENCY_DAYS In Case Deposit Code is made for specific weekdays
        ->when(!empty($userSegmentId), function($query) use($userSegmentId) {
            $query->whereRaw("case when promo_campaign.SEGMENT_ID is not null then promo_campaign.SEGMENT_ID in ($userSegmentId) else 1=1 end");
        })
        ->when(empty($userSegmentId), function($query) {
            $query->whereRaw("promo_campaign.SEGMENT_ID is null");
        })
        ->whereIn('VISIBILITY_ID', $userVisibilityIds)
        ->first();


        if ($promoCodeDetails) {
            return $promoCodeDetails;
        } else {
            return false;
        }
    }

    /* function to return list of enabled payment gateways */

    protected function enabledPaymentGatewaysList()
    {
        $enabledGateways = PaymentGateways::select('id', 'gateway_name', 'description', 'sort_order', 'logo', 'value')->where('status', '1')->get();
        if (count($enabledGateways)) {
            return $enabledGateways;
        } else {
            return false;
        }
    }

    /* Function to add record in promo_clicks table each and every time a code is checked */

    protected function addPromoCodeCheckCount($PROMO_CAMPAIGN_ID)
    {
        $promoClicks = new PromoClicks();
        $promoClicks->PROMO_CAMPAIGN_ID = $PROMO_CAMPAIGN_ID;
        $promoClicks->CLICKED_ON = date('Y-m-d H:i:s');
        $promoClicks->IP_ADDRESS = $this->getRealIpAddr();
        $promoClicks->save();
    }

    /* function to get promocode usage count */

    protected function getPromocodeUsagecount($promoCampaignID)
    {
        $usageCount = CampaignToUser::select('PROMO_CAMPAIGN_ID')->where('PROMO_CAMPAIGN_ID', $promoCampaignID)->count();
        return $usageCount;
    }

    /* function to check if user has used a promo code or not */

    protected function promoCodeUsageCheck($promoCampaignID, $userID)
    {
        $checkUsgae = CampaignToUser::select('PROMO_CAMPAIGN_ID', 'USER_ID')->where([
            ['PROMO_CAMPAIGN_ID', '=', $promoCampaignID],
            ['USER_ID', '=', $userID]
        ])->count();

        if ($checkUsgae) {
            return $checkUsgae;
        } else {
            return false;
        }
    }

    protected function promoCodeUsedByUserToday($promo_code = null, $user_id = null)
    {
        $checkUsgae = PaymentTransaction::whereIn('PAYMENT_TRANSACTION_STATUS', [125, 103])
            ->when(!empty($promo_code), function ($query) use ($promo_code) {
                return $query->where('PROMO_CODE', $promo_code);
            })
            ->when(!empty($user_id), function ($query) use ($user_id) {
                return $query->where('USER_ID', $user_id);
            })
            ->whereRaw("DATE(`PAYMENT_TRANSACTION_CREATED_ON`) = CURDATE()")
            ->count();

        if ($checkUsgae) {
            return $checkUsgae;
        } else {
            return false;
        }
    }

    protected function promoCodeUsedByUser($promo_code = null, $user_id = null)
    {
        $checkUsgae = PaymentTransaction::whereIn('PAYMENT_TRANSACTION_STATUS', [125, 103])
            ->when(!empty($promo_code), function ($query) use ($promo_code) {
                return $query->where('PROMO_CODE', $promo_code);
            })
            ->when(!empty($user_id), function ($query) use ($user_id) {
                return $query->where('USER_ID', $user_id);
            })
            ->count();

        if ($checkUsgae) {
            return $checkUsgae;
        } else {
            return false;
        }
    }

    protected function uniquePromoCodeUsageCheck($promoCampaignID)
    {
        $checkUsgae = CampaignToUser::select('USER_ID')->where([
            ['PROMO_CAMPAIGN_ID', '=', $promoCampaignID]
        ])->count();
        if ($checkUsgae) {
            return true;
        } else {
            return false;
        }
    }

    /* function to validate promo / deposit code */

    protected function validateCode($depositCode, $amount, $userId)
    {
        $promoCodeDetails = $this->getPromoDetails($depositCode);
        $response = "Invalid";
        if ($promoCodeDetails) {
            /* Add promo code to promo_clicks table */
            $PROMO_CAMPAIGN_ID = $promoCodeDetails->PROMO_CAMPAIGN_ID;
            $this->addPromoCodeCheckCount($PROMO_CAMPAIGN_ID);
            /* Check if promo code is active */
            $isPromoValid = false;
            if ($promoCodeDetails->STATUS == 1 && $promoCodeDetails->PROMO_STATUS_ID == 1) {
                $isPromoValid = true;
            }
            if (!$isPromoValid) {
                $this->errorBag[] = [
                    'code' => 422029,
                    'message' => 'Invalid Code'
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            }
            /* Check for promo code Minimum deposit amount */
            if ($amount < $promoCodeDetails->MINIMUM_DEPOSIT_AMOUNT) {
                $this->errorBag[] = [
                    'code' => 422028,
                    'message' => 'Amount is less then minimum required:' . $promoCodeDetails->MINIMUM_DEPOSIT_AMOUNT
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            }

            // /* Check for promo code maximum deposit amount */
            // if ($amount > $promoCodeDetails->MAXIMUM_DEPOSIT_AMOUNT) {
            //     $this->errorBag[] = [
            //         'code' => 422030,
            //         'message' => 'Amount is greater then maximum deposit required:' . $promoCodeDetails->MAXIMUM_DEPOSIT_AMOUNT
            //     ];
            //     return $this->errorResponse(1, $this->errorBag, 422);
            // }
            /* Check for partner ID to check if promo code can be used by any one or specific partner */
            $user = $this->getUserDetails($userId);
            if ($promoCodeDetails->PARTNER_ID != 10001) {
                if ($promoCodeDetails->PARTNER_ID != $user->PARTNER_ID) {
                    $this->errorBag[] = [
                        'code' => 422029,
                        'message' => 'Code is valid for a specific partner only'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                }
            }
            if ($promoCodeDetails->PROMO_CAMPAIGN_TYPE_ID != 1) {
                /* 
                Only Campaign type 1 is valid 
                 */
                $this->errorBag[] = [
                    'code' => 422029,
                    'message' => 'Code is not valid for deposits'
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            }

            /**
             * Check if promo code is for specific tournament and user already attained the ticket to that tournament
             *
             * * Need to add condition for payment, FTD and one time deposit
             * ? but condition will very for poker and rummy
             * ? because in Rummy campaign type id 6 is for FTD and poker FTD: 8
             * ? poker one time deposit campaign type id 6 but r
             */

            if (in_array($promoCodeDetails->PROMO_CAMPAIGN_TYPE_ID, $this->campaign_type_ids_tour_ticket)) {
                $tournamentNames = array();
                $tournamentNames = $this->getTournamentsForPromoCampaign($promoCodeDetails->PROMO_CAMPAIGN_ID);
                array_push($tournamentNames, $promoCodeDetails->TOURNAMENT_NAME);
                $tournamentNames = array_unique($tournamentNames);
                $tournamentList = Tournament::select(DB::raw("MIN(TOURNAMENT_START_TIME) as TOURNAMENT_START_TIME, TOURNAMENT_ID"))
                    ->where('IS_ACTIVE', 1)
                    ->whereIn('TOURNAMENT_STATUS', [0, 1])
                    ->where('TOURNAMENT_START_TIME', '>=', date('Y-m-d H:i:s'))
                    ->whereIn('TOURNAMENT_NAME', $tournamentNames)
                    ->groupBy('TOURNAMENT_NAME')
                    ->get();
                $tournamentIDs = $tournamentList->pluck('TOURNAMENT_ID')->toArray();
                $tournamentCount = count($tournamentIDs);
                if ($tournamentCount > 0) {
                    $ticketCount = TournamentUserTicket::where('USER_ID', $userId)
                        ->whereIn('TOURNAMENT_ID', $tournamentIDs)->count();
                    if ($ticketCount >= $tournamentCount) {
                        $this->errorBag[] = [
                            'code' => 422029,
                            'message' => 'You already have ticket to the tournament(s) associated with this code'
                        ];
                        return $this->errorResponse(1, $this->errorBag, 422);
                    }
                }
            }

            /* Check is promo code is user specific */
            if ($promoCodeDetails->IS_USER_SPECIFIC) {
                $allowedUser = CampaignUserSpecific::select('PROMO_CAMPAIGN_ID', 'USER_ID', 'STATUS')->where([
                    ['PROMO_CAMPAIGN_ID', '=', $promoCodeDetails->PROMO_CAMPAIGN_ID],
                    ['USER_ID', '=', $userId]
                ])->limit(1)->first();
                if ($allowedUser) {
                    if ($allowedUser->STATUS != 1) {
                        $this->errorBag[] = [
                            'code' => 422029,
                            'message' => 'Deposit code validation error'
                        ];
                        return $this->errorResponse(1, $this->errorBag, 422);
                    }
                } else {
                    $this->errorBag[] = [
                        'code' => 422029,
                        'message' => 'Invalid Code'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                }
            }
            /* check for max usage count */
            $usageCount = $this->getPromocodeUsagecount($promoCodeDetails->PROMO_CAMPAIGN_ID);
            if ($usageCount >= $promoCodeDetails->EXP_USER_MAX) {
                $this->errorBag[] = [
                    'code' => 422029,
                    'message' => 'Deposit code already used maximum allowed times'
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            }

            /**
             * Check code used maximum number for times for the user
             * ! chkCampaignUsedMaxCap in old API
             */
            if (!empty($promoCodeDetails->MAX_CAP) && ($codeUsedNTimes = $this->promoCodeUsageCheck($promoCodeDetails->PROMO_CAMPAIGN_ID, $userId)) != false) {
                if ($codeUsedNTimes >= $promoCodeDetails->MAX_CAP) {
                    $this->errorBag[] = [
                        'code' => 422029,
                        'message' => 'Deposit code already used maximum allowed times'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                }
            }

            /**
             * Check code used maximum number for times for the user daily basis
             */
            $codeUsedNTimes = $promoCodeDetails->FREQUENCY == 1 && $this->promoCodeUsedByUserToday($depositCode, $userId) >= $promoCodeDetails->DAILY_COUNT;
            if ($codeUsedNTimes) {
                $this->errorBag[] = [
                    'code' => 422029,
                    'message' => 'Deposit code already used maximum allowed times'
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            }

            /**
             * check number for transaction for the user
             */
            $transactionCount = $this->promoCodeUsedByUser(null, $userId);
            if ($promoCodeDetails->FIRST_DEPOSIT == 1 && $transactionCount > 0) {
                $this->errorBag[] = [
                    'code' => 422029,
                    'message' => 'Valid for First transaction only'
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            } elseif ($promoCodeDetails->SECOND_DEPOSIT == 1 && $transactionCount != 1) {
                $this->errorBag[] = [
                    'code' => 422029,
                    'message' => 'Valid for Second transaction only'
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            } elseif ($promoCodeDetails->FREQUENCY != 0) {
                $date = strtotime(date("Y-m-d"));
                $date = date("l", $date);
                $date = strtolower($date);
                // $day_of_week = date('N', strtotime($date));  //WEEKDAYS
                $day_of_week = date('w');
                $frequencyWeekDaysDeposit = explode(',', $promoCodeDetails->FREQUENCY_WEEKDAYS);

                if ($promoCodeDetails->FREQUENCY == 1 && !in_array($day_of_week, $frequencyWeekDaysDeposit)) {
                    $this->errorBag[] = [
                        'code' => 422029,
                        'message' => 'Valid for first transaction only'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                } elseif ($promoCodeDetails->FREQUENCY == 2 && ($date == "saturday" || $date == "sunday")) {
                    $this->errorBag[] = [
                        'code' => 422029,
                        'message' => 'Valid for weekdays only'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                } elseif ($promoCodeDetails->FREQUENCY == 3 && ($date != "saturday" || $date != "sunday")) {
                    $this->errorBag[] = [
                        'code' => 422029,
                        'message' => 'Valid for weekends only'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                } elseif ($promoCodeDetails->FREQUENCY == 4 && $date != 'sunday') {
                    $this->errorBag[] = [
                        'code' => 422029,
                        'message' => 'Valid for Sunday Only'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                }
            }

            /**
             * check if KYC is required for Promo code being used
             */
            $promoRule = PromoRule::select('PROMO_PAYMENT_TYPE_ID', 'S_PROMO_CHIPS', 'S_PROMO_CHIP_VALUE', 'MULTIPLE_CODE_USES', 'VOUCHER_USAGE', 'KYC_CHECK','TIER_BONUS_DATA')->where('PROMO_CAMPAIGN_ID', $PROMO_CAMPAIGN_ID)->first();
            $user = User::find($userId);
            //Check for KYC Completion
            if ($promoRule->KYC_CHECK == 1) {
                if ($user->KYC_REMAINING_STEPS != 0) {
                    $this->errorBag[] = [
                        'code' => 422029,
                        'message' => 'Code cannot be used without KYC completion.'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                }
            }

            /**
             * check if applied deposit code is allowed for User's tier level
             */
            if ($promoCodeDetails->TIER_TYPE == 1) {
                //check if promo code allowed for user's tier level
                $levelConfigId = $user->LEVEL_CONFIG_ID;
                $tier_data = json_decode($promoRule->TIER_BONUS_DATA);
                $tier_data_collect = collect($tier_data)->where("level", $levelConfigId)->toArray();
                if (!empty($tier_data_collect)) {
                    $tier_data_collect = $tier_data_collect[$levelConfigId - 1];
                    if ($tier_data_collect->status == 0) {
                        $this->errorBag[] = [
                            'code' => 422133,
                            'message' => 'Deposit Code is not allowed for your tier level.'
                        ];
                        return $this->errorResponse(1, $this->errorBag, 422);
                    }
                }
            }

            $response = "Valid";
            return $response;
        } else {
            return false;
        }
    }

    /* Function to get list of tournaments attached to a promo_campaign */

    protected function getTournamentsForPromoCampaign($promoCampaignID)
    {
        return CampaignTournamentMapping::select('TOURNAMENT_ID')->where('PROMO_CAMPAIGN_ID', $promoCampaignID)->get()->pluck('TOURNAMENT_ID')->toArray();
    }

    /* Function to get list of saved payment options for a user from JusPay */

    protected function getSavedPaymentOptions($jusPayCustID, $userId)
    {
        $data = $this->ApiCallCurl([
            "url" => config('jusPay.jusPay_url') . "cards?customer_id=" . $jusPayCustID,
            "form_params" => [],
            "headers" => [
                "Content-Type" => "application/x-www-form-urlencoded",
                "x-merchantid" => config('jusPay.jusPay_merchant_id')
            ],
            "extra_curl_options_array" => [
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_USERPWD => config('jusPay.jusPay_api_key'),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_CUSTOMREQUEST => "GET",
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_USERPWD => config('jusPay.jusPay_api_key'),
                CURLOPT_POST => true,
            ]
        ]);
        if ($data->httpStatus != 200) {
            $this->userActivitiesTracking($userId, "UNBALE_TO_CREATE_JUSPAY_ACCOUNT_FOR_USER_SPECIFIED", $data);
            return false;
        }
        return $data->response->cards;
    }

    /* function to check id gateway id is valid and enabled */

    protected function checkGatewayCode($gateway_code)
    {
        $gateway = PaymentGateways::select('id')->where([
            ['gateway_name', '=', $gateway_code],
            ['status', '=', '1']
        ])->first();
        if ($gateway) {
            return $gateway;
        } else {
            return false;
        }
    }

    /* function to check if entered card validity is equal or above current month */

    protected function checkCardValidity($month, $year)
    {
        $currentMonth = date('m');
        $currentYear = date('Y');
        if ($year < $currentYear) {
            return false;
        } elseif ($year == $currentYear) {
            if ($month >= $currentMonth) {
                return true;
            } else {
                return false;
            }
        } elseif ($year > $currentYear) {
            return true;
        } else {
            return false;
        }
    }

    /* Function to check user transaction limit */

    protected function getUserTransactionLimit($userId)
    {
        $depositLimit = DepositLimitUser::where('USER_ID', $userId)->limit(1)->first();
        if ($depositLimit) {
            return $depositLimit;
        } else {
            return false;
        }
    }

    /* function to get user transaction limits nased on KYC status */

    protected function getUserTransactionLimitSettings($kycStatus)
    {
        $depositLimitSetting = null;
        if ($kycStatus == 0) {
            $depositLimitSetting = DepositLimitSetting::where('DEP_LEVEL', 'POST_KYC')->first();
        } else {
            $depositLimitSetting = DepositLimitSetting::where('DEP_LEVEL', 'PRE_KYC')->first();
        }
        if ($depositLimitSetting) {
            return $depositLimitSetting;
        } else {
            return false;
        }
    }

    /* function get total transactions per made by user */

    protected function getUserTotaltransactions($userId)
    {
        $transactionCount = MasterTransactionHistory::select('transaction_amount', 'transaction_date', 'transaction_type_id')
            ->where('USER_ID', $userId)->whereIn('TRANSACTION_STATUS_ID', [103, 107, 125, 202])
            ->whereIn('TRANSACTION_TYPE_ID', [8, 61, 62, 83])
            ->count();

        return $transactionCount;
    }

    /* function to get total amount deposited by user on current day */

    protected function getUserDepositSum($userId, $dateFrom = null, $dateTo = null)
    {
        $query = PaymentTransaction::select(DB::raw('SUM(PAYMENT_TRANSACTION_AMOUNT) as DEPOSITED_AMOUNT, count(USER_ID) as TRANSACTION_COUNT'))
            ->where('USER_ID', $userId)
            ->whereIn('TRANSACTION_TYPE_ID', [8, 61, 62, 83, 111])
            ->whereIn('PAYMENT_TRANSACTION_STATUS', [103, 125])
            ->when(!empty( $dateFrom) && !empty($dateTo), function($query) use ($dateFrom, $dateTo) {
                return $query->whereBetween('PAYMENT_TRANSACTION_CREATED_ON', [$dateFrom, $dateTo]);
            })
            ->first();

        return $query;
    }

    /* function to validate amount and number of transactions as per limits of user */

    protected function validateAmountAndDepositLimit($userId, $amount)
    {
        $depositLimitUser = $this->getUserTransactionLimit($userId);
        $depositLimitSetting = null;
        $globalConfig = GlobalConfig::select('CONFIG_VALUE')->where('CONFIG_KEY','life_time_pre_kyc_deposit_limit')->first();
        $lifetimeDepositLimitNonKycUsers = $globalConfig->CONFIG_VALUE;

        $kycStatus = $this->getProfileStatus($userId, false);

        if (!$depositLimitUser) {  
            $depositLimitSetting = $this->getUserTransactionLimitSettings($kycStatus);
        }
        if (!$depositLimitUser && !$depositLimitSetting) {
            $this->errorBag[] = [
                'code' => 422059,
                'message' => 'Transaction limit validation error'
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
      
        if($kycStatus != 0){
            $lifetimeDepositSum = $this->getUserDepositSum($userId);
            if( ($lifetimeDepositSum->DEPOSITED_AMOUNT+$amount) >  $lifetimeDepositLimitNonKycUsers)
            {
                $this->errorBag[] = [
                    'code' => 422135,
                    'message' => 'Lifetime limit exceeded before KYC'
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            }   
        }   

        $DEP_AMOUNT = 0;
        $DEP_AMOUNT_PER_DAY = 0;
        $TRANSACTIONS_PER_DAY = 0;
        $DEP_AMOUNT_PER_WEEK = 0;
        $TRANSACTIONS_PER_WEEK = 0;
        $DEP_AMOUNT_PER_MONTH = 0;
        $TRANSACTIONS_PER_MONTH = 0;
        if ($depositLimitUser) {
            $DEP_AMOUNT = $depositLimitUser->DEP_AMOUNT;
            $DEP_AMOUNT_PER_DAY = $depositLimitUser->DEP_AMOUNT_PER_DAY;
            $TRANSACTIONS_PER_DAY = $depositLimitUser->TRANSACTIONS_PER_DAY;
            $DEP_AMOUNT_PER_WEEK = $depositLimitUser->DEP_AMOUNT_PER_WEEK;
            $TRANSACTIONS_PER_WEEK = $depositLimitUser->TRANSACTIONS_PER_WEEK;
            $DEP_AMOUNT_PER_MONTH = $depositLimitUser->DEP_AMOUNT_PER_MONTH;
            $TRANSACTIONS_PER_MONTH = $depositLimitUser->TRANSACTIONS_PER_MONTH;
        } elseif ($depositLimitSetting) {
            $DEP_AMOUNT = $depositLimitSetting->DEP_AMOUNT;
            $DEP_AMOUNT_PER_DAY = $depositLimitSetting->DEP_AMOUNT_PER_DAY;
            $TRANSACTIONS_PER_DAY = $depositLimitSetting->TRANSACTIONS_PER_DAY;
            $DEP_AMOUNT_PER_WEEK = $depositLimitSetting->DEP_AMOUNT_PER_WEEK;
            $TRANSACTIONS_PER_WEEK = $depositLimitSetting->TRANSACTIONS_PER_WEEK;
            $DEP_AMOUNT_PER_MONTH = $depositLimitSetting->DEP_AMOUNT_PER_MONTH;
            $TRANSACTIONS_PER_MONTH = $depositLimitSetting->TRANSACTIONS_PER_MONTH;
        } else {
            $this->errorBag[] = [
                'code' => 422059,
                'message' => 'Transaction limit validation error'
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        // Get Deposits Made Today
        $dateFrom = date("Y-m-d") . " 00:00:00";
        $dateTo = date("Y-m-d") . " 23:59:59";
        $userDepositSumOfToday = $this->getUserDepositSum($userId, $dateFrom, $dateTo);
        $todaysAvailableLimit = $DEP_AMOUNT_PER_DAY - $userDepositSumOfToday['DEPOSITED_AMOUNT'];
        $todaysAvailableTransactions = $TRANSACTIONS_PER_DAY - $userDepositSumOfToday['TRANSACTION_COUNT'];
        // Get Deposits Made in Current Week
        $dateFrom = date("Y-m-d 00:00:00", strtotime("this week monday"));
        $dateTo = date("Y-m-d 23:59:59", strtotime("this week sunday"));
        $userDepositSumOfWeek = $this->getUserDepositSum($userId, $dateFrom, $dateTo);
        $weeksAvailableLimit = $DEP_AMOUNT_PER_WEEK - $userDepositSumOfWeek['DEPOSITED_AMOUNT'];
        $weeksAvailableTransactions = $TRANSACTIONS_PER_WEEK - $userDepositSumOfWeek['TRANSACTION_COUNT'];
        // Get Deposits Made in Current Month
        $dateFrom = date("Y-m-1") . " 00:00:00";
        $dateTo = date("Y-m-d") . " 23:59:59";
        $userDepositSumOfMOnth = $this->getUserDepositSum($userId, $dateFrom, $dateTo);
        $monthsAvailableLimit = $DEP_AMOUNT_PER_MONTH - $userDepositSumOfMOnth['DEPOSITED_AMOUNT'];
        $monthsAvailableTransactions = $TRANSACTIONS_PER_MONTH - $userDepositSumOfMOnth['TRANSACTION_COUNT'];
        if ($amount > $DEP_AMOUNT) {
            $this->errorBag[] = [
                'code' => 422007,
                'message' => 'Transaction limit exceeded',
                'available_limit' => $DEP_AMOUNT
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        if ($amount > $todaysAvailableLimit) {
            $this->errorBag[] = [
                'code' => 422008,
                'message' => 'Daily limit exceeded',
                'available_limit' => $todaysAvailableLimit
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        if ($amount > $weeksAvailableLimit) {
            $this->errorBag[] = [
                'code' => 422010,
                'message' => 'Weekly limit exceeded',
                'available_limit' => $weeksAvailableLimit
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        if ($amount > $monthsAvailableLimit) {
            $this->errorBag[] = [
                'code' => 422012,
                'message' => 'Monthly limit exceeded',
                'available_limit' => $monthsAvailableLimit
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        if ($todaysAvailableTransactions < 1) {
            $this->errorBag[] = [
                'code' => 422009,
                'message' => 'Daily transaction count limit exceeded',
                'max_daily_transactions_allowed' => $TRANSACTIONS_PER_DAY
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        if ($weeksAvailableTransactions < 1) {
            $this->errorBag[] = [
                'code' => 422011,
                'message' => 'Weekly transaction count limit exceeded',
                'max_weelky_transactions_allowed' => $TRANSACTIONS_PER_WEEK
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        if ($monthsAvailableTransactions < 1) {
            $this->errorBag[] = [
                'code' => 422013,
                'message' => 'Monthly transaction count limit exceeded',
                'max_monthly_transactions_allowed' => $TRANSACTIONS_PER_MONTH
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        return "Valid";
    }

    /* function to insert into payment_transaction table */

    public function insertIntoPaymentTransaction($userID, $paymentProviderID, $transactionTypeID, $depositAmount, $transactionStatusID, $createdBY, $createdOn, $internalRefNo, $promo_code, $paymentMethod, $requestedVia, $appType, $milestoneId)
    {
        $requestedViaArr['requestedVia'] = $requestedVia;
        $requestedViaArr['milestoneId'] = $milestoneId;
        $paymentTransaction = new PaymentTransaction();
        $paymentTransaction->USER_ID = $userID;
        $paymentTransaction->PAYMENT_PROVIDER_ID = $paymentProviderID;
        $paymentTransaction->TRANSACTION_TYPE_ID = $transactionTypeID;
        $paymentTransaction->PAYMENT_TRANSACTION_AMOUNT = $depositAmount;
        $paymentTransaction->PAYMENT_TRANSACTION_STATUS = $transactionStatusID;
        $paymentTransaction->PAYMENT_TRANSACTION_CREATED_BY = $createdBY;
        $paymentTransaction->PAYMENT_TRANSACTION_CREATED_ON = $createdOn;
        $paymentTransaction->INTERNAL_REFERENCE_NO = $internalRefNo;
        $paymentTransaction->PROMO_CODE = $promo_code;
        $paymentTransaction->PAYPAL_RETURN_VALUES = $paymentMethod;
        $paymentTransaction['PG_REFERENCE_NOS'] = $requestedViaArr;
        $paymentTransaction->APP_TYPE = $appType;
        $paymentTransactionRes = $paymentTransaction->save();
        $paymentTransactionID = $paymentTransaction->PAYMENT_TRANSACTION_ID;
        if ($paymentTransactionID) {
            $data = ['depositAmount' => $depositAmount, 'inRefNo' => $internalRefNo, 'paymentTransactionID' => $paymentTransactionID];
            $sucessData = ['status' => 'success', 'type' => 'user_payment_created', 'msg' => 'Insert into payment transaction table', 'responseData' => $data, 'code' => 200];
            $action = "USER_PAYMENT_CREATED";
            $data = json_encode($sucessData);
            $this->userActivitiesTracking($userID, $action, $data);
            return true;
        } else {
            $errorData = ['status' => 'failed', 'type' => 'user_payment_not_created', 'msg' => 'Error while Insert into payment transaction table', 'code' => 200];
            $action = "USER_PAYMENT_NOT_CREATED";
            $data = json_encode($errorData);
            $this->userActivitiesTracking($userID, $action, $data);
            $this->errorBag[] = [
                'code' => 422060,
                'message' => 'Unable to initiate payment'
            ];
            return false;
        }
    }

    public function insertIntoPaymentTransactionNew($userID, $paymentProviderID, $transactionTypeID, $depositAmount, $transactionStatusID, $createdBY, $createdOn, $internalRefNo, $promo_code, $paymentMethod, $requestedVia,$appType,$milestoneId,$extraFields=[])   
    {
       $requestedViaArr['requestedVia'] = $requestedVia;
       $requestedViaArr['milestoneId'] = $milestoneId;

       if(isset($extraFields['gateway_fee_component'])){
           $requestedViaArr['gateway_fee_component']  = $extraFields['gateway_fee_component'];
       }
           
       $requestedViaArr['amount_to_be_credited']  = round($depositAmount - $extraFields['gst']['amount'], 2);

       if(isset($extraFields['gst'])){
           $requestedViaArr['gst']  = $extraFields['gst'];
       }

       $paymentTransaction = new PaymentTransaction();
       $paymentTransaction->USER_ID = $userID;
       $paymentTransaction->PAYMENT_PROVIDER_ID = $paymentProviderID;
       $paymentTransaction->TRANSACTION_TYPE_ID = $transactionTypeID;
       $paymentTransaction->PAYMENT_TRANSACTION_AMOUNT = $depositAmount;
       $paymentTransaction->TOTAL_AMOUNT = $extraFields['gateway_fee_component']['total_amount_disc_fee_without_gst'];
       $paymentTransaction->GST = $extraFields['gst']['amount'];
       $paymentTransaction->DISC_GATEWAY_FEE = $extraFields['gateway_fee_component']['disc_deposit_fee'] - $extraFields['gateway_fee_component']['gst_on_disc_deposit_fee'];
       $paymentTransaction->DISC_GATEWAY_FEE_WITH_GST = $extraFields['gateway_fee_component']['disc_deposit_fee'];
       $paymentTransaction->GATEWAY_FEE = $extraFields['gateway_fee_component']['deposit_fee'] - $extraFields['gateway_fee_component']['gst_on_deposit_fee'];
       $paymentTransaction->GATEWAY_FEE_WITH_GST = $extraFields['gateway_fee_component']['deposit_fee'];
       $paymentTransaction->PAYMENT_TRANSACTION_STATUS = $transactionStatusID;
       $paymentTransaction->PAYMENT_TRANSACTION_CREATED_BY = $createdBY;
       $paymentTransaction->PAYMENT_TRANSACTION_CREATED_ON = $createdOn;
       $paymentTransaction->INTERNAL_REFERENCE_NO = $internalRefNo;
       $paymentTransaction->PROMO_CODE = $promo_code;
       $paymentTransaction->PAYPAL_RETURN_VALUES = $paymentMethod;
       $paymentTransaction['PG_REFERENCE_NOS'] = $requestedViaArr;
       $paymentTransaction->APP_TYPE = $appType;
       $paymentTransactionRes = $paymentTransaction->save();
       $paymentTransactionID = $paymentTransaction->PAYMENT_TRANSACTION_ID;
       if ($paymentTransactionID) {
           $data = ['depositAmount' => $depositAmount, 'inRefNo' => $internalRefNo, 'paymentTransactionID' => $paymentTransactionID];
           $sucessData = ['status' => 'success', 'type' => 'user_payment_created', 'msg' => 'Insert into payment transaction table', 'responseData' => $data, 'code' => 200];
           $action = "USER_PAYMENT_CREATED";
           $data = json_encode($sucessData);
           $this->userActivitiesTracking($userID, $action, $data);
           return true;
       } else {
           $errorData = ['status' => 'failed', 'type' => 'user_payment_not_created', 'msg' => 'Error while Insert into payment transaction table', 'code' => 200];
           $action = "USER_PAYMENT_NOT_CREATED";
           $data = json_encode($errorData);
           $this->userActivitiesTracking($userID, $action, $data);
           $this->errorBag[] = [
               'code' => 422060,
               'message' => 'Unable to initiate payment'
           ];
           return false;
       }
   }

    /* function to save payment details if not already saved */

    protected function savePaymentOptions($userID, $gateway_code, $paymentMethod, $details, $userDetails)
    {
        if ($paymentMethod == "card" && $gateway_code == config('rummy_config.juspay_code')) {
            $this->userActivitiesTracking($userID, "SAVE_CARD_DETAILS_TO_JUSPAY_INITIATED", ["PG" => "JUSPAY"]);
            $jusPaySave = $this->saveJusPayCard($userID, $details, $userDetails);
            return $jusPaySave;
        }
    }

    /* function to validate ownership and update masked details to be marked as deleted */

    protected function deletePaymentOption($jusPayToken)
    {
        $data = $this->ApiCallCurl([
            "url" => config('jusPay.jusPay_url') . "card/delete?card_token=" . $jusPayToken,
            "form_params" => [],
            "headers" => [
                "Content-Type" => "application/x-www-form-urlencoded",
                "x-merchantid" => config('jusPay.jusPay_merchant_id')
            ],
            "extra_curl_options_array" => [
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_USERPWD => config('jusPay.jusPay_api_key'),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_CUSTOMREQUEST => "GET",
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_USERPWD => config('jusPay.jusPay_api_key'),
                CURLOPT_POST => true,
            ]
        ]);
        if ($data->httpStatus == 200 && $data->response->deleted === true) {
            return $this->successResponse(1);
        } else {
            $this->errorBag[] = [
                'code' => 422040,
                'message' => 'Unable to delete payment option'
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
    }

    /* function to check transaction status and return all relevant details required as per design */

    protected function transationStatus($txn_id)
    {
        $paymentModes = [
            "UPI" => "UPI",
            "NB" => "Net Banking",
            "DEBIT" => "Debit Card",
            "CREDIT" => "Credit Card",
            "WALLET" => "Wallet"
        ];

        $query = PaymentTransaction::query();
        $query->from(app(PaymentTransaction::class)->getTable() . ' as p');
        $query->select('ts.TRANSACTION_STATUS_DESCRIPTION as status', 'p.INTERNAL_REFERENCE_NO as txnid', 'p.PAYMENT_TRANSACTION_AMOUNT as amount', 'p.DISC_GATEWAY_FEE_WITH_GST as disc_fee', 'p.GATEWAY_FEE_WITH_GST as gateway_fee', 'p.PAYMENT_TRANSACTION_CREATED_ON as created_date', 'p.UPDATED_DATE as updated_date', 'p.PROMO_CODE as deposit_code', 'mth.TRANSACTION_AMOUNT', 'mth.TRANSACTION_TYPE_ID', 'mthfppb.TRANSACTION_TYPE_ID as FPP_TRANSACTION_TYPE_ID', 'p.TRANSACTION_TYPE_ID as TRANSACTION_TYPE', 'mthfppb.TRANSACTION_AMOUNT as fpp_bonus_amt', 'p.USER_ID as user_id', 'p.PAYMENT_TRANSACTION_STATUS', 'p.PG_REFERENCE_NOS');
        $query->leftJoin('transaction_status as ts', 'p.PAYMENT_TRANSACTION_STATUS', '=', 'ts.TRANSACTION_STATUS_ID');
        $query->join('master_transaction_history as mth', 'mth.INTERNAL_REFERENCE_NO', '=', 'p.INTERNAL_REFERENCE_NO', 'left outer');
        $query->join('master_transaction_history_fppbonus as mthfppb', 'mthfppb.INTERNAL_REFERENCE_NO', '=', 'p.INTERNAL_REFERENCE_NO', 'left outer');
        $query->where('p.INTERNAL_REFERENCE_NO', $txn_id);
        $query->where(function($query) {
            $query->where('mth.TRANSACTION_TYPE_ID', '<>', 248)
            ->orwhereRaw('mth.TRANSACTION_TYPE_ID is null');
        });

        $response = $query->get();
        $depositResponse = $response->where("TRANSACTION_TYPE", 8);
        //dd($type->pluck('TRANSACTION_AMOUNT')->first());
        //dd( $depositResponse->pluck('deposit_code')->first());
        if ($response) {
            $total_amount_with_bonus = $depositResponse->pluck('amount')->first();
            $promoCode = $depositResponse->pluck('deposit_code')->first();
            $successBag['status'] = $depositResponse->pluck('status')->first();
            $successBag['txnid'] = $depositResponse->pluck('txnid')->first();
            $successBag['amount'] = $depositResponse->pluck('amount')->first();
            $successBag['gateway_fee'] = $gatewayFeeValue = $depositResponse->pluck('gateway_fee')->first();
            $successBag['processing_fee'] = $processingFee = $depositResponse->pluck('disc_fee')->first();
            $successBag['is_fee_waived_off'] = $processingFee == 0 && $gatewayFeeValue > 0 ? true : false;
            $successBag['is_fee_discounted'] = $processingFee != 0 && $processingFee < $gatewayFeeValue ? true : false;
            $successBag['created_date'] = $depositResponse->pluck('created_date')->first();
            $successBag['updated_date'] = $depositResponse->pluck('updated_date')->first();
            $successBag['deposit_code'] = $promoCode;
            $successBag['deposit_code_description'] = "";
            $successBag['deposit_code_offers']['tickets'] = [];
            $userId = $depositResponse->pluck('user_id')->first();
            $gatewayFee = $depositResponse->pluck('PG_REFERENCE_NOS')->first();
            //get all tournament details for which tickets that were issued with the promo code used
            if (!empty($promoCode)) {
                $promoCampaignDetails = PromoCampaign::query();
                $promoCampaignDetails->from(app(PromoCampaign::class)->getTable() . " as pc");
                $promoCampaignDetails->leftJoin('promo_rule as pr', 'pr.PROMO_CAMPAIGN_ID', '=', 'pc.PROMO_CAMPAIGN_ID');
                $promoCampaignDetails->where('pc.PROMO_CAMPAIGN_CODE', $promoCode);
                $promoCampaignDetails->orderBy('START_DATE_TIME', 'DESC');
                $promoCampaignDetails->limit(1);
                $promoCampaignDetails = $promoCampaignDetails->first();

                $promoResponse = $promoCampaignDetails->P_PROMO_CHIPS == 1 ? $response->where("TRANSACTION_TYPE_ID", 9) : ($promoCampaignDetails->P_UNCLAIMED_BONUS == 1 ? $response->where("FPP_TRANSACTION_TYPE_ID", 9) : $response->filter(function ($value, $key) {
                    return ($value->FPP_TRANSACTION_TYPE_ID == 9 || $value->FPP_TRANSACTION_TYPE_ID == 9);
                }));

                /**
                 * If promo is not processed yet(possible in case of ftd & pending or failed transaction)
                 */
                
                if (empty($promoResponse->toArray()) && $promoCampaignDetails->P_TOURNAMENT_TICKETS != 1) {
                        $successBag['deposit_code_description'] = $promoCampaignDetails->PROMO_CAMPAIGN_DESC;

                        $transactionStatusID = $depositResponse->pluck('PAYMENT_TRANSACTION_STATUS')->first();
                        $userId = $depositResponse->pluck('user_id')->first();

                        /**
                         * In Case of Pending or Failed Transaction
                         */
                        if($transactionStatusID == config('rummy_config.TRANSACTION_STATUS_CODE.DEPOSIT_PENDING') || $transactionStatusID == config('rummy_config.TRANSACTION_STATUS_CODE.DEPOSIT_FAILED')) {
                            $promoDetails = $this->getPromoValueAndTypeByPromoCampaignDetail($promoCampaignDetails, $successBag['amount'], $userId);

                            if(!empty($promoDetails)){
                                $successBag['deposit_code_offers']['bonus_type'] = $promoDetails['type'];
                                $successBag['deposit_code_offers']['bonus_value'] = "₹" .$promoDetails['value'];
                                if($promoDetails['type'] == "SB, RCB"){
                                    $successBag['deposit_code_offers']['bonus']['RCB'] = $promoDetails['rcb'];
                                    $successBag['deposit_code_offers']['bonus']['SB'] = $promoDetails['sb'];
                                } else if($promoDetails['type'] == "Special Bonus"){
                                    $successBag['deposit_code_offers']['bonus']['SB'] = $promoDetails['value'];
                                } else if($promoDetails['type'] == "Real Cash Bonus" ){
                                    $successBag['deposit_code_offers']['bonus']['RCB'] = $promoDetails['value'];
                                }
                            }
                            $total_amount_with_bonus += $promoDetails['total_bonus'];
                        } else if($transactionStatusID == config('rummy_config.TRANSACTION_STATUS_CODE.DEPOSIT_SUCCESS')) {
                            /**
                             * In Case of Succesfull Transaction
                             */
                            $bonusQuery = BonusTransactionHistory::query();
                            $bonusQuery->from(app(BonusTransactionHistory::class)->getTable() . ' as bth');
                            $bonusQuery->select('ts.TRANSACTION_STATUS_DESCRIPTION as status', 'bth.TRANSACTION_AMOUNT as RCB', 'bth.OTHER_DETAILS');
                            $bonusQuery->leftJoin('transaction_status as ts', 'bth.TRANSACTION_STATUS_ID', '=', 'ts.TRANSACTION_STATUS_ID');
                            $bonusQuery->where('bth.INTERNAL_REFERENCE_NO', $txn_id);
                            $bonusDetails = $bonusQuery->first();
                            if ($bonusDetails) {
    
                                if (!empty($promoCampaignDetails)) {
                                    /**
                                     * Check Coin Type Id from Promo Campaign Table
                                     * 
                                     * |Coin Type Id  |   Name                     |
                                     * |        1     |   Real Cash Bonus          |
                                     * |        3     |   Locked Bonus or FPP Bonus|
                                     */
                                    $bonusType = $promoCampaignDetails->P_PROMO_CHIPS == 1 ? "rcb" : ($promoCampaignDetails->P_UNCLAIMED_BONUS == 1 ? "lb": "");
    
                                    if(!empty($bonusType)) {
                                        $successBag['deposit_code_offers']['bonus_type'] = $bonusType == "rcb" ? "Real Cash Bonus" : ($promoCampaignDetails->P_UNCLAIMED_BONUS == 1 ? "Special Bonus" : "SB, RCB");
                                        $successBag['deposit_code_offers']['bonus_value'] = (int)$bonusDetails->RCB;
                                    }
                                }
    
                                $total_amount_with_bonus += $bonusDetails->RCB;
                                $successBag['bonus_status'] = $bonusDetails['status'];
                                if ($bonusDetails['status'] == "Pending" || $bonusDetails['status'] == 'Promo Pending') {
                                    $kycMethod = GlobalConfig::select('CONFIG_VALUE')->where('CONFIG_KEY', 'kyc_method')->pluck('CONFIG_VALUE')->first();
                                    $digilockerUrl = '';
                                    if ($kycMethod != 'manual') {
                                        $digilockerUrlResponse = $this->generateDigilockerUrl($userId);
                                        if ($digilockerUrlResponse) {
                                            if ($digilockerUrlResponse == "Max KYC Attempt Reached") {
                                                $successBag['digilocker_url_error'] = "Max KYC Attempt Reached";
                                                $this->userActivitiesTracking($userId, "GIFT_VOUCHER_KYC_INITIATE_ERROR", ["message" => "Max KYC Attempy Reached."]);
                                            } elseif ($digilockerUrlResponse == "Aadhar Already Verified") {
                                                $successBag['digilocker_url_error'] = "Aadhar Already Verified";
                                                $this->userActivitiesTracking($userId, "GIFT_VOUCHER_KYC_INITIATE_ERROR", ["message" => "KYC Already Done"]);
                                            } else {
                                                $digilockerUrl = $digilockerUrlResponse;
                                            }
                                        } else {
                                            $successBag['digilocker_url_error'] = "There was some error in generating digilocker URL";
                                        }
                                    }
                                    $successBag['release_document'] = $kycMethod == 'manual' ? "kyc" : "aadhaar";
                                    $successBag['digilocker_url'] = $digilockerUrl;
                                }
                                $userDetails = $this->getUserDetails($userId);
                                $state = \App\Models\State::where('StateName', $userDetails->STATE)->first();
                                if ($state) {
                                    $successBag['state_data']['state_id'] = $state->StateID;
                                    $successBag['state_data']['state_name'] = $userDetails->STATE;
                                }
                            }
                            if (!empty($bonusDetails['OTHER_DETAILS']['BENIFITS']['TOURNAMENTS'])) {
                                $tournamentDetails = [];
                                foreach ($bonusDetails['OTHER_DETAILS']['BENIFITS']['TOURNAMENTS'] as $tournament) {
                                    array_push($tournamentDetails, ['name' => $tournament['TOURANMENT_NAME'], 'start_date' => $tournament['START_DATE']]);
                                }
                                $successBag['deposit_code_offers']['tickets'] = $tournamentDetails;
                            }
                        }
                } else {
                    /**
                     * If promo is already processed
                     */
                    if (!empty($promoCampaignDetails)) {
                        $successBag['deposit_code_description'] = $promoCampaignDetails->PROMO_CAMPAIGN_DESC;

                        $bonusType = $promoCampaignDetails->P_UNCLAIMED_BONUS == 1 ? "Special Bonus" : ($promoCampaignDetails->P_PROMO_CHIPS == 1 ? "Real Cash Bonus" : ("SB, RCB"));

                        $specialBonusValue = $promoResponse->pluck('fpp_bonus_amt')->first();
                        $rcbBonusValue = $promoResponse->pluck('TRANSACTION_AMOUNT')->first();

                        $bonusValue = $bonusType == "Special Bonus" ? $promoResponse->pluck('fpp_bonus_amt')->first() : ($bonusType == "Real Cash Bonus" ? $promoResponse->pluck('TRANSACTION_AMOUNT')->first() : (
                                ($specialBonusValue + $rcbBonusValue) . " (₹$specialBonusValue SB + ₹$rcbBonusValue RCB)"
                            )
                        );

                        if(!empty($bonusValue) && !empty($bonusType)) {
                            if(!empty($bonusType)) {
                                $successBag['deposit_code_offers']['bonus_type'] = $bonusType;
                                $successBag['deposit_code_offers']['bonus_value'] = "₹" . $bonusValue;
                                if($bonusType == "SB, RCB"){
                                    $successBag['deposit_code_offers']['bonus']['RCB'] = $rcbBonusValue;
                                    $successBag['deposit_code_offers']['bonus']['SB'] = $specialBonusValue;
                                } else if($bonusType == "Special Bonus"){
                                    $successBag['deposit_code_offers']['bonus']['SB'] = $specialBonusValue;
                                } else if($bonusType == "Real Cash Bonus" ){
                                    $successBag['deposit_code_offers']['bonus']['RCB'] = $rcbBonusValue;
                                }
                                
                            }
                            if($bonusType == "SB, RCB") {
                                $total_amount_with_bonus = $total_amount_with_bonus + $specialBonusValue+ $rcbBonusValue;
                            } else {
                                $total_amount_with_bonus += $bonusValue;
                            }
                        }

                        if ($promoCampaignDetails->P_TOURNAMENT_TICKETS == 1) {
                            $tournamentTicketIDs = CampaignTournamentMapping::select('TOURNAMENT_ID')->where('PROMO_CAMPAIGN_ID',$promoCampaignDetails->PROMO_CAMPAIGN_ID)->get()->pluck('TOURNAMENT_ID');
                            $tournamentDetails = TournamentsReinRummy::select('TOURNAMENT_DESC', 'START_DATETIME')->whereIn('TOURNAMENT_ID', $tournamentTicketIDs)->get();
                            $successBag['deposit_code_offers']['tickets'] = $tournamentDetails;
                        }
                    }
                }
            }
            $successBag['total_amount_with_bonus'] = $gatewayFee['gst']['gst_waived_off'] ? $total_amount_with_bonus - ((isset($gatewayFee['gst']['amount']) && isset($gatewayFee['gst']['amount_to_be_refunded'])) ? ($gatewayFee['gst']['amount']- $gatewayFee['gst']['amount_to_be_refunded']) : 0) : $total_amount_with_bonus - $gatewayFee['gst']['amount'];
            $successBag['payment_method'] = isset($gatewayFee['paymentMethodType']) ? $paymentModes[$gatewayFee['paymentMethodType']] : "";
            $successBag['gateway_fee_breakup'] = $gatewayFee['gateway_fee_component'] ?? "";
            $successBag['gst'] = $gatewayFee['gst'] ?? "";
            $successBag['amount_to_be_credited'] = $gatewayFee['amount_to_be_credited'] ?? "";
            $successBag['gst_applicable_on_deposit'] = (isset($gatewayFee['gst']) && $gatewayFee['gst']['amount'] > 0) ? true : false;
            return $this->successResponse(1, $successBag);
        } else {
            //return error if no results found for the supplied transaction id
            $this->errorBag[] = [
                'code' => 422088,
                'message' => 'No Results found'
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
    }

    protected function checkJusPayCustomerDetails($userId, $user)
    {

        if (isset($user['USER_OTHER_DETAILS']['jusPay_Cust_ID'])) {
            return "Valid";
        } else {
            $jusPay = $this->jusPayCreateCustomer($user->EMAIL_ID, $user->CONTACT, $user->FIRSTNAME, $user->LASTNAME, $userId);
            if (isset($jusPay->error_code)) {
                $this->errorBag[] = [
                    'code' => 422091,
                    'message' => 'JusPay Customer ID already exist for current user\'s mobile'
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            } elseif (!empty($jusPay->id)) {
                $user = User::find($userId);
                $testVar = $user->USER_OTHER_DETAILS;
                $testVar['jusPay_Cust_ID'] = $jusPay->id;
                $user->USER_OTHER_DETAILS = $testVar;
                if ($user->save()) {
                    return "Valid";
                } else {
                    return false;
                }
            } else {
                $this->userActivitiesTracking($userId, "UNBALE_TO_CREATE_JUSPAY_ACCOUNT_FOR_MOBILE_NUMBER_SPECIFIED", $jusPay);
                $this->errorBag[] = [
                    'code' => 422091,
                    'message' => 'Couldn\'t initiate payment'
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            }
        }
    }

    protected function retrieveAndUpdateJusPayCustomerDetail($userId, $user)
    {
        $data = $this->ApiCallCurl([
            "url" => config('jusPay.jusPay_url') . "customers/" . $user->CONTACT,
            "form_params" => [],
            "headers" => [
                "Content-Type" => "application/x-www-form-urlencoded",
                "x-merchantid" => config('jusPay.jusPay_merchant_id')
            ],
            "extra_curl_options_array" => [
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_USERPWD => config('jusPay.jusPay_api_key'),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_CUSTOMREQUEST => "GET",
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_USERPWD => config('jusPay.jusPay_api_key'),
                CURLOPT_POST => true,
            ]
        ]);
        if ($data->httpStatus == 200) {
            $user = User::find($userId);
            $testVar = $user->USER_OTHER_DETAILS;
            $testVar['jusPay_Cust_ID'] = $data->response->id;
            $user->USER_OTHER_DETAILS = $testVar;
            if ($user->save()) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    protected function jusPayCreateCustomer($email, $mobile, $firstName, $lastName, $userId)
    {
        $firstName = preg_replace('/[^a-z]/i', '', $firstName);
        $lastName = preg_replace('/[^a-z]/i', '', $lastName);
        $data = $this->ApiCallCurl([
            "url" => config('jusPay.jusPay_url') . "customers?mobile_number=" . $mobile . "&object_reference_id=" . $mobile . "&email_address=" . $email . "&first_name=" . $firstName . "&last_name=" . $lastName . "&mobile_country_code=91&options.get_client_auth_token=true",
            "form_params" => [],
            "headers" => [
                "Content-Type" => "application/x-www-form-urlencoded",
                "x-merchantid" => config('jusPay.jusPay_merchant_id')
            ],
            "extra_curl_options_array" => [
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_USERPWD => config('jusPay.jusPay_api_key'),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_USERPWD => config('jusPay.jusPay_api_key'),
                CURLOPT_POST => true,
            ]
        ]);

        if (!empty($data->response)) {
            return $data->response;
        } else {
            $this->userActivitiesTracking($userId, "UNBALE_TO_CREATE_JUSPAY_ACCOUNT_FOR_USER_SPECIFIED", $data);
            return false;
        }
    }

    protected function fetchJusPayEnabledPaymentOptions()
    {
        $data = $this->ApiCallCurl([
            "url" => config('jusPay.jusPay_url') . "merchants/" . config('jusPay.jusPay_merchant_id') . "/paymentmethods?options.add_outage=true",
            "form_params" => [],
            "headers" => [
                "Content-Type" => "application/x-www-form-urlencoded",
                "x-merchantid" => config('jusPay.jusPay_merchant_id')
            ],
            "extra_curl_options_array" => [
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_USERPWD => config('jusPay.jusPay_api_key'),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_CUSTOMREQUEST => "GET",
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_USERPWD => config('jusPay.jusPay_api_key'),
                CURLOPT_POST => true,
            ]
        ]);
        return $data->response;
    }

    protected function getJusPayOutages()
    {
        /*
          $ch = curl_init();
          curl_setopt_array($ch, array(
          CURLOPT_URL => config('jusPay.jusPay_url') . "txns/outages",
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => "",
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_USERPWD => config('jusPay.jusPay_api_key'),
          CURLOPT_POST => true,
          CURLOPT_HTTPHEADER => array(
          "Content-Type: application/x-www-form-urlencoded",
          "x-merchantid:" . config('jusPay.jusPay_merchant_id')
          ),
          ));
          $jusPayresponse = curl_exec($ch);
          curl_close($ch);
          return json_decode($jusPayresponse, true); */
    }

    public function checkCardValidation($cardsixdigits, $userID)
    {
        $url = config('jusPay.jusPay_url') . "cardbins/$cardsixdigits?merchant_id=" . config('jusPay.jusPay_merchant_id') . "&options.check_atm_pin_auth_support=true";

        $data = $this->ApiCallCurl([
            "url" => $url,
            "form_params" => [],
            "headers" => [
                "Content-Type" => "application/x-www-form-urlencoded",
                "x-merchantid" => config('jusPay.jusPay_merchant_id')
            ],
            "extra_curl_options_array" => [
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_USERPWD => config('jusPay.jusPay_api_key'),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_CUSTOMREQUEST => "POST"
            ]
        ]);
        $this->userActivitiesTracking($userID, "VALIDATE_CARD_DETAILS_FROM_JUSPAY_RESPONSE", $data);
        if ($data->httpStatus == 200) {
            return !empty($data->response->type) ? true : false;
        } else {
            return false;
        }
    }

    protected function saveJusPayCard($userID, $details, $userDetails)
    {
        if (isset($userDetails['USER_OTHER_DETAILS']['jusPay_Cust_ID'])) {
            $expiryMonth = 0;
            if (strlen($details['expiry_month']) < 2) {
                $expiryMonth .= $details['expiry_month'];
            } else {
                $expiryMonth = $details['expiry_month'];
            }
            $url = config('jusPay.jusPay_url') . "card/add?merchant_id=" . config('jusPay.jusPay_merchant_id') . "&customer_id=" . $userDetails['USER_OTHER_DETAILS']['jusPay_Cust_ID'] . "&customer_email=" . $userDetails->EMAIL_ID . "&card_number=" . $details['card_number'] . "&card_exp_year=" . $details['expiry_year'] . "&card_exp_month=" . $expiryMonth . "&name_on_card=" . rawurlencode($details['name']);
            if ($this->checkCardValidation(substr($details['card_number'], 0, 6), $userID)) {
                $data = $this->ApiCallCurl([
                    "url" => $url,
                    "form_params" => [],
                    "headers" => [
                        "Content-Type" => "application/x-www-form-urlencoded",
                        "x-merchantid" => config('jusPay.jusPay_merchant_id')
                    ],
                    "extra_curl_options_array" => [
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_USERPWD => config('jusPay.jusPay_api_key'),
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => "",
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_CUSTOMREQUEST => "POST"
                    ]
                ]);
                $this->userActivitiesTracking($userID, "SAVE_CARD_DETAILS_TO_JUSPAY_RESPONSE", $data);
                if (empty($data->response)) {
                    $this->errorBag[] = [
                        'code' => 422062,
                        'message' => 'Unable to save payment details'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                }
                return $data->response;
            } else {
                $this->errorBag[] = [
                    'code' => 422099,
                    'message' => 'Invalid Card Details'
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            }
        } else {
            $this->errorBag[] = [
                'code' => 422062,
                'message' => 'Unable to save payment details'
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
    }

    protected function jusPayOrderCreate($txnid, $amount, $jusPayCustID, $customerEmail, $customerPhone, $productID, $userId, $requestedVia)
    {
        $url = config('jusPay.jusPay_url') . "orders?order_id=" . $txnid . "&amount=" . $amount . "&currency=" . config('jusPay.jusPay_default_currency') . "&customer_id=" . $jusPayCustID . "&customer_email=" . $customerEmail . "&customer_phone=" . $customerPhone . "&product_id=" . $productID . "&udf1=" . config('rummy_config.juspay_code') . "&udf2=" . $txnid . "&udf3=" . $requestedVia . "&options.get_client_auth_token=true";
        $data = $this->ApiCallCurl([
            "url" => $url,
            "form_params" => [],
            "headers" => [
                "Content-Type" => "application/x-www-form-urlencoded",
                "x-merchantid" => config('jusPay.jusPay_merchant_id'),
                "version" => config('jusPay.jusPay_version')
            ],
            "extra_curl_options_array" => [
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_USERPWD => config('jusPay.jusPay_api_key'),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_CUSTOMREQUEST => "POST",
            ]
        ]);

        $this->userActivitiesTracking($userId, "DEPOSIT_API_JUSPAY_ORDER_CREATION", ['request' => $url, 'response' => $data]);
        $jusPayResponse = (array)$data->response;
        if (!empty($jusPayResponse['status'])) {
            if ($jusPayResponse['status'] == 'CREATED' || $jusPayResponse['status'] == 'NEW') {
                return $jusPayResponse;
            }
        }
        return false;
    }

    protected function jusPayTokenizeCard($details, $card_security_code, $userId)
    {
        $data = $this->ApiCallCurl([
            "url" => config('jusPay.jusPay_url') . "card/tokenize?card_number=" . $details['card_number'] . "&merchant_id=" . config('jusPay.jusPay_merchant_id') . "&card_exp_year=" . $details['expiry_year'] . "&card_exp_month=" . $details['expiry_month'] . "&card_security_code=" . $card_security_code . "&name_on_card=" . rawurlencode($details['name']),
            "form_params" => [],
            "headers" => [
                "Content-Type" => "application/x-www-form-urlencoded",
                "x-merchantid" => config('jusPay.jusPay_merchant_id'),
                "version" => config('jusPay.jusPay_version')
            ],
            "extra_curl_options_array" => [
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_USERPWD => config('jusPay.jusPay_api_key'),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_CUSTOMREQUEST => "POST",
            ]
        ]);
        $this->userActivitiesTracking($userId, "DEPOSIT_API_TOKENIZE_CARD_RESPONSE", $data);
        return $data->response->token;
    }

    protected function validateVPA($vpa, $userId)
    {
        $data = $this->ApiCallCurl([
            "url" => config('jusPay.jusPay_url') . "v2/upi/verify-vpa?vpa=" . $vpa . "&merchant_id=" . config('jusPay.jusPay_merchant_id'),
            "form_params" => [],
            "headers" => [
                "Content-Type" => "application/x-www-form-urlencoded",
                "x-merchantid" => config('jusPay.jusPay_merchant_id'),
                "version" => config('jusPay.jusPay_version')
            ],
            "extra_curl_options_array" => [
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_USERPWD => config('jusPay.jusPay_api_key'),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_CUSTOMREQUEST => "POST",
            ]
        ]);
        $this->userActivitiesTracking($userId, "DEPOSIT_API_VALIDATE_VPA_RESPONSE", $data);
        if (!empty($data->httpStatus) && $data->httpStatus == 200) {
            if (!empty($data->response->status ?? null) && $data->response->status == "VALID") {
                return true;
            }
        }
        return false;
    }

    /* FUNCTIONS USED FOR WEB HOOKS :: START */
    /* FUNCTION TO CHECK IF TRANSACTION ID EXIST IN PAYMENT TRANSACTION TABLE */

    protected function transactionExistInPaymentTransaction($referenceNumber)
    {
        return PaymentTransaction::select('USER_ID', 'PAYMENT_TRANSACTION_AMOUNT', 'PAYMENT_TRANSACTION_STATUS')->where([
            'INTERNAL_REFERENCE_NO' => $referenceNumber
        ])->limit(1)->first();
    }

    /* FUNCTION TO CHECK IF TRANSACTION ID EXIST IN MASTER TRANSACTION TABLE */

    protected function transactionExistInMasterTransaction($referenceNumber, $userID)
    {
        return MasterTransactionHistory::where([
            'INTERNAL_REFERENCE_NO' => $referenceNumber,
            'USER_ID' => $userID
        ])->limit(1)->first();
    }

    protected function applyPromoCode($promoCampaign, object $user, object $transaction, $processBonus)
    {
        $transactionAmount = $transaction->PAYMENT_TRANSACTION_AMOUNT;
        $levelConfigId = $user->LEVEL_CONFIG_ID;
        $applyPromoResponse = array();
        $applyPromoResponse['bonusType'] = "Promotions";
        $applyPromoResponse['bonusAmount'] = 0;
        $applyPromoResponse['expiryDate'] = NULL;
        if (in_array($promoCampaign->PROMO_CAMPAIGN_TYPE_ID, [1])) {
            if ($promoCampaign->TIER_TYPE == 1) {
                $tier_bonus = json_decode($promoCampaign->TIER_BONUS_DATA);
                $tier_bonus_collect = collect($tier_bonus)->where("level", $levelConfigId)->toArray();
                if (!empty($tier_bonus_collect)) {
                    $tier_bonus_collect = $tier_bonus_collect[$levelConfigId - 1];
                    $bonus_percentage = $tier_bonus_collect->bonus_percentage;

                    $promoCodeValue = ($transactionAmount / 100) * $bonus_percentage;
                    $bonus_cap_value = $tier_bonus_collect->bonus_cap_value;

                    $promoCodeValue = ($promoCodeValue > $bonus_cap_value) ? $bonus_cap_value : $promoCodeValue;
                    $applyPromoResponse['bonusAmount'] = $promoCodeValue;

                    if ($promoCampaign->P_UNCLAIMED_BONUS == 1) 
                    {
                        if ($promoCodeValue > 0) {
                            $applyPromoResponse['bonusType'] = "Special Bonus";
                        }
                        $expiry_date_db = $promoCampaign->BONUS_EXPIRY_INTERVAL_DAYS;
                        $redeem_dates = date("Y-m-d H:i:s");
                        $expiry_date = date('Y-m-d H:i:s', strtotime("+" . $expiry_date_db . " days", strtotime($redeem_dates)));
                        $applyPromoResponse['expiryDate'] = $expiry_date;
                        $this->payment_unclaimed_bonus($promoCampaign, $user, $transaction, $promoCodeValue,$processBonus);
                    } 
                    elseif ($promoCampaign->P_PROMO_CHIPS == 1) 
                    {
                        if ($promoCodeValue > 0) {
                            $applyPromoResponse['bonusType'] = "RCB";
                        }
                        $this->payment_claimed_bonus($promoCampaign, $user, $transaction, $promoCodeValue,$processBonus);
                    } else {
                        $this->userActivitiesTracking($user->USER_ID, "Campaign Value is Empty - " . $transaction->INTERNAL_REFERENCE_NO, [
                            "promo_campaign" => $promoCampaign
                        ]);
                    }
                } else {
                    $this->userActivitiesTracking($user->USER_ID, "Campaign Value is Empty - " . $transaction->INTERNAL_REFERENCE_NO, [
                        "promo_campaign" => $promoCampaign,
                        "tier_bonus" => $tier_bonus,
                        "tier_bonus_collect" => $tier_bonus_collect
                    ]);
                }
            } else {
                $tournamentIds = [];
                if ($promoCampaign->P_PROMO_CHIPS == 1 || $promoCampaign->P_COMBO_REWARD == 1) {
                    $PPromPercentageVal = (($transactionAmount * $promoCampaign->P_PROMO_VALUE) / 100);
                    $PPromoCap = $promoCampaign->P_PROMO_CAP_VALUE;
                    $promoCodeValue = $PPromPercentageVal > $PPromoCap ? $PPromoCap : $PPromPercentageVal;
                    $applyPromoResponse['bonusAmount'] = $promoCodeValue;
                    if ($promoCodeValue > 0) {
                        $applyPromoResponse['bonusType'] = "RCB";
                    }
                    $this->payment_claimed_bonus($promoCampaign, $user, $transaction, $promoCodeValue, $processBonus);

                    if($promoCampaign->P_COMBO_REWARD == 1) {
                        $UBonusPercentageVal = (($transactionAmount * $promoCampaign->P_PROMO_VALUE_COMBO) / 100);
                        $UBonusCap = $promoCampaign->P_PROMO_CAP_VALUE_COMBO;
                        $promoCodeValue = ($UBonusCap && ($UBonusPercentageVal > $UBonusCap) ) ? $UBonusCap : $UBonusPercentageVal;
                        $applyPromoResponse['bonusAmount'] = $applyPromoResponse['bonusAmount'] + $promoCodeValue;
                        if ($promoCodeValue > 0) {
                            $applyPromoResponse['bonusType'] = "RCB + Special Bonus";
                        }
                        $expiry_date_db = $promoCampaign->BONUS_EXPIRY_INTERVAL_DAYS;
                            $redeem_dates = date("Y-m-d H:i:s");
                            $expiry_date = date('Y-m-d H:i:s', strtotime("+" . $expiry_date_db . " days", strtotime($redeem_dates)));
                            $applyPromoResponse['expiryDate'] = $expiry_date;
                        $this->payment_unclaimed_bonus($promoCampaign, $user, $transaction, $promoCodeValue , $processBonus);
                    }
                } elseif ($promoCampaign->P_UNCLAIMED_BONUS == 1) {
                    $UBonusPercentageVal = (($transactionAmount * $promoCampaign->P_PROMO_VALUE) / 100);
                    $UBonusCap = $promoCampaign->P_PROMO_CAP_VALUE;
                    $promoCodeValue = ($UBonusCap && ($UBonusPercentageVal > $UBonusCap) ) ? $UBonusCap : $UBonusPercentageVal;
                    $applyPromoResponse['bonusAmount'] = $promoCodeValue;
                    if ($promoCodeValue > 0) {
                        $applyPromoResponse['bonusType'] = "Special Bonus";
                    }
                    $expiry_date_db = $promoCampaign->BONUS_EXPIRY_INTERVAL_DAYS;
                        $redeem_dates = date("Y-m-d H:i:s");
                        $expiry_date = date('Y-m-d H:i:s', strtotime("+" . $expiry_date_db . " days", strtotime($redeem_dates)));
                        $applyPromoResponse['expiryDate'] = $expiry_date;
                    $this->payment_unclaimed_bonus($promoCampaign, $user, $transaction, $promoCodeValue , $processBonus);
                }
                if ($promoCampaign->P_TOURNAMENT_TICKETS == 1) {
                    $this->userActivitiesTracking($user->USER_ID, "Initiate Tournament Ticket - " . $transaction->INTERNAL_REFERENCE_NO, [
                        "promo_campaign" => $promoCampaign
                    ]);
                    $tournamentNames = array();
                    if (!empty($promoCampaign->PROMO_CAMPAIGN_ID)) {
                        $tournamentNames = $this->getTournamentsForPromoCampaign($promoCampaign->PROMO_CAMPAIGN_ID);
                    }
                    // if (!empty($promoCampaign->TOURNAMENT_NAME)) {
                    //     array_push($tournamentNames, $promoCampaign->TOURNAMENT_NAME);
                    // }

                    if (!empty($tournamentNames)) {
                        $tournamentNames = array_unique($tournamentNames);
                        $applyPromoResponse['bonusType'] = implode(",", $tournamentNames);
                        $tournaments = [];
                        $tournamentRegRefs = [];

                        foreach ($tournamentNames as $tournamentName) {
                            /**
                             * ---------------------------------------------------------------------
                             * Check if tournament is mapped
                             * ----------------------------------------------------------------------
                             */
                            $chktournamentInfo = TournamentsReinRummy::select(
                                'TOURNAMENT_ID',
                                'TOURNAMENT_DESC',
                                'ENTRY_FEE',
                                'START_DATETIME',
                                'END_DATETIME'
                            )->where('TOURNAMENT_ID', $tournamentName)
                                ->where('STATUS', 1)
                                ->where('START_DATETIME', '>', date('Y-m-d H:i:s'))
                                ->oldest('START_DATETIME')
                                ->limit(1)
                                ->first();

                            if (!empty($chktournamentInfo->TOURNAMENT_ID)) {
                                /**
                                 * ---------------------------------------------------------------------
                                 * Check if already assigned
                                 * ----------------------------------------------------------------------
                                 */
                                $ticketCount = TournamentUserTicket::where('TOURNAMENT_ID', $chktournamentInfo->TOURNAMENT_ID)
                                    ->where('USER_ID', $transaction->USER_ID)
                                    ->limit(1)
                                    ->count();
                                $trnmtUsrTicket = null;
                                if ($ticketCount < 1) {
                                    array_push($tournamentIds, $chktournamentInfo->TOURNAMENT_ID);
                                    $source = "Deposit_Code";
                                    $reason = $transaction->PROMO_CODE;
                                    $intRefNo = "123" . $transaction->USER_ID . date('dmyhis');
                                    $trnmtUsrTicket = TournamentUserTicket::create([
                                        'TOURNAMENT_ID' => $chktournamentInfo->TOURNAMENT_ID,
                                        'USER_ID' => $transaction->USER_ID,
                                        'INTERNAL_REFERENCE_NO' => $intRefNo,
                                        'GENERATED_TICK_MODE' => 2,
                                        'TICKET_STATUS' => 2,
                                        'TICKET_SOURCE' => $source,
                                        'TICKET_REASON' => $reason,
                                        'CREATED' => date('Y-m-d H:i:s'),
                                        'UPDATED' => date('Y-m-d H:i:s')
                                    ]);

                                    $data = [
                                        'identity' => $transaction->USER_ID,
                                        'USER_ID' => $transaction->USER_ID,
                                        'TOURNAMENT_ID' => $chktournamentInfo->TOURNAMENT_ID,
                                        'TICKET_SOURCE' => $source,
                                        'TICKET_REASON' => $reason,
                                        'GAME_NAME' => "Rummy",
                                        'TOURNAMENT_NAME' => $chktournamentInfo->TOURNAMENT_DESC,
                                        'START_TIME' => $chktournamentInfo->START_DATETIME,
                                        'END_TIME' => $chktournamentInfo->END_DATETIME
                                    ];
                                    
                                    $ctData = [
                                        "type" => "event",
                                        "type_name" => "Tournament Ticket Rewarded",
                                        "data" => $data
                                    ];
                    
                                    try {
                                        $this->putOnKinesis($ctData);
                                        $this->sendDataToCardbaaziRealTimeEventsTable($transaction->USER_ID, "Tournament Ticket Rewarded", $data);
                                    } catch (\Exception $e) {
                                        Log::error($e);
                                    }
                    
                                    // $getProcResult = $this->executeProcedureTicket($chktournamentInfo->TOURNAMENT_ID, $transaction->USER_ID, $source, $reason);
                                    // if (!empty($getProcResult) && $getProcResult != '2' && $getProcResult != '3') {
                                    //     //array_push($tournamentIds, $chktournamentInfo->TOURNAMENT_ID);
                                    //     array_push($tournaments, $chktournamentInfo);
                                    //     array_push($tournamentRegRefs, $getProcResult);

                                    //     /**
                                    //      * ! comment due to redis not implemented in txn api
                                    //      */
                                    //     // self::publisRedis('tournamentAutoRegister', $data);
                                    // } else {
                                    //     $this->userActivitiesTracking($user->USER_ID, "Tournament Auto Un-Register - " . $transaction->INTERNAL_REFERENCE_NO, [
                                    //         'tournamentId' => $chktournamentInfo->TOURNAMENT_ID,
                                    //         'userId' => $user->USER_ID,
                                    //         'referenceNo' => $getProcResult
                                    //     ]);
                                    // }
                                }
                            }
                        }

                        if (!empty($trnmtUsrTicket)) {
                            $data = [
                                'tournamentIds' => $tournamentIds,
                                'userId' => $user->USER_ID,
                                'referenceNo' => $intRefNo,
                                "PromoValues" => $promoCampaign,
                                "PromoCode" => $transaction->PROMO_CODE
                            ];

                            $this->userActivitiesTracking($user->USER_ID, "Tournament Auto Register - " . $transaction->INTERNAL_REFERENCE_NO, $data);
                        }

                        $checkPromoCodeUsage = $this->promoCodeUsageCheck($promoCampaign->PROMO_CAMPAIGN_ID, $user->USER_ID);
                        if ($checkPromoCodeUsage == false) {
                            $bonus_amount = $bonus_status = $coin_type_bonus_id = $status = 0;

                            CampaignToUser::create([
                                'PROMO_CAMPAIGN_ID' => $promoCampaign->PROMO_CAMPAIGN_ID,
                                'USER_ID' => $user->USER_ID,
                                'BONUSE_TYPE' => "Ticket",
                                'BONUS_AMOUNT' => $bonus_amount,
                                'BALANCE_BONUS_AMOUNT' => $bonus_amount,
                                'REDEEM_DATE' => date('Y-m-d H:i:s'),
                                'EXPIRY_DATE' => date('Y-m-d H:i:s'),
                                'STATUS' => $status,
                                'BONUS_STATUS' => $bonus_status,
                                'CAMPAIGN_CODE' => $transaction->PROMO_CODE,
                                'CAMPAIGN_NAME' => $promoCampaign->PROMO_CAMPAIGN_NAME,
                                'COIN_TYPE_ID' => $coin_type_bonus_id
                            ]);
                        }
                    }
                }
            }
            if (!empty($tournamentIds)) {
                $applyPromoResponse['tournamentIDs'] = $tournamentIds;
            }
            return $applyPromoResponse;
        } else {
            $this->userActivitiesTracking($user->USER_ID, "Invalid Campaign type - " . $transaction->INTERNAL_REFERENCE_NO, [
                "promo_campaign" => $promoCampaign
            ]);
        }

        return $applyPromoResponse;
    }

    private function payment_unclaimed_bonus($promoCampaign, object $user, object $transaction, $promoCodeValue, $processBonus)
    {
        $transactionVariablesArr = config('rummy_config.transactionVariables');

        $expiry_date_db = $promoCampaign->BONUS_EXPIRY_INTERVAL_DAYS;
        $redeem_dates = date("Y-m-d H:i:s");
        $expiry_date = date('Y-m-d H:i:s', strtotime("+" . $expiry_date_db . " days", strtotime($redeem_dates)));

        $userID = $user->USER_ID;
        $campaignCodeBonus = $transaction->PROMO_CODE;
        $campaignNameBonus = $promoCampaign->PROMO_CAMPAIGN_NAME;
        $promo_campaign_id = $promoCampaign->PROMO_CAMPAIGN_ID;
        $end_date_time = $promoCampaign->END_DATE_TIME;
        $bonus_release_per = $promoCampaign->BONUS_RELEASE_PER;
        $internalRefNop = "123" . $userID . date('dmyhis');
        $bonus_type = "Special";
        $redeem_date = Carbon::now();
        $coin_type_bonus_id = 3;

        $userCampCount = CampaignToUser::where('USER_ID', $userID)
            ->where('COIN_TYPE_ID', $coin_type_bonus_id)
            ->where('BONUS_STATUS', 1)
            ->count();

        if ($userCampCount != 1) {
            $status = 1;
            $bStatus = 1;
        } else {
            $status = 2;
            $bStatus = 2;
        }

        if($processBonus)
        {
            $userPointInfoPromo1 = $this->getUserBalanceFromUserPointsByCoinType($coin_type_bonus_id, $userID);

            DB::select(
                'update
                    user_points
                SET
                    `VALUE` = ?,
                    `USER_PROMO_BALANCE` = `USER_PROMO_BALANCE` + ?,
                    `USER_TOT_BALANCE` = `USER_WIN_BALANCE` + `USER_PROMO_BALANCE` + `USER_DEPOSIT_BALANCE`,
                    `UPDATED_DATE` = ?
                WHERE
                    `USER_ID` = ? AND
                    `COIN_TYPE_ID` = ?
                ',
                [
                    $promoCodeValue,
                    $promoCodeValue,
                    date('Y-m-d H:i:s'),
                    $userID,
                    $coin_type_bonus_id
                ]
            );
    
            MasterTransactionHistoryFppBonus::create([
                'USER_ID' => $userID,
                'BALANCE_TYPE_ID' => $transactionVariablesArr['promoBalanceTypeId'],
                'TRANSACTION_STATUS_ID' => $transactionVariablesArr['promoOkSuccessPaymentStatus'],
                'TRANSACTION_TYPE_ID' => $transactionVariablesArr['promoTransactionTypeId'],
                'TRANSACTION_AMOUNT' => $promoCodeValue,
                'TRANSACTION_DATE' => $redeem_date,
                'INTERNAL_REFERENCE_NO' => $transaction->INTERNAL_REFERENCE_NO,
                'CURRENT_TOT_BALANCE' => $userPointInfoPromo1->USER_PROMO_BALANCE,
                'CLOSING_TOT_BALANCE' => $userPointInfoPromo1->USER_PROMO_BALANCE + $promoCodeValue,
                'PARTNER_ID' => $user->PARTNER_ID,
                'PROMO_CAMPAIGN_ID' => $promo_campaign_id,
            ]);

            $data = [
                'userId' => $user->USER_ID,
                "PromoValues" => $promoCampaign,
                "PromoCode" => $transaction->PROMO_CODE,
                "promoAmountProvided" => $promoCodeValue,
                "BONUS_RELEASE_PER" => $bonus_release_per,
                "coin_type_id" => $coin_type_bonus_id
            ];
    
            $this->userActivitiesTracking($user->USER_ID, "Deposite Promo Provided - " . $transaction->INTERNAL_REFERENCE_NO, $data);
        }

        $campaiganTouser = CampaignToUser::create([
            'PROMO_CAMPAIGN_ID' => $promo_campaign_id,
            'USER_ID' => $userID,
            'BONUSE_TYPE' => $bonus_type,
            'BONUS_AMOUNT' => $promoCodeValue,
            'BALANCE_BONUS_AMOUNT' => $promoCodeValue,
            'REDEEM_DATE' => $redeem_date,
            'EXPIRY_DATE' => $expiry_date,
            'BONUS_STATUS' => $bStatus,
            'STATUS' => $status,
            'CAMPAIGN_CODE' => $campaignCodeBonus,
            'CAMPAIGN_NAME' => $campaignNameBonus,
            'COIN_TYPE_ID' => $coin_type_bonus_id,
            'BONUS_RELEASE_PER' => $bonus_release_per
        ]);

        \DB::table('special_bonus_meta_data')->insert([
            'CAMPAIGN_TO_USER_ID' => $campaiganTouser->CAMPAIGN_TO_USER_ID,
            'SOURCE' => 'Deposit Code',
            'META_DATA' => json_encode([
                'promo_code' => $promoCampaign->PROMO_CAMPAIGN_CODE
            ])
        ]);

        if ((!$processBonus) && ($promoCodeValue > 0)) 
        {
            $otherDetails['PROMO_CAMPAIGN_CODE'] = $promoCampaign->PROMO_CAMPAIGN_CODE;
            $otherDetails['PROMO_CAMPAIGN_ID'] = $promoCampaign->PROMO_CAMPAIGN_ID;
            $otherDetails['PROMO_CAMPAIGN_TYPE_ID'] = $promoCampaign->PROMO_CAMPAIGN_TYPE_ID;
            $otherDetails['BENIFITS']['RCB']['COINS_REQUIRED'] = $promoCampaign->COINS_REQUIRED;
            $otherDetails['BENIFITS']['RCB']['MONEY_OPTION'] = $promoCampaign->MONEY_OPTION;
            $otherDetails['BONUS_TYPE'] = 'LB';// Bonus Type to be released

            $this->insertIntoBonusTransactionHistory(
                $transaction->USER_ID, 
                $transactionVariablesArr['promoBalanceTypeId'], 
                $transactionVariablesArr['promoPendingStatusId'], 
                $transactionVariablesArr['promoTransactionTypeId'], 
                $promoCodeValue, 
                $transaction->INTERNAL_REFERENCE_NO, 
                $user->PARTNER_ID, 
                $otherDetails
            );
        }
    }

    private function payment_claimed_bonus($promoCampaign, object $user, object $transaction, $promoCodeValue, $processBonus)
    {
        $transactionVariablesArr = config('rummy_config.transactionVariables');

        $expiry_date_db = $promoCampaign->BONUS_EXPIRY_INTERVAL_DAYS;
        $redeem_dates = date("Y-m-d H:i:s");
        $expiry_date = date('Y-m-d H:i:s', strtotime("+" . $expiry_date_db . " days", strtotime($redeem_dates)));

        $userID = $user->USER_ID;
        $campaignCodeBonus = $transaction->PROMO_CODE;
        $campaignNameBonus = $promoCampaign->PROMO_CAMPAIGN_NAME;
        $promo_campaign_id = $promoCampaign->PROMO_CAMPAIGN_ID;
        $end_date_time = $promoCampaign->END_DATE_TIME;
        $bonus_release_per = $promoCampaign->BONUS_RELEASE_PER;
        $internalRefNop = "123" . $userID . date('dmyhis');
        $bonus_type = "Special";
        $redeem_date = Carbon::now();

        $coin_type_bonus_id = 1;
        $status = 3;
        $bStatus = 3;

        if($processBonus)
        {
            $userPointInfoPromo1 = $this->getUserBalanceFromUserPointsByCoinType($coin_type_bonus_id, $userID);

            DB::select(
                'update
                    user_points
                SET
                    `VALUE` = ?,
                    `USER_PROMO_BALANCE` = `USER_PROMO_BALANCE` + ?,
                    `USER_TOT_BALANCE` = `USER_WIN_BALANCE` + `USER_PROMO_BALANCE` + `USER_DEPOSIT_BALANCE`,
                    `UPDATED_DATE` = ?
                WHERE
                    `USER_ID` = ? AND
                    `COIN_TYPE_ID` = ?
                ',
                [
                    $promoCodeValue,
                    $promoCodeValue,
                    date('Y-m-d H:i:s'),
                    $userID,
                    $coin_type_bonus_id
                ]
            );
    
            MasterTransactionHistory::create([
                'USER_ID' => $userID,
                'BALANCE_TYPE_ID' => $transactionVariablesArr['promoBalanceTypeId'],
                'TRANSACTION_STATUS_ID' => $transactionVariablesArr['promoOkSuccessPaymentStatus'],
                'TRANSACTION_TYPE_ID' => $transactionVariablesArr['promoTransactionTypeId'],
                'TRANSACTION_AMOUNT' => $promoCodeValue,
                'TRANSACTION_DATE' => $redeem_date,
                'INTERNAL_REFERENCE_NO' => $transaction->INTERNAL_REFERENCE_NO,
                'CURRENT_TOT_BALANCE' => $userPointInfoPromo1->USER_PROMO_BALANCE,
                'CLOSING_TOT_BALANCE' => $userPointInfoPromo1->USER_PROMO_BALANCE + $promoCodeValue,
                'PARTNER_ID' => $user->PARTNER_ID,
                'PROMO_CAMPAIGN_ID' => $promo_campaign_id,
            ]);

            $data = [
                'userId' => $user->USER_ID,
                "PromoValues" => $promoCampaign,
                "PromoCode" => $transaction->PROMO_CODE,
                "promoAmountProvided" => $promoCodeValue,
                "coin_type_id" => $coin_type_bonus_id
            ];
    
            $this->userActivitiesTracking($user->USER_ID, "Deposite Promo Provided - " . $transaction->INTERNAL_REFERENCE_NO, $data);
        }
    
        CampaignToUser::create([
            'PROMO_CAMPAIGN_ID' => $promo_campaign_id,
            'USER_ID' => $userID,
            'BONUSE_TYPE' => $bonus_type,
            'BONUS_AMOUNT' => $promoCodeValue,
            'BALANCE_BONUS_AMOUNT' => $promoCodeValue,
            'REDEEM_DATE' => $redeem_date,
            'EXPIRY_DATE' => $expiry_date,
            'BONUS_STATUS' => $bStatus,
            'STATUS' => $status,
            'CAMPAIGN_CODE' => $campaignCodeBonus,
            'CAMPAIGN_NAME' => $campaignNameBonus,
            'COIN_TYPE_ID' => $coin_type_bonus_id
        ]);

        if ((!$processBonus) && ($promoCodeValue > 0)) 
        {
            $otherDetails['PROMO_CAMPAIGN_CODE'] = $promoCampaign->PROMO_CAMPAIGN_CODE;
            $otherDetails['PROMO_CAMPAIGN_ID'] = $promoCampaign->PROMO_CAMPAIGN_ID;
            $otherDetails['PROMO_CAMPAIGN_TYPE_ID'] = $promoCampaign->PROMO_CAMPAIGN_TYPE_ID;
            $otherDetails['BENIFITS']['RCB']['COINS_REQUIRED'] = $promoCampaign->COINS_REQUIRED;
            $otherDetails['BENIFITS']['RCB']['MONEY_OPTION'] = $promoCampaign->MONEY_OPTION;
            $otherDetails['BONUS_TYPE'] = 'RCB';// Bonus Type to be relased 

            $this->insertIntoBonusTransactionHistory(
                $transaction->USER_ID, 
                $transactionVariablesArr['promoBalanceTypeId'], 
                $transactionVariablesArr['promoPendingStatusId'], 
                $transactionVariablesArr['promoTransactionTypeId'], 
                $promoCodeValue, 
                $transaction->INTERNAL_REFERENCE_NO, 
                $user->PARTNER_ID, 
                $otherDetails
            );
        }
    }
    /* FUNCTIONS USED FOR WEB HOOKS :: END */

    protected function fetchLastestTransactions($userID)
    {
        $suggestions = MasterTransactionHistory::select('transaction_amount')->where('USER_ID', $userID)->whereIn('TRANSACTION_STATUS_ID', [103, 107, 125, 202])->whereIn('TRANSACTION_TYPE_ID', [8, 61, 62, 83])->orderBy('MASTER_TRANSACTTION_ID', 'desc')->limit(10)->get()->pluck('transaction_amount')->toArray();

        $firstSuggestion = 0;
        $secondSuggestion = 0;
        $thirdSuggestion = 0;
        $fourthSuggestion = 0;
        $fifthSuggestion = 0;
        $sixthSuggestion = 0;

        if (count($suggestions) > 0) {
            /* If user have more then 1 payment 
                then collect the suggestions and find the average */
            if(count($suggestions) > 1) {
                $avg = collect($suggestions)->avg();
                $firstSuggestion = ceil($avg / 50) * 50;
            } else {
                /* IF only one payment then make a direct logic */
                $firstSuggestion = ceil($suggestions[0] / 50) * 50;
            }
                $secondSuggestion = 2 * $firstSuggestion;
                $thirdSuggestion = 4 * $firstSuggestion;
                $fourthSuggestion = 10 * $firstSuggestion;
                $fifthSuggestion = 20 * $firstSuggestion;
                $sixthSuggestion = 40 * $firstSuggestion;
           
        } else {
            // Get values from env
            $getSuggestions = explode(',', env('PAYMENT_SUGGESTIONS'));

            if(array_key_exists(0, $getSuggestions)){
                $firstSuggestion = $getSuggestions[0];
            }
            if(array_key_exists(1, $getSuggestions)){
                $secondSuggestion = $getSuggestions[1];
            }
            if(array_key_exists(2, $getSuggestions)){
                $thirdSuggestion = $getSuggestions[2];
            }
            if(array_key_exists(3, $getSuggestions)){
                $fourthSuggestion = $getSuggestions[3];
            }
            if(array_key_exists(4, $getSuggestions)){
                $fifthSuggestion = $getSuggestions[4];
            }
            if(array_key_exists(5, $getSuggestions)){
                $sixthSuggestion = $getSuggestions[5];
            }
        }

        $data['suggestions'] = [];
        array_push($data['suggestions'], ['amount' => $firstSuggestion, 'best' => 0]);
        array_push($data['suggestions'], ['amount' => $secondSuggestion, 'best' => 0]);
        array_push($data['suggestions'], ['amount' => $thirdSuggestion, 'best' => count($suggestions) > 0 ? 0 : 1]);
        array_push($data['suggestions'], ['amount' => $fourthSuggestion, 'best' => count($suggestions) > 0 ? 1 : 0]);
        array_push($data['suggestions'], ['amount' => $fifthSuggestion, 'best' => 0]);
        array_push($data['suggestions'], ['amount' => $sixthSuggestion, 'best' => 0]);
        return $data;
    }


    protected function insertIntoBonusTransactionHistory($userID, $balanceTypeID, $transactionStatusID, $transactionTypeID, $transactionAmount, $internalReferenceNo, $partnerID, $otherDetails)
    {
        $bonusTransactionRecord = new BonusTransactionHistory();
        $bonusTransactionRecord->USER_ID = $userID;
        $bonusTransactionRecord->BALANCE_TYPE_ID = $balanceTypeID;
        $bonusTransactionRecord->TRANSACTION_STATUS_ID = $transactionStatusID;
        $bonusTransactionRecord->TRANSACTION_TYPE_ID = $transactionTypeID;
        $bonusTransactionRecord->TRANSACTION_AMOUNT = $transactionAmount;
        $bonusTransactionRecord->TRANSACTION_DATE =  date('Y-m-d H:i:s');
        $bonusTransactionRecord->INTERNAL_REFERENCE_NO = $internalReferenceNo;
        $bonusTransactionRecord->PARTNER_ID = $partnerID;
        $bonusTransactionRecord->OTHER_DETAILS = $otherDetails;
        $bonusTransactionRecord->save();
    }

    /**
     * function to get promo value & type in case of pending or failed transaction
     * 
     * @param $promoCampaign
     * @param $transactionAmount
     * @return array
     */
    protected function getPromoValueAndTypeByPromoCampaignDetail($promoCampaign, $transactionAmount, $userId)
    {
        try {
            $bonusResponse = [];
            $user = $this->getUserDetails($userId);
            $levelConfigId = $user->LEVEL_CONFIG_ID;
            if (in_array($promoCampaign->PROMO_CAMPAIGN_TYPE_ID, [1])) {
                if ($promoCampaign->TIER_TYPE == 1) {
                    $tier_bonus = json_decode($promoCampaign->TIER_BONUS_DATA);
                    $tier_bonus_collect = collect($tier_bonus)->where("level", $levelConfigId)->toArray();
                    if (!empty($tier_bonus_collect)) {
                        $tier_bonus_collect = $tier_bonus_collect[$levelConfigId - 1];
                        $bonus_percentage = $tier_bonus_collect->bonus_percentage;

                        $promoCodeValue = ($transactionAmount / 100) * $bonus_percentage;
                        $bonus_cap_value = $tier_bonus_collect->bonus_cap_value;

                        $bonusResponse['value'] = ($promoCodeValue > $bonus_cap_value) ? $bonus_cap_value : $promoCodeValue;
                        $bonusResponse['type'] = $promoCampaign->P_UNCLAIMED_BONUS == 1 ? "Special Bonus" : ($promoCampaign->P_PROMO_CHIPS == 1 ? "Real Cash Bonus" : "");

                        $bonusResponse['total_bonus'] = $bonusResponse['value'];
                    }
                } else {
                    if ($promoCampaign->P_PROMO_CHIPS == 1) {
                        $PPromPercentageVal = floor(($transactionAmount * $promoCampaign->P_PROMO_VALUE) / 100);
                        $PPromoCap = $promoCampaign->P_PROMO_CAP_VALUE;
                        $bonusResponse['value'] = $PPromPercentageVal > $PPromoCap ? $PPromoCap : $PPromPercentageVal;
                        $bonusResponse['type'] = "Real Cash Bonus";

                        $bonusResponse['total_bonus'] = $bonusResponse['value'];
                    } elseif ($promoCampaign->P_UNCLAIMED_BONUS == 1) {
                        $UBonusPercentageVal = floor(($transactionAmount * $promoCampaign->P_PROMO_VALUE) / 100);
                        $UBonusCap = $promoCampaign->P_PROMO_CAP_VALUE;
                        $bonusResponse['value'] = ($UBonusCap && ($UBonusPercentageVal > $UBonusCap) ) ? $UBonusCap : $UBonusPercentageVal;
                        $bonusResponse['type'] = "Special Bonus";

                        $bonusResponse['total_bonus'] = $bonusResponse['value'];
                    } elseif ($promoCampaign->P_COMBO_REWARD == 1) {
                        $PPromPercentageVal = floor(($transactionAmount * $promoCampaign->P_PROMO_VALUE) / 100);
                        $PPromoCap = $promoCampaign->P_PROMO_CAP_VALUE;
                        $bonusResponse['value'] = ($PPromPercentageVal > $PPromoCap ? $PPromoCap : $PPromPercentageVal);
                        $bonusResponse['total_bonus'] = $bonusResponse['value'];
                        $promoBalance = $bonusResponse['value'];

                        $sbValueAndCap = json_decode($promoCampaign->P_REWARD_DATA);
                        $UBonusPercentageVal = !empty($sbValueAndCap) ? floor(($transactionAmount * $sbValueAndCap->P_PROMO_VALUE) / 100) : 0;
                        $UBonusCap = !empty($sbValueAndCap) ? $sbValueAndCap->P_PROMO_CAP_VALUE : 0;
                        $uncliamedBonusAmount = (($UBonusCap && ($UBonusPercentageVal > $UBonusCap) ) ? $UBonusCap : $UBonusPercentageVal);
                        $bonusResponse['value'] = ($bonusResponse['value'] + $uncliamedBonusAmount) . " (₹" . $uncliamedBonusAmount . " SB + ₹" . $bonusResponse['value'] . " RCB)";
                        $bonusResponse['type'] = "SB, RCB";
                        $bonusResponse['total_bonus'] += $uncliamedBonusAmount;
                        $bonusResponse['sb'] = $uncliamedBonusAmount;
                        $bonusResponse['rcb'] = $promoBalance;
                    }
                }
            }

            return $bonusResponse;
        } catch(\Exception $e) {
            Log::error($e);
            return [];
        }
    }

    /**
     * function to fetch last two payment methods used in deposit
     * <AUTHOR> Kumar<<EMAIL>>
     * @param int $userId
     * @return array
     */
    protected function getLastUsedPaymentMethods($userId): array
    {
        try {
            return PaymentTransaction::where('USER_ID', $userId)
            ->where('PAYMENT_TRANSACTION_STATUS', 125)
            ->limit(2)
            ->orderByDesc('PAYMENT_TRANSACTION_ID')
            ->pluck('PG_REFERENCE_NOS')
            ->toArray();
        } catch(\Exception $e) {
            Log::error($e);
            return [];
        }
    }

    //Payment Gateway fee calculation
    
    public function paymentGatewayFeeCalculator($amount, $paymentMode, $userDetails){

        $userSpecificFeeSetting = UserwisePaymentGatewayfeeSetting::select('USER_FEE_BREAKUP')->where('USER_ID', $userDetails->USER_ID)->get();
        $paymentMode = strtoupper($paymentMode);
        if(count($userSpecificFeeSetting)){
            $feeST = json_decode($userSpecificFeeSetting[0]->USER_FEE_BREAKUP);
            $finalFee = $feeST->$paymentMode;

        }else{
            $userType = $this->checkUserOldNew($userDetails->USER_ID);
            $feeST = PaymentGatewayFeeSetting::select('PAYMENT_MODE', 'FEE_RATE', 'DISC_FEE_RATE', 'MAX_CAP')->where(['USER_TYPE' => $userType, 'PAYMENT_MODE' => $paymentMode])->first();
            $finalFee = $feeST;
        }

        //calculateFee and fee GST @18 %
        $gstFeeRate = config('rummy_config.transactionVariables.gstRateOnGatewayFee');

        $calculatedFee = [
                'fee_rate' =>  $finalFee->FEE_RATE,
                'disc_fee_rate' =>  $finalFee->DISC_FEE_RATE,
                'fee_gst_rate' =>  $gstFeeRate,
                'deposit_fee' => $depFee = round($amount/100*$finalFee->FEE_RATE,2),
                'disc_deposit_fee' => $discDepFee = round($amount/100*$finalFee->DISC_FEE_RATE,2) <= (int) $finalFee->MAX_CAP ? round($amount/100*$finalFee->DISC_FEE_RATE,2) : (int) $finalFee->MAX_CAP,
                'gst_on_deposit_fee' => $gstOnFee = round($depFee/(100+$gstFeeRate)*$gstFeeRate, 2),
                'gst_on_disc_deposit_fee' => $discGstOnFee = round($discDepFee/(100+$gstFeeRate)*$gstFeeRate, 2),
                'total_amount_with_fee_gst' => round($depFee+$amount,2),
                'total_amount_with_disc_fee_gst' => round($discDepFee+$amount,2),
                'total_amount_fee_without_gst' => round($depFee+$amount,2),
                'total_amount_disc_fee_without_gst' => round($discDepFee+$amount,2),
                'max_cap' => (int) $finalFee->MAX_CAP
            ];

        return $calculatedFee;
    }

    public function getPaymentGatewayFeeStructure($userId, $amount, $gstComp, $includedBonus, $totalWithIncludedBonus){

        $gstFeeRate = config('rummy_config.transactionVariables.gstRateOnGatewayFee');
        $userSpecificFeeSetting = UserwisePaymentGatewayfeeSetting::select('USER_FEE_BREAKUP')->where('USER_ID', $userId)->get();

        $feeSt['requested_amount'] = (int) $amount;
        $feeSt['fee_gst_rate'] = (int) $gstFeeRate;
        if(count($userSpecificFeeSetting)){
            foreach(json_decode($userSpecificFeeSetting[0]->USER_FEE_BREAKUP) as $key => $feeModeWise){

                $feeSt[strtolower($key)] =  [
                    'fee_rate' => $feeModeWise->FEE_RATE,
                    'disc_fee_rate' => $feeModeWise->DISC_FEE_RATE,
                    'is_fee_waived_off' => $isFeeWaivedOFF = $feeModeWise->DISC_FEE_RATE == 0 && $feeModeWise->FEE_RATE > 0 ? true : false,
                    'is_fee_discounted' => $isFeeDiscounted = $feeModeWise->DISC_FEE_RATE != 0 && $feeModeWise->DISC_FEE_RATE < $feeModeWise->FEE_RATE ? true : false,
                    'deposit_fee' => $depFee = round($amount/100*$feeModeWise->FEE_RATE,2),
                    'disc_deposit_fee' => $discDepFee = round($amount/100*$feeModeWise->DISC_FEE_RATE,2) <= (int) $feeModeWise->MAX_CAP ? round($amount/100*$feeModeWise->DISC_FEE_RATE,2) : (int) $feeModeWise->MAX_CAP,
                    'gst_on_deposit_fee' => $gstOnFee = round($depFee/(100+$gstFeeRate)*$gstFeeRate,2),
                    'gst_on_disc_deposit_fee' => $discGstOnFee = round($discDepFee/(100+$gstFeeRate)*$gstFeeRate,2),
                    'total_amount_with_fee_gst' => round($depFee+$amount,2),
                    'total_amount_with_disc_fee_gst' => round($discDepFee+$amount,2),
                    'total_amount_fee_without_gst' => round($depFee+$amount,2),
                    'total_amount_disc_fee_without_gst' => round($discDepFee+$amount,2),
                    'max_cap' => (int) $feeModeWise->MAX_CAP,
                    'deposit_confirmation' => [
                        [
                          'key' => 'Deposit requested (incl. GST)',
                          'value' => $feeSt['requested_amount'],
                          'keyType' => 'deposit',
                          'subKeyType' => '',
                        ],
                        [
                          'key' => 'GST applied on deposit amount @'.$gstComp['rate'].'%',
                          'value' => $gstComp['amount'],
                          'keyType' => 'gst',
                          'subKeyType' => '',
                        ],
                        [
                          'key' => 'GST bonus',
                          'value' => $gstComp['amount_to_be_refunded'],
                          'subKey' => "To be credited as ".($gstComp['refund_balance_type'] == 1 ? "deposit amount" : ($gstComp['refund_balance_type'] == 2 ? "real cash bonus" : "win amount"))." to your CB Wallet",
                          'keyType' => 'gstBonus',
                          'subKeyType' => '',
                        ],
                        [
                          'key' => 'Processing fee applied on deposit @'.($isFeeWaivedOFF ? $feeModeWise->FEE_RATE : $feeModeWise->DISC_FEE_RATE).'%',
                          'value' => $isFeeDiscounted ? $depFee : ($isFeeWaivedOFF ? $depFee : $discDepFee),
                          'subValue' => $discDepFee,
                          'subKey' => 'See how this is calculated ?',
                          'keyType' => 'processingFee',
                          'subKeyType' => 'processingFee',
                          'isFeeWaivedOff' => $isFeeWaivedOFF,
                          'isFeeDiscounted' => $isFeeDiscounted,
                          'tooltipDetails' => [
                            [
                              'key' => 'Processing fee :',
                              'value' => $isFeeWaivedOFF ? ($depFee - $gstOnFee) : ($discDepFee-$discGstOnFee),
                              'subKey' => 'Mode : '.$key,
                            ],
                            [
                              'key' => 'GST on processing fee @'.$gstFeeRate.'%',
                              'value' => $isFeeWaivedOFF ? $gstOnFee : $discGstOnFee,
                            ],
                            [
                              'key' => 'Processing fee on deposit :',
                              'value' => $isFeeWaivedOFF ? round($depFee,2) : round($discDepFee,2),
                            ],
                          ],
                        ],
                        [
                          'key' => 'Amount to be credited to your wallet',
                          'value' =>  $totalWithIncludedBonus,
                          'subKey' => '(Includes bonus)',
                          'keyType' => 'amount',
                          'subKeyType' => 'wallet',
                          'tooltipDetails' => $includedBonus,
                        ],
                        [
                          'key' => 'Net payable',
                          'value' => round($discDepFee+$amount,2),
                          'keyType' => 'payable',
                          'subKeyType' => 'payable',
                        ],
                    ]
                ];
            }
 
        }else{

            $userType = $this->checkUserOldNew($userId);
            $feeComponent = PaymentGatewayFeeSetting::select('PAYMENT_MODE', 'FEE_RATE', 'DISC_FEE_RATE', 'MAX_CAP')->where('USER_TYPE', $userType)->get();
    
            foreach($feeComponent as $feeModeWise){
                $feeSt[strtolower($feeModeWise->PAYMENT_MODE)] =  [
                    'fee_rate' => $feeModeWise->FEE_RATE,
                    'disc_fee_rate' => $feeModeWise->DISC_FEE_RATE,
                    'is_fee_waived_off' => $isFeeWaivedOFF = $feeModeWise->DISC_FEE_RATE == 0 && $feeModeWise->FEE_RATE > 0 ? true : false,
                    'is_fee_discounted' => $isFeeDiscounted = $feeModeWise->DISC_FEE_RATE != 0 && $feeModeWise->DISC_FEE_RATE < $feeModeWise->FEE_RATE ? true : false,
                    'deposit_fee' => $depFee = round($amount/100*$feeModeWise->FEE_RATE,2),
                    'disc_deposit_fee' => $discDepFee = round($amount/100*$feeModeWise->DISC_FEE_RATE,2) <= (int) $feeModeWise->MAX_CAP ? round($amount/100*$feeModeWise->DISC_FEE_RATE,2) : (int) $feeModeWise->MAX_CAP,
                    'gst_on_deposit_fee' => $gstOnFee = round($depFee/(100+$gstFeeRate)*$gstFeeRate, 2),
                    'gst_on_disc_deposit_fee' => $discGstOnFee = round($discDepFee/(100+$gstFeeRate)*$gstFeeRate,2),
                    'total_amount_with_fee_gst' => round($depFee+$amount,2),
                    'total_amount_with_disc_fee_gst' => round($discDepFee+$amount,2),
                    'total_amount_fee_without_gst' => round($depFee+$amount,2),
                    'total_amount_disc_fee_without_gst' => round($discDepFee+$amount,2),
                    'max_cap' => (int) $feeModeWise->MAX_CAP,
                    'deposit_confirmation' => [
                        [
                          'key' => 'Deposit requested (incl. GST)',
                          'value' => $feeSt['requested_amount'],
                          'keyType' => 'deposit',
                          'subKeyType' => '',
                        ],
                        [
                          'key' => 'GST applied on deposit amount @'.$gstComp['rate'].'%',
                          'value' => $gstComp['amount'],
                          'keyType' => 'gst',
                          'subKeyType' => '',
                        ],
                        [
                          'key' => 'GST bonus',
                          'value' => $gstComp['amount_to_be_refunded'],
                          'subKey' => "To be credited as ".($gstComp['refund_balance_type'] == 1 ? "deposit amount" : ($gstComp['refund_balance_type'] == 2 ? "real cash bonus" : "win amount"))." to your CB Wallet",
                          'keyType' => 'gstBonus',
                          'subKeyType' => '',
                        ],
                        [
                            'key' => 'Processing fee applied on deposit @'.($isFeeWaivedOFF ? $feeModeWise->FEE_RATE : $feeModeWise->DISC_FEE_RATE).'%',
                            'value' => $isFeeDiscounted ? $depFee : ($isFeeWaivedOFF ? $depFee : $discDepFee),
                            'subValue' => $discDepFee,
                            'subKey' => 'See how this is calculated ?',
                            'keyType' => 'processingFee',
                            'subKeyType' => 'processingFee',
                            'isFeeWaivedOff' => $isFeeWaivedOFF,
                            'isFeeDiscounted' => $isFeeDiscounted,
                            'tooltipDetails' => [
                              [
                                'key' => 'Processing fee :',
                                'value' => $isFeeWaivedOFF ? ($depFee - $gstOnFee) : ($discDepFee-$discGstOnFee),
                                'subKey' => 'Mode : '.$feeModeWise->PAYMENT_MODE,
                              ],
                              [
                                'key' => 'GST on processing fee @'.$gstFeeRate.'%',
                                'value' => $isFeeWaivedOFF ? $gstOnFee : $discGstOnFee,
                              ],
                              [
                                'key' => 'Processing fee on deposit :',
                                'value' => $isFeeWaivedOFF ? round($depFee,2) : round($discDepFee,2),
                              ],
                            ],
                        ],
                        [
                          'key' => 'Amount to be credited to your wallet',
                          'value' => $totalWithIncludedBonus,
                          'subKey' => '(Includes bonus)',
                          'keyType' => 'amount',
                          'subKeyType' => 'wallet',
                          'tooltipDetails' => $includedBonus,
                        ],
                        [
                          'key' => 'Net payable',
                          'value' => round($discDepFee+$amount,2),
                          'keyType' => 'payable',
                          'subKeyType' => 'payable',
                        ],
                    ]
                ];
            }

        }

        return $feeSt;
    }

    public function checkUserOldNew($userId){

        $transactionCount = PaymentTransaction::where(['USER_ID' => $userId])->whereIn('PAYMENT_TRANSACTION_STATUS', [103, 125, 202])->whereIn('TRANSACTION_TYPE_ID', [8])->count();

        if($transactionCount == 0){
            return "NEW_USER";
        }else{
            return "OLD_USER";
        }

    }

    public function checkCardFromVendorJuspay($cardsixdigits, $userID)
    {
        $url = config('jusPay.jusPay_url') . "cardbins/$cardsixdigits?merchant_id=" . config('jusPay.jusPay_merchant_id');
        
        $data = $this->ApiCallCurl([
            "url" => $url,
            "form_params" => [],
            "headers" => [
                "Content-Type" => "application/x-www-form-urlencoded",
                "x-merchantid" => config('jusPay.jusPay_merchant_id')
            ],
            "extra_curl_options_array" => [
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_USERPWD => config('jusPay.jusPay_api_key'),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_CUSTOMREQUEST => "POST"
            ]
        ]);
        $this->userActivitiesTracking($userID, "VALIDATE_CARD_DETAILS_FROM_JUSPAY_RESPONSE", $data);
       
        return $data;

    }

    public function calculateGstOnDepositAmount($amount){
        $gstFeeRate = config('rummy_config.transactionVariables.gstRateOnDeposit');
        $gstAmount  = $amount/(100 + $gstFeeRate) * $gstFeeRate;
        return round($gstAmount, 2);
    }

    public function getUserSettings($userId){
        return \DB::table('user_settings')->where('USER_ID', $userId)->first();
    }

    public function checkGstFlagEnabled($userId){

        $testUsersList = $this->getTestingUserList();
        $depositTestUsers = explode("|", $testUsersList);
        if(!empty($depositTestUsers) && in_array($userId, $depositTestUsers)){
            return true;
        }else{
            return GlobalConfig::select('CONFIG_VALUE')
                ->where('CONFIG_KEY', 'deposit_gst_flag')
                ->pluck('CONFIG_VALUE')
                ->first() == 'true';
        }
    } 

    public function getGstBonusBalanceType(){
        return GlobalConfig::select('CONFIG_VALUE')
            ->where('CONFIG_KEY', 'gst_refund_balance_type')
            ->pluck('CONFIG_VALUE')
            ->first();
    } 

    public function getStateCode($state){

        $stateData = State::select('STATE_CODE_TIN','display_status')->where('StateName', 'like', '%' .$state. '%')->orWhere('possible_names', 'like', '%' .$state. '%')->get()->first();
        
        if(!empty($stateData)){
            return $stateData;
        }else{
            return (object)["STATE_CODE_TIN"=> "07", "display_status"=> 2];
        }
    }

    public function getTestingUserList(){
        return GlobalConfig::select('CONFIG_VALUE')
            ->where('CONFIG_KEY', 'testing_users')
            ->pluck('CONFIG_VALUE')
            ->first();
    }

    public function generateDepositInvoiceNo(String $date = null) : String {
        $date = date('Y-m-d', strtotime($date)) ?? date('Y-m-d');
        
        $latestRow = DepositGstInvoice::whereDate('CREATED_DATE', $date)->latest('ID')->first();
        
        $invoicePrefix = 'CB' . date('ymd', strtotime($date));
        
        if(!empty($latestRow)){
            $invoiceCount = (Int) substr($latestRow->INVOICE_NO, -7, 15);
            $invoiceCount++;
            $invoiceSeries = str_pad($invoiceCount, strlen($invoiceCount) + (7 - strlen($invoiceCount)), '0', STR_PAD_LEFT);
            $invoiceNo = $invoicePrefix . $invoiceSeries;
        }else{
            $invoiceNo = $invoicePrefix . '0000001';
        }
        return $invoiceNo;
    }

    public function createRuntimeDepositCode($request)
    {
        try {
            $codeConfig = \DB::table('deposit_amount_suggestion_configuration')->where('ID', $request->offer_id)->first();
            $rewardConfig = json_decode($codeConfig->REWARD_CONFIGURATION);
            $intRandom = rand(100000, 999999);
			$gcampaignCODE = "PAY_" . $intRandom;
            $promoCampaignArr = [
                "PARTNER_ID" => "10001",
                "PROMO_CAMPAIGN_NAME" => $gcampaignCODE,
                "PROMO_CAMPAIGN_CODE" => $gcampaignCODE,
                "PROMO_CAMPAIGN_DESC" => $gcampaignCODE,
                "PROMO_STATUS_ID" => "1",
                "START_DATE_TIME" => Carbon::now(),
                "END_DATE_TIME" => Carbon::now()->addDay(),
                "EXP_USER_MIN" => "1",
                "EXP_USER_MAX" => "1",
                "STATUS" => "1",
                "PROMO_CAMPAIGN_TYPE_ID" => "1",
                "DISPLAY_STATUS" => 1,
                "BONUS_RELEASE_PER" => isset($rewardConfig->SB) && isset($rewardConfig->SB->conversion) ? $rewardConfig->SB->conversion : null,
                "BONUS_EXPIRY_INTERVAL_DAYS" => isset($rewardConfig->SB) && isset($rewardConfig->SB->validity) ? $rewardConfig->SB->validity : null,
                "TIER_TYPE" => "2",
                "FIRST_DEPOSIT" => $codeConfig->USER_TYPE == 1 ? 1 : 0,
                "SECOND_DEPOSIT" => $codeConfig->USER_TYPE == 2 ? 1 : 0,
                "MINIMUM_DEPOSIT_AMOUNT" => $request->amount,
                "MAXIMUM_DEPOSIT_AMOUNT" => $request->amount,
                "COINS_REQUIRED" => null,
            ];
            $promoCampaign = PromoCampaign::create($promoCampaignArr);
            $promoRuleArr = [
                "PROMO_CAMPAIGN_ID" => $promoCampaign->PROMO_CAMPAIGN_ID,
                "PROMO_PAYMENT_TYPE_ID" => 0,
                "PROMO_TYPE_ID" => 1,
                "PROMO_MIN_VALUE" => 0,
                "PROMO_MAX_VALUE" => 0,
                "PROMO_VALUE1" => 0,
                "PROMO_VALUE2" => 0,
                "P_PROMO_CHIPS" => !isset($rewardConfig->SB) && isset($rewardConfig->RCB) ? 1 : 0,
                "P_PROMO_VALUE" => isset($rewardConfig->RCB) && isset($rewardConfig->RCB->value) ? $rewardConfig->RCB->value : (isset($rewardConfig->SB) && isset($rewardConfig->SB->value) ? $rewardConfig->SB->value : null),
                "P_PROMO_CAP_VALUE" => isset($rewardConfig->RCB) && isset($rewardConfig->RCB->cap) ? $rewardConfig->RCB->cap : (isset($rewardConfig->SB) && isset($rewardConfig->SB->cap) ? $rewardConfig->SB->cap : null),
                "P_UNCLAIMED_BONUS" => isset($rewardConfig->SB) && !isset($rewardConfig->RCB) ? 1 : 0,
                "P_COMBO_REWARD" => isset($rewardConfig->RCB) && isset($rewardConfig->SB) ? 1 : 0,
                "P_REWARD_DATA" => isset($rewardConfig->RCB) && isset($rewardConfig->SB) ? json_encode(["P_PROMO_VALUE" => $rewardConfig->SB->value, "P_PROMO_CAP_VALUE" => $rewardConfig->SB->cap]) : json_encode([]),
                "P_CREDIT_DEBITCARD_VALUE" => 0,
                "P_NETBANKING_VALUE" => 0,
                "P_BONUS_CAP_VALUE" => 0,
                "P_TOURNAMENT_TICKETS" => 0,
                "S_PROMO_CHIPS" => 0,
                "S_PROMO_CHIP_VALUE" => null,
                "S_TOURNAMENT_TICKETS" => 0,
                "S_TOURNAMENT_TICKET_ID" => null,
                "RAF_PERCENTAGE" => null,
                "KYC_CHECK" => 0,
                "TIER_BONUS_DATA" => json_encode([
                    0 => [
                      'level' => 1,
                      'bonus_percentage' => NULL,
                      'bonus_cap_value' => NULL,
                      'status' => 1,
                    ],
                    1 => [
                      'level' => 2,
                      'bonus_percentage' => NULL,
                      'bonus_cap_value' => NULL,
                      'status' => 1,
                    ],
                    2 => [
                      'level' => 3,
                      'bonus_percentage' => NULL,
                      'bonus_cap_value' => NULL,
                      'status' => 1,
                    ],
                    3 => [
                      'level' => 4,
                      'bonus_percentage' => NULL,
                      'bonus_cap_value' => NULL,
                      'status' => 1,
                    ],
                    4 => [
                      'level' => 5,
                      'bonus_percentage' => NULL,
                      'bonus_cap_value' => NULL,
                      'status' => 1,
                    ],
                  ]),
            ];
            PromoRule::create($promoRuleArr);
            return $gcampaignCODE;
        } catch(\Exception $e){
            Log::error($e);
            return null;
        }
    }

        /**
     * function to get user Eligiblity IDs
     * 
     * @param $userId
     * @return array
     */
    public function getUserEligiblityIds($userId)
    {
        try {
            $eligiblityIdsWithName = \DB::table('milestone_eligibilities')->where('STATUS', 1)->pluck('MILESTONE_ELIGIBILITY_ID', 'ELIGIBILITY_CRITERIA_NAME')->toArray();

            $userEligiblityId = [];

            $depositCount = $this->checkDeposit($userId);

            foreach ($eligiblityIdsWithName as $eligiblityName => $eligiblityId) {
                $isUserEligible = $this->checkUserEligiblityByType($eligiblityName, $userId, $depositCount);
                if ($isUserEligible) {
                    array_push($userEligiblityId, $eligiblityId);
                }
            }
            return $userEligiblityId;
        } catch (\Exception $e) {
            Log::error($e);
            return [];
        }
    }

    /**
     * function to check yser eligiblity by type
     * 
     * @param $eligiblityName
     * @param $userId
     * @param $depositCount
     * 
     * @return boolean
     */
    public function checkUserEligiblityByType($eligiblityName, $userId, $depositCount)
    {
        try {
            $isUserEligible = false;

            switch ($eligiblityName) {
                case 'FTD':
                    //Check user deposit count
                    $isUserEligible = ($depositCount == 1);
                    break;
                case 'No Deposit User':
                    //Check No deposit user
                    $isUserEligible = ($depositCount == 0);
                    break;
                case 'Recurring Deposit User':
                    //Recurring Deposit User
                    $isUserEligible = ($depositCount > 1);
                    break;
                case 'No Deposit + Recurring User':
                    // No Deposit + Recurring User
                    $isUserEligible = Tracking::where('USER_ID', $userId)->exists() && $depositCount == 0;
                    break;
                case 'KYC check':
                    //KYC check
                    $isUserEligible = User::where('KYC_REMAINING_STEPS', 0)->where('USER_ID', $userId)->exists();
                    break;
                case 'Non KYC Users':
                    // Non KYC User
                    $isUserEligible = !(User::where('KYC_REMAINING_STEPS', 0)->where('USER_ID', $userId)->exists());
                    break;
                case 'All Users':
                    // All Users
                    $isUserEligible = true;
                    break;
                case 'User Segment':
                    //Segment User
                    $isUserEligible = true;
                    break;
                default:
                    $isUserEligible = false;
            }
            return $isUserEligible;
        } catch (\Exception $e) {
            Log::error($e);
            return false;
        }
    }

    /**
     * function to get total deposit count
     * 
     * @param $USER_ID
     * @return integer|\Exception
     */
    public function checkDeposit($USER_ID)
    {
        try {
            return MasterTransactionHistory::whereIn('TRANSACTION_TYPE_ID', [8, 61, 62, 83])
                ->whereIn('TRANSACTION_STATUS_ID', [103, 107, 125, 202])->where('USER_ID', $USER_ID)->count();
        } catch (\Exception $e) {
            Log::error($e);
            throw $e;
        }
    }

    public function updatePaymentStatusToFail($referenceNumber, $userId, $ignoreThreshold = false){
        $transactionVariablesArr = config('rummy_config.transactionVariables');

        if($ignoreThreshold){
            $waitingThreshold = 0;
        }else{
            $waitingThreshold = $transactionVariablesArr['waitingThreshold'];
        }

        $transaction = PaymentTransaction::where([
            'INTERNAL_REFERENCE_NO' => $referenceNumber, 'USER_ID' => $userId
        ])->whereRaw('PAYMENT_TRANSACTION_CREATED_ON <= now() - interval ? minute', [$waitingThreshold])->limit(1)->first();
        if(!empty($transaction)){
        
            $user = $this->getUserDetails($transaction->USER_ID);
    
            if(in_array($transaction->PAYMENT_TRANSACTION_STATUS, [122,280])){
                PaymentTransaction::where('INTERNAL_REFERENCE_NO', $transaction->INTERNAL_REFERENCE_NO)->update([
                    'PAYMENT_TRANSACTION_STATUS' => $transactionVariablesArr['transactionFailedPaymentStatus']
                ]);
    
                $this->userActivitiesTracking($userId, 'WAITING_TIME_FINISHED_PAYMENT_FAIL-DONE - ' . $referenceNumber, [$transaction]);
        
                $transactionDetails = PaymentTransaction::select('USER_ID','PG_REFERENCE_NOS', 'PAYMENT_TRANSACTION_STATUS', 'PAYMENT_TRANSACTION_AMOUNT', 'INTERNAL_REFERENCE_NO','PAYMENT_TRANSACTION_CREATED_ON')->where(['INTERNAL_REFERENCE_NO' => $transaction->INTERNAL_REFERENCE_NO])->whereIn('PAYPAL_RETURN_VALUES', ['juspay'])->first();
                $requestedVia = $transactionDetails->PG_REFERENCE_NOS['requestedVia'] ?? null;
                
                $successBag['status'] = "Failed";
                $successBag['txnid'] = $transaction->INTERNAL_REFERENCE_NO;
                $successBag['amount'] = $transaction->PAYMENT_TRANSACTION_AMOUNT;
    
                if (!empty($requestedVia)){
                    $tracking = Tracking::where('USER_ID', $transactionDetails->USER_ID)->latest()->first();
                    $deviceName = $requestedVia == 'app' ? $tracking->DEVICE_MODEL : $tracking->OPERATING_SYSTEM;
                } else {
                    $deviceName = '';
                }
              
                $depositEventData = [
                    'userId' => $transaction->USER_ID,
                    "event" => "Deposit Failed",
                    "evtData" => [
                        "amount" => $transaction->PAYMENT_TRANSACTION_AMOUNT,
                        "promoCode" => $transaction->PROMO_CODE ?? "",
                        "time_stamp" => $transaction->PAYMENT_TRANSACTION_CREATED_ON,
                        "paymentMethod" => "NA",
                        "device" => $deviceName,
                        "transaction_id" => $transaction->INTERNAL_REFERENCE_NO,
                        "payment_id" => $transaction->INTERNAL_REFERENCE_NO,
                        "gst" => $transaction->GST,
                        "amount_excluding_gst" => round($transaction->PAYMENT_TRANSACTION_AMOUNT - $transaction->GST, 2),
                        "username" => $user->USERNAME,
                        "amount_deducted_from_bank" => $transaction->TOTAL_AMOUNT,
                        "app_type" => $transaction->APP_TYPE
                    ]
                ];
        
                $this->createCleverTapEvent($depositEventData);
        
                // if($ignoreThreshold == false){
                //     //send email
                //     $this->sendMail([
                //         "email" => $user->EMAIL_ID,
                //         "username" => $user->USERNAME,
                //         "emailkey" => 'deposit_failed',
                //         "userId" => $transactionDetails->USER_ID,
                //         "internalReferenceNumber" => $referenceNumber,
                //         "amount" => $transactionDetails->PAYMENT_TRANSACTION_AMOUNT,
                //         "transactionDate" => $transactionDetails->PAYMENT_TRANSACTION_CREATED_ON,
                //         "paymentId" => $referenceNumber
                //     ]);
                // }
    
                return $this->successResponse(1, $successBag);
    
            }else{
                $successBag['status'] = $transaction->PAYMENT_TRANSACTION_STATUS == 125 ? "Success" : "Failed";
                $successBag['txnid'] = $transaction->INTERNAL_REFERENCE_NO;
                $successBag['amount'] = $transaction->PAYMENT_TRANSACTION_AMOUNT;
    
                return $this->successResponse(1, $successBag);
    
            }
        }else{
            $this->errorBag[] = [
                'code' => 422062,
                'message' => 'Action not allowed as transaction status is awaited.'
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
    }
}
