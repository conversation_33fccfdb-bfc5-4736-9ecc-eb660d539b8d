<?php

namespace App\Http\Controllers\Payment\AddMoney;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use App\Models\PaymentTransaction;
use App\Models\MasterTransactionHistory;
use App\Models\PromoCampaign;
use App\Models\Tournament;
use App\Models\TournamentUserTicket;
use App\Models\TournamentUserEligibility;
use App\Models\CampaignToUser;
use App\Models\DepositGstInvoice;
use App\Models\PaymentFailureMessageLog;
use App\Models\UserPoint;
use App\Models\User;
use DB, Log;
use App\Models\RFActivityConfig;
use App\Models\RFUserMapping;
use App\Models\RFGlobalConfig;
use App\Models\RFTransactionHitory;
use App\Models\UserDetail;
use Carbon\Carbon;
use App\Models\EmailerPreventedEmail;
use Exception;
use App\Models\GlobalConfig;
use FacebookAds\Api;
use FacebookAds\Logger\CurlLogger;
use FacebookAds\Object\ServerSide\ActionSource;
use FacebookAds\Object\ServerSide\CustomData;
use FacebookAds\Object\ServerSide\Event;
use FacebookAds\Object\ServerSide\EventRequest;
use FacebookAds\Object\ServerSide\UserData;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Queue;
use App\Jobs\DepositInvoiceReconcileJob;
use App\Models\MasterTransactionHistoryFppBonus;
use App\Models\Tracking;

/**
 * Payment Webhook Handlers
 *
 * @category    Payments
 * @package     Deposit
 * @copyright   RummyBaazi
 * <AUTHOR> Sharma<<EMAIL>>
 */
class WebhookHandlerController extends AddMoneyBaseController
{

    /**
     * Common Payment Webhook Response Handler
     *
     * ! Need to remove player ledger functionality
     *
     * @param Request $request
     * @return object|string
     */
    public function webHookResponse(Request $request)
    {
        //Log::info("JusPay WebHook IP: " . json_encode($_SERVER));
        $ip = $this->getRealIpAddr();
        $requestAllowedFromIP = false;
        $apiCode = 1;
        $errorCode = 422000;
        $content = $request->get('content');

        if (!empty($request->udf2)) {
            $payUAllowedIPs = explode("|", config('rummy_config.PAYU_MONEY_WEBHOOK_IP'));
            if (in_array('0.0.0.0', $payUAllowedIPs) || in_array($ip, $payUAllowedIPs)) {
                $requestAllowedFromIP = true;
            }
            $referenceNumber = $request->udf2;
            $requestAmount = $request->amount;
        } elseif (!empty($content['order']['udf2'])) {
            $jusPayAllowedIPs = explode("|", config('rummy_config.JUSPAY_WEBHOOK_IP'));
            if (in_array('0.0.0.0', $jusPayAllowedIPs) || in_array($ip, $jusPayAllowedIPs)) {
                $requestAllowedFromIP = true;
            }
            $referenceNumber = $content['order']['udf2'];
            $requestAmount = $content['order']['amount'];
        } else {
            $referenceNumber = NULL;
            $requestAmount = 0;
        }
        if ($requestAllowedFromIP) {
            $transactionVariablesArr = config('rummy_config.transactionVariables');
            $transaction = PaymentTransaction::where([
                'INTERNAL_REFERENCE_NO' => $referenceNumber
            ])->limit(1)->first();

            /* Check if transaction exists */
            if (collect($transaction)->isEmpty()) {
                $this->userActivitiesTracking(NULL, 'WEBHOOK_TRANSACTION_NOT_FOUND', $request->all());
                return $this->errorResponse($apiCode, array([
                    'code' => $errorCode,
                    'message' => 'Transction not found with the given transaction id.'
                ]));
            }

            /* Check if user id not blank */
            if (empty($transaction->USER_ID)) {
                $this->userActivitiesTracking(NULL, 'USER_ID_NOT_FOUND_IN_WEBHOOK_TRANSACTION', $request->all());
                return $this->errorResponse($apiCode, array([
                    'code' => $errorCode,
                    'message' => 'Transction not contain user id.'
                ]));
            }

            /* Check if webhook already processed the given transaction */
            $masterTransaction = MasterTransactionHistory::where([
                'INTERNAL_REFERENCE_NO' => $referenceNumber,
                'USER_ID' => $transaction->USER_ID
            ])->limit(1)->first();

            if (collect($masterTransaction)->isNotEmpty()) {
                $this->userActivitiesTracking($transaction->USER_ID, 'WEBHOOK_TRANSACTION_ALREADY_PROCESSED', $request->all());
                return $this->successResponse($apiCode);
            }

            if($transaction->TOTAL_AMOUNT > 0){
                /* Check for amount mismatch */
                if (((float) $requestAmount) !== ((float) $transaction->TOTAL_AMOUNT)) {
                    $this->userActivitiesTracking($transaction->USER_ID, 'WEBHOOK_TRANSACTION_AMOUNT_MIS_MATCH', [$request->all(), $transaction]);
                    return $this->errorResponse($apiCode, array([
                        'code' => $errorCode,
                        'message' => 'Transction amount mismatch.'
                    ]));
                }
            }else{
                /* Check for amount mismatch */
                if (((float)$requestAmount) !== ((float)$transaction->PAYMENT_TRANSACTION_AMOUNT)) {
                    $this->userActivitiesTracking($transaction->USER_ID, 'WEBHOOK_TRANSACTION_AMOUNT_MIS_MATCH', [$request->all(), $transaction]);
                    return $this->errorResponse($apiCode, array([
                        'code' => $errorCode,
                        'message' => 'Transction amount mismatch.'
                    ]));
                }
            }
            // if ($transaction->PAYMENT_TRANSACTION_STATUS != $transactionVariablesArr['depositTransactionStatusPendingID']) {
            //     $this->userActivitiesTracking($transaction->USER_ID, 'WEBHOOK_PAYMENT_STATUS_ALREADY_PROCESSED', $request->all());
            //     return $this->errorResponse($apiCode, array([
            //         'code' => $errorCode,
            //         'message' => 'Given transaction status or payment status already processed.'
            //     ]));
            // }
            $user = $this->getUserDetails($transaction->USER_ID);
            /* START PROCESSING PAYMENT */
            if ($request->has('udf2')) {
                if (!empty($request->mihpayid)) {
                    $pg_transaction_id = $request->mihpayid;
                }
                return $this->payuWebhookHandler($request, $transaction, $transactionVariablesArr, $user, $pg_transaction_id, $referenceNumber);
            } elseif (!empty($content['order']['udf2'])) {
                if (!empty($content['order']['order_id'])) {
                    $pg_transaction_id = $content['order']['order_id'];
                }

                // fail the transaction after given threshold time
                $from = $transaction->PAYMENT_TRANSACTION_CREATED_ON;
              
                $diff_time = (strtotime(date('Y-m-d H:i:s'))-strtotime($from))/60;
               
                if($diff_time >= $transactionVariablesArr['waitingThreshold'] && $request->event_name == "ORDER_SUCCEEDED"){
                    if($transaction->PAYMENT_TRANSACTION_STATUS == $transactionVariablesArr['depositTransactionStatusInitiatedID']){
                        $this->userActivitiesTracking($transaction->USER_ID, "PAYMENT_FAILED_DUE_TO_WEBHOOK_WAITING_TIME_EXCEEDED_$diff_time-$transaction->INTERNAL_REFERENCE_NO", $request->all());
                        
                        $refNos = $transaction->PG_REFERENCE_NOS;
                        if (!empty($request->content['order']['id'])) {
                            $refNos['jusPayOrderID'] = $request->content['order']['id'];
                        }
                        if (!empty($request->content['order']['payment_gateway_response']['txn_id'])) {
                            $refNos['jusPayPGTxnID'] = $request->content['order']['payment_gateway_response']['txn_id'];
                        }
                        if (!empty($request->content['order']['payment_gateway_response']['epg_txn_id'])) {
                            $refNos['paymentGatewayTxnID'] = $request->content['order']['payment_gateway_response']['epg_txn_id'];
                        }
                        if (!empty($request->content['order']['txn_detail']['gateway'])) {
                            $refNos['paymentMethod'] = "JusPay-" . $request->content['order']['txn_detail']['gateway'];
                        }
                        $paymentMethodType = "";
                        $cardType = "";
                        if (!empty($request->content['order']['payment_method_type'])) {
                            $paymentMethodType = $request->content['order']['payment_method_type'];
                            $refNos['paymentMethodType'] = $paymentMethodType;
                            if ($paymentMethodType == "CARD" && !empty($request->content['order']['card']['card_type'])) {
                                $cardType = $request->content['order']['card']['card_type'];
                                $refNos['paymentMethodType'] = $cardType;
                            }
                        }
                        if (!empty($request->content['order']['payment_method'])) {
                            $refNos['paymentProcessedVia'] = $request->content['order']['payment_method'];
                        }
                        PaymentTransaction::where('INTERNAL_REFERENCE_NO', $transaction->INTERNAL_REFERENCE_NO)->update([
                            'PAYMENT_TRANSACTION_STATUS' => $transactionVariablesArr['transactionFailedPaymentStatus'],
                            'PG_REFERENCE_NOS' => $refNos
                        ]);
                        
                        // $transactionDetails = PaymentTransaction::select('USER_ID','PG_REFERENCE_NOS')->where('INTERNAL_REFERENCE_NO', $transaction->INTERNAL_REFERENCE_NO)->first();
                        $requestedVia = $transaction->PG_REFERENCE_NOS['requestedVia'] ?? null;
            
                        if (!empty($requestedVia)){
                            $tracking = Tracking::where('USER_ID', $transaction->USER_ID)->orderBy('DATE_TIME', 'DESC')->first();
                            $deviceName = $requestedVia == 'app' ? $tracking->DEVICE_MODEL : $tracking->OPERATING_SYSTEM;
                        } else {
                            $deviceName = '';
                        }

                        $appType = $tracking->APP_TYPE ?? $transaction->PG_REFERENCE_NOS['appType'] ?? null;

                        $depositEventData = [
                            'userId' => $transaction->USER_ID,
                            "event" => "Deposit Failed",
                            "evtData" => [
                                "amount" => $transaction->PAYMENT_TRANSACTION_AMOUNT,
                                "promoCode" => $transaction->PROMO_CODE ?? "",
                                "time_stamp" => $transaction->PAYMENT_TRANSACTION_CREATED_ON,
                                "paymentMethod" => "NA",
                                "device" => $deviceName,
                                "transaction_id" => $transaction->INTERNAL_REFERENCE_NO,
                                "payment_id" => $transaction->INTERNAL_REFERENCE_NO,
                                "gst" => $transaction->GST,
                                "amount_excluding_gst" => round($transaction->PAYMENT_TRANSACTION_AMOUNT - $transaction->GST, 2),
                                "username" => $user->USERNAME,
                                "amount_deducted_from_bank" => $transaction->TOTAL_AMOUNT,
                                "app_type" => $transaction->APP_TYPE
                            ]
                        ];
            
                        $this->createCleverTapEvent($depositEventData);
                        return $this->successResponse($apiCode);
                    }else{
                        return $this->successResponse($apiCode);
                    }
                }  

                return $this->juspayWebhookHandler($request, $transaction, $transactionVariablesArr, $user, $pg_transaction_id, $referenceNumber);
            } else {
                $this->userActivitiesTracking(NULL, 'WEBHOOK_UNABLE_TO_IDENTIFY_PAYMENT_GATEWAY', $request->all());
                return $this->errorResponse($apiCode, array([
                    'code' => $errorCode,
                    'message' => 'Unable to identify payment gateway.'
                ]));
            }
        } else {
            $this->userActivitiesTracking(NULL, 'WEBHOOK_REQUEST_FROM_UNALLOWED_IP', $request->all());
            return $this->errorResponse($apiCode, array([
                'code' => $errorCode,
                'message' => 'Transction came from invalid IP.'
            ]));
        }
    }

    /**
     * Payment Return response Handler
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|\Laravel\Lumen\Http\Redirector|string
     */
    public function returnResponse(Request $request)
    {
        $txn_id = "";
        if (!empty($request->order_id)) {
            $txn_id = $request->order_id;
        } elseif (!empty($request->udf2)) {
            $txn_id = $request->udf2;
        }
        if (!empty($txn_id)) {
            $txn = PaymentTransaction::where('INTERNAL_REFERENCE_NO', $txn_id)->first();
            $requestedVia = "";
            if (!empty($txn->PG_REFERENCE_NOS['requestedVia'])) {
                $requestedVia = $txn->PG_REFERENCE_NOS['requestedVia'];
                $redirectUrl = "";
                if (strtolower($requestedVia) == "app") {
                    $websiteBaseUrl = config('rummy_config.website_base_url');
                    $paymentResponseRoute = config('rummy_config.payment_response_route');
                    $redirectUrl = $websiteBaseUrl . $paymentResponseRoute . '?txn_id=' . $txn_id;
                } elseif (strtolower($requestedVia) == "web") {
                    $accountSectionBaseURL = config('rummy_config.accountSectionBaseURL');
                    $accountSectionResponseRoute = config('rummy_config.accountSectionResponseRoute');
                    $redirectUrl = $accountSectionBaseURL . $accountSectionResponseRoute . '?txn_id=' . $txn_id;
                }
                return redirect($redirectUrl);
            }
        } else {
            $websiteBaseUrl = config('rummy_config.website_base_url');
            $paymentResponseRoute = config('rummy_config.payment_response_route');
            $redirectUrl = $websiteBaseUrl . $paymentResponseRoute;
            return $redirectUrl;
        }
    }

    private function payuWebhookHandler(Request $request, object $transaction, array $transactionVariablesArr, object $user, string $pg_transaction_id, string $referenceNumber)
    {
        $apiCode = 1;
        $errorCode = 4220001;
        $this->userActivitiesTracking($transaction->USER_ID, 'INITIATE_PAYMENT_THROUGH_PAYU_WEBHOOK - ' . $referenceNumber, [$request->all(), $transaction]);

        $key = config('payU.payU_key');
        $salt = config('payU.payu_salt');

        $rehash = $salt . '|' . strtolower($request->status) . '||||||||' . $request->udf3 . '|' . $request->udf2 . '|' . $request->udf1 . '|' . $request->customerEmail . '|' . $request->firstname . '|' . $request->productInfo . '|' . $request->amount . '|' . $request->udf2 . '|' . $key;

        $rehash = hash('sha512', $rehash);

        /* Check received and generated hash matched or not */
        if (strtolower($request->hash) != strtolower($rehash)) {
            $this->userActivitiesTracking($transaction->USER_ID, 'PAYU_WEBHOOK_HASH_NOT_MATCHED - ' . $referenceNumber, [$rehash]);
            return $this->errorResponse($apiCode, array([
                'code' => $errorCode,
                'message' => 'Payu hash not matched.'
            ]));
        }

        $this->userActivitiesTracking($transaction->USER_ID, 'PAYU_WEBHOOK_HASH_MATCHED - ' . $referenceNumber, [$rehash]);

        if (strtolower($request->status) == 'success') {
            try {
                $ok = $this->validatePromoCodeThenProcessForPaymentStatus($request, $transaction, $transactionVariablesArr, $user, $pg_transaction_id, $referenceNumber);

                if ($ok == 'ok') {
                    return $this->successResponse($apiCode);
                } elseif ($ok == 'recently_updated') {
                    $this->userActivitiesTracking($transaction->USER_ID, 'TRANSACTION_PREVENTED_FROM_REPROCESSING_AS_IT_WAS_RECENTLY_UPDATED - ' . $referenceNumber, ['WEBHOOK_REQUEST_PAYLOAD' => $request->all()]);
                    return $this->successResponse($apiCode);
                } else {
                    $this->userActivitiesTracking($transaction->USER_ID, 'PAYU_SOMETHING_WENT_WRONG_IN_PAYMENT_AND_PROMO_CODE_PROCESS_NOT_RECEIVED_OK - ' . $referenceNumber, [$request->all(), $transaction]);

                    return $this->errorResponse($apiCode, array([
                        'code' => $errorCode,
                        'message' => 'Someting went wrong in PAYU payment and promocode processs not received OK.'
                    ]));
                }
            } catch (\Exception $e) {

                $this->userActivitiesTracking($transaction->USER_ID, 'PAYU_SOMETHING_WENT_WRONG_IN_PAYMENT_AND_PROMO_CODE_PROCESS - ' . $referenceNumber, [$request->all(), $transaction]);

                return $this->errorResponse($apiCode, array([
                    'code' => $errorCode,
                    'message' => 'Someting went wrong in PAYU payment and promocode processs.'
                ]));
            }
        } else {
            $this->userActivitiesTracking($transaction->USER_ID, 'ERROR_CAME_FROM_PAYU_WEBHOOK_PAYMENT_FAILED - ' . $referenceNumber, [$request->all(), $transaction]);

            try {
                PaymentFailureMessageLog::insertOrIgnore(
                    ['message_hash' => md5($request->content['order']['bank_error_message']), 'module' => 'Payment', 'message' => $request->content['order']['bank_error_message']]
                );
            } catch (Exception $e) {
            }

            $refNos = $transaction->PG_REFERENCE_NOS;
            $refNos['paymentMethod'] = NULL;
            if (!empty($request->mihpayid)) {
                $refNos['payUSeamlessTxnID'] = $request->mihpayid;
                $refNos['paymentMethod'] = "PayUSeamless";
            }
            PaymentTransaction::where('INTERNAL_REFERENCE_NO', $transaction->INTERNAL_REFERENCE_NO)->update([
                'PAYMENT_TRANSACTION_STATUS' => $transactionVariablesArr['transactionFailedPaymentStatus'],
                'PG_REFERENCE_NOS' => $refNos
            ]);

            $data = [
                "type" => "event",
                "type_name" => "Deposit",
                "data" => [
                    "identity" => $transaction->USER_ID,
                    "Success" => false,
                    "Pending" => false,
                    "Code" => $transaction->PROMO_CODE,
                    "Amount" => $transaction->PAYMENT_TRANSACTION_AMOUNT,
                    "Gateway_Fee" => $transaction->DISC_GATEWAY_FEE,
                    "Amount_Deducted" => $transaction->TOTAL_AMOUNT,
                    "Payment Method" => $refNos['paymentMethod']
                ]
            ];
            $resp = $this->putOnKinesis($data);
            $this->userActivitiesTracking($transaction->USER_ID, 'KINESIS_DEPOSIT_FAILED', [
                $data,
                'resp' => $resp
            ]);
            //PaymentTransaction::where(['INTERNAL_REFERENCE_NO' => $transaction->INTERNAL_REFERENCE_NO])->update(['PAYMENT_TRANSACTION_STATUS' => 204]);

            // CB-821 (if - else logic only)
            $checkEmailId = EmailerPreventedEmail::where('EMAIL_ID', $user->EMAIL_ID)->get();
            if (count($checkEmailId) > 0) {
                Log::info("Emails not sent as email id of user " . $user->EMAIL_ID . " is in bounce restricted email list. Transaction ID: " . $referenceNumber);
            } else {
                //send email
                if ($user->EMAIL_VERIFY == 1) {
                    $this->sendMail([
                        "email" => $user->EMAIL_ID,
                        "username" => $user->USERNAME,
                        "emailkey" => 'deposit_failed',
                        "userId" => $transaction->USER_ID,
                        "internalReferenceNumber" => $referenceNumber,
                        "amount" => $transaction->PG_REFERENCE_NOS['gst']['gst_waived_off'] ? $transaction->PAYMENT_TRANSACTION_AMOUNT : $transaction->PAYMENT_TRANSACTION_AMOUNT-$transaction->GST,
                        "transactionDate" => $transaction->PAYMENT_TRANSACTION_CREATED_ON,
                        "paymentId" => $pg_transaction_id
                    ]);
                }
            }
            return $this->errorResponse($apiCode, array([
                'code' => $errorCode,
                'message' => 'Payu webhook payment failed.'
            ]));
        }
    }

    private function juspayWebhookHandler(Request $request, object $transaction, array $transactionVariablesArr, object $user, string $pg_transaction_id, string $referenceNumber)
    {
        $apiCode = 1;
        $errorCode = 4220001;
        $content = $request->get('content');

        $this->userActivitiesTracking($transaction->USER_ID, 'INITIATE_PAYMENT_THROUGH_JUSPAY_WEBHOOK - ' . $referenceNumber, [$request->all(), $transaction]);

        $secretKey = config('rummy_config.juspay.juspay_response_key');

        if ($request->event_name == "ORDER_SUCCEEDED") {
            if(!in_array($transaction->PAYMENT_TRANSACTION_STATUS, [$transactionVariablesArr['depositTransactionStatusInitiatedID'] ])){
                $this->userActivitiesTracking($transaction->USER_ID, 'WEBHOOK_PAYMENT_STATUS_ALREADY_PROCESSED', $request->all());
                return $this->successResponse($apiCode);
            }
            try {
                $ok = $this->validatePromoCodeThenProcessForPaymentStatus($request, $transaction, $transactionVariablesArr, $user, $pg_transaction_id, $referenceNumber);
                if ($ok == 'ok') {
                    return $this->successResponse($apiCode);
                } elseif ($ok == 'recently_updated') {
                    $this->userActivitiesTracking($transaction->USER_ID, 'TRANSACTION_PREVENTED_FROM_REPROCESSING_AS_IT_WAS_RECENTLY_UPDATED - ' . $referenceNumber, ['WEBHOOK_REQUEST_PAYLOAD' => $request->all()]);
                    return $this->successResponse($apiCode);
                } else {
                    $this->userActivitiesTracking($transaction->USER_ID, 'JUSPAY_SOMETHING_WENT_WRONG_IN_PAYMENT_AND_PROMO_CODE_PROCESS_NOT_RECEIVED_OK - ' . $referenceNumber, [$request->all(), $transaction]);

                    return $this->errorResponse($apiCode, array([
                        'code' => $errorCode,
                        'message' => 'Someting went wrong in JUSPAY payment and promocode processs not received OK.'
                    ]));
                }
            } catch (\Exception $e) {
                \Log::error($e);
                $this->userActivitiesTracking($transaction->USER_ID, 'JUSPAY_SOMETHING_WENT_WRONG_IN_PAYMENT_AND_PROMO_CODE_PROCESS - ' . $referenceNumber, [$request->all(), $transaction]);

                return $this->errorResponse($apiCode, array([
                    'code' => $errorCode,
                    'message' => 'Someting went wrong in JUSPAY payment and promocode process.'
                ]));
            }
        } elseif ($request->event_name == "TXN_CREATED") {
            $refNos = $transaction->PG_REFERENCE_NOS;
            if(!in_array($transaction->PAYMENT_TRANSACTION_STATUS, [$transactionVariablesArr['depositTransactionStatusPendingID']])){
                $this->userActivitiesTracking($transaction->USER_ID, 'WEBHOOK_PAYMENT_STATUS_ALREADY_PROCESSED', $request->all());
                return $this->errorResponse($apiCode, array([
                    'code' => 422000,
                    'message' => 'Given transaction status or payment status already processed.'
                ]));
            }

            $transactionCount = $this->getUserTotaltransactions($transaction->USER_ID);
            
            $refNos['ftdFlag'] = false;
    
            if ($transactionCount == 0) {
                $refNos['ftdFlag'] = true;
            }

            // Capture payment method on payment creation Starts

            $paymentMethodType = "";
            $cardType = "";
            
            if (!empty($request->content['order']['id'])) {
                $refNos['jusPayOrderID'] = $request->content['order']['id'];
            }
            if (!empty($request->content['order']['payment_gateway_response']['txn_id'])) {
                $refNos['jusPayPGTxnID'] = $request->content['order']['payment_gateway_response']['txn_id'];
            }
            if (!empty($request->content['order']['payment_gateway_response']['epg_txn_id'])) {
                $refNos['paymentGatewayTxnID'] = $request->content['order']['payment_gateway_response']['epg_txn_id'];
            }
            if (!empty($request->content['order']['txn_detail']['gateway'])) {
                $refNos['paymentMethod'] = "JusPay-" . $request->content['order']['txn_detail']['gateway'];
            }
            if (!empty($request->content['order']['payment_method_type'])) {
                $paymentMethodType = $request->content['order']['payment_method_type'];
                $refNos['paymentMethodType'] = $paymentMethodType;
                if ($paymentMethodType == "CARD" && !empty($request->content['order']['card']['card_type'])) {
                    $cardType = $request->content['order']['card']['card_type'];
                    $refNos['paymentMethodType'] = $cardType;
                    $refNos['nameOnCard'] = $request->content['order']['card']['name_on_card'];
                    $refNos['lastFourDigit'] = $request->content['order']['card']['last_four_digits'];
                    $refNos['expiryYear'] = $request->content['order']['card']['expiry_year'];
                    $refNos['expiryMonth'] = $request->content['order']['card']['expiry_month'];
                }

                if ($paymentMethodType == "UPI" && !empty($request->content['order']['upi'])) { 
                    $refNos['txnFlowType'] = $request->content['order']['upi']['txn_flow_type'];
                    $refNos['payerApp'] = $request->content['order']['upi']['payer_app'];
                    $refNos['payerAppName'] = $request->content['order']['payer_app_name'];
                }

            }
            if (!empty($request->content['order']['payment_method'])) {
                $refNos['paymentProcessedVia'] = $request->content['order']['payment_method'];
            }

            // Capture payment method on payment creation Ends

            PaymentTransaction::where('INTERNAL_REFERENCE_NO', $referenceNumber)->update([
                'PAYMENT_TRANSACTION_STATUS' => $transactionVariablesArr['depositTransactionStatusInitiatedID'],
                'PG_REFERENCE_NOS' => $refNos
            ]);

            $this->userActivitiesTracking($transaction->USER_ID, 'WEBHOOK_REQUEST_FROM_JUSPAY_TXN_CREATED - ' . $referenceNumber, [$request->all(), $transaction]);
            return $this->successResponse($apiCode);
        } elseif ($request->event_name == "ORDER_FAILED") {
            if(!in_array($transaction->PAYMENT_TRANSACTION_STATUS, [$transactionVariablesArr['depositTransactionStatusInitiatedID']])){
                $this->userActivitiesTracking($transaction->USER_ID, 'WEBHOOK_PAYMENT_STATUS_ALREADY_PROCESSED', $request->all());
                return $this->successResponse($apiCode);
            }
            $this->userActivitiesTracking($transaction->USER_ID, 'ERROR_CAME_FROM_JUSPAY_WEBHOOK_PAYMENT_FAILED - ' . $referenceNumber, [$request->all(), $transaction]);

            try {
                PaymentFailureMessageLog::insertOrIgnore(
                    ['message_hash' => md5($content['order']['bank_error_message']), 'module' => 'Payment', 'message' => $content['order']['bank_error_message']]
                );
            } catch (Exception $e) {
            }

            $refNos = $transaction->PG_REFERENCE_NOS;
            $refNos['paymentMethod'] = NULL;
            if (!empty($content['order']['id'])) {
                $refNos['jusPayOrderID'] = $content['order']['id'];
            }
            if (!empty($content['order']['payment_gateway_response']['txn_id'])) {
                $refNos['jusPayPGTxnID'] = $content['order']['payment_gateway_response']['txn_id'];
            }
            if (!empty($content['order']['payment_gateway_response']['epg_txn_id'])) {
                $refNos['paymentGatewayTxnID'] = $content['order']['payment_gateway_response']['epg_txn_id'];
            }
            if (!empty($content['order']['txn_detail']['gateway'])) {
                $refNos['paymentMethod'] = "JusPay-" . $content['order']['txn_detail']['gateway'];
            }
            PaymentTransaction::where('INTERNAL_REFERENCE_NO', $transaction->INTERNAL_REFERENCE_NO)->update([
                'PAYMENT_TRANSACTION_STATUS' => $transactionVariablesArr['transactionFailedPaymentStatus'],
                'PG_REFERENCE_NOS' => $refNos
            ]);

            $data = [
                "type" => "event",
                "type_name" => "Deposit",
                "data" => [
                    "identity" => $transaction->USER_ID,
                    "Success" => false,
                    "Pending" => false,
                    "Code" => $transaction->PROMO_CODE,
                    "Amount" => $transaction->PAYMENT_TRANSACTION_AMOUNT,
                    "Gateway_Fee" => $transaction->DISC_GATEWAY_FEE,
                    "Amount_Deducted" => $transaction->TOTAL_AMOUNT,
                    "Payment Method" => $refNos['paymentMethod']
                ]
            ];
            $resp = $this->putOnKinesis($data);
            $this->userActivitiesTracking($transaction->USER_ID, 'KINESIS_DEPOSIT_FAILED', [
                $data,
                'resp' => $resp
            ]);
            //PaymentTransaction::where(['INTERNAL_REFERENCE_NO' => $transaction->INTERNAL_REFERENCE_NO])->update(['PAYMENT_TRANSACTION_STATUS' => 204]);

            // CB-821 (if - else logic only)
            $checkEmailId = EmailerPreventedEmail::where('EMAIL_ID', $user->EMAIL_ID)->get();
            if (count($checkEmailId) > 0) {
                Log::info("Emails not sent as email id of user " . $user->EMAIL_ID . " is in bounce restricted email list. Transaction ID: " . $referenceNumber);
            } else {
                //send email
                if ($user->EMAIL_VERIFY == 1) {
                    $commonDiv = '<div
                    style="border-radius: 8px; background: #f5f5f5; padding: 30px">';
                    $commonDiv = $commonDiv .'<p
                    style="
                        color: #595959;
                        font-size: 16px;
                        font-weight: 700;
                        margin: 0px;
                        padding-bottom: 10px;
                    "
                    >
                    Transaction ID:
                    <span
                        style="color: #595959; font-size: 15px; font-weight: 400"
                        >'.$transaction->INTERNAL_REFERENCE_NO.'</span
                    >
                    </p>';
                    if(!empty($transaction->PROMO_CODE)){
                        $commonDiv = $commonDiv . '<p
                        style="
                            color: #595959;
                            font-size: 16px;
                            font-weight: 700;
                            margin: 0px;
                            padding-bottom: 10px;
                        "
                        >
                        Offer Code:
                        <span
                            style="color: #595959; font-size: 15px; font-weight: 400"
                        >
                            '.$transaction->PROMO_CODE.'</span
                        >
                        </p>';
                    }
                    $commonDiv = $commonDiv . '<p
                      style="
                        color: #595959;
                        font-size: 16px;
                        font-weight: 700;
                        margin: 0px;
                        padding-bottom: 10px;
                      "
                    >
                      Deposit Requested (incl. GST): ₹<span
                        style="color: #595959; font-size: 15px; font-weight: 400"
                      >
                        '.number_format($transaction->PAYMENT_TRANSACTION_AMOUNT, 2, '.', '').'</span
                      >
                    </p>';
    
                    if(!empty($transaction->PG_REFERENCE_NOS) && isset($transaction->PG_REFERENCE_NOS['gateway_fee_component']) && isset($transaction->PG_REFERENCE_NOS['gateway_fee_component']['disc_deposit_fee'])){

                        $processingFee = '';
                        if($transaction->PG_REFERENCE_NOS['gateway_fee_component']['disc_deposit_fee'] == 0 && $transaction->PG_REFERENCE_NOS['gateway_fee_component']['deposit_fee'] > 0){
                            $processingFee = $processingFee.'₹ <span style="color: #595959; font-size: 15px; font-weight: 400">';
                            $processingFee = $processingFee.'<s>'.number_format($transaction->PG_REFERENCE_NOS['gateway_fee_component']['deposit_fee'], 2, '.', '').'</s>'; 
                            $processingFee = $processingFee.'</span>';
                        } else if($transaction->PG_REFERENCE_NOS['gateway_fee_component']['disc_deposit_fee'] != $transaction->PG_REFERENCE_NOS['gateway_fee_component']['deposit_fee']){
                            $processingFee = $processingFee.'₹ <span style="color: #595959; font-size: 15px; font-weight: 400">';
                            $processingFee = $processingFee.'<s>'.number_format($transaction->PG_REFERENCE_NOS['gateway_fee_component']['deposit_fee'], 2, '.', '').'</s>'; 
                            $processingFee = $processingFee.'</span>';
                            $processingFee = $processingFee.' ₹ <span style="color: #595959; font-size: 15px; font-weight: 400">';
                            $processingFee = $processingFee.number_format($transaction->PG_REFERENCE_NOS['gateway_fee_component']['disc_deposit_fee']); 
                            $processingFee = $processingFee.'</span>';
                        } else {
                            $processingFee = $processingFee.'₹ <span style="color: #595959; font-size: 15px; font-weight: 400">';
                            $processingFee = $processingFee.number_format($transaction->PG_REFERENCE_NOS['gateway_fee_component']['disc_deposit_fee'], 2, '.', ''); 
                            $processingFee = $processingFee.'</span>';
                        }

                        $commonDiv = $commonDiv . '<p
                        style="
                            color: #595959;
                            font-size: 16px;
                            font-weight: 700;
                            margin: 0px;
                            padding-bottom: 10px;
                        "
                        >
                        Processing fee applied on deposit: 
                            '.$processingFee.'
                        </p>';
                    }
                    $commonDiv = $commonDiv . '<p
                      style="
                        color: #595959;
                        font-size: 16px;
                        font-weight: 700;
                        margin: 0px;
                        padding-bottom: 10px;
                      "
                    >
                      Amount failed to deposit: ₹<span
                        style="color: #595959; font-size: 15px; font-weight: 400"
                      >
                        '.number_format($transaction->TOTAL_AMOUNT, 2, '.', '').'</span
                      >
                    </p>';
                    $commonDiv = $commonDiv . '</div>';

                    $this->sendMail([
                        "email" => $user->EMAIL_ID,
                        "username" => $user->USERNAME,
                        "emailkey" => 'deposit_failed',
                        "userId" => $transaction->USER_ID,
                        "internalReferenceNumber" => $referenceNumber,
                        "amount" => number_format($transaction->TOTAL_AMOUNT, 2, '.', ''),
                        "transactionDate" => $transaction->PAYMENT_TRANSACTION_CREATED_ON,
                        "paymentId" => $pg_transaction_id,
                        "commonDiv" => $commonDiv
                    ]);
                }
                return $this->successResponse($apiCode);
            }
        } elseif ($request->event_name == "AUTO_REFUND_INITIATED" || $request->event_name == "REFUND_INITIATED") {

            if(!in_array($transaction->PAYMENT_TRANSACTION_STATUS, [$transactionVariablesArr['depositTransactionStatusInitiatedID'], $transactionVariablesArr['transactionFailedPaymentStatus']])){
                $this->userActivitiesTracking($transaction->USER_ID, 'WEBHOOK_PAYMENT_STATUS_ALREADY_PROCESSED', $request->all());
                return $this->successResponse($apiCode);
            }

            $this->userActivitiesTracking($transaction->USER_ID, 'ORDER_REFUNDED_RESPONSE_CAME_FROM_JUSPAY_WEBHOOK_PAYMENT_FAILED - ' . $referenceNumber, [$request->all(), $transaction]);

            $refNos = $transaction->PG_REFERENCE_NOS;
            if (!empty($request->content['order']['id'])) {
                $refNos['jusPayOrderID'] = $request->content['order']['id'];
            }
            if (!empty($request->content['order']['payment_gateway_response']['txn_id'])) {
                $refNos['jusPayPGTxnID'] = $request->content['order']['payment_gateway_response']['txn_id'];
            }
            if (!empty($request->content['order']['payment_gateway_response']['epg_txn_id'])) {
                $refNos['paymentGatewayTxnID'] = $request->content['order']['payment_gateway_response']['epg_txn_id'];
            }
            if (!empty($request->content['order']['txn_detail']['gateway'])) {
                $refNos['paymentMethod'] = "JusPay-" . $request->content['order']['txn_detail']['gateway'];
            }
            $paymentMethodType = "";
            $cardType = "";
            if (!empty($request->content['order']['payment_method_type'])) {
                $paymentMethodType = $request->content['order']['payment_method_type'];
                $refNos['paymentMethodType'] = $paymentMethodType;
                if ($paymentMethodType == "CARD" && !empty($request->content['order']['card']['card_type'])) {
                    $cardType = $request->content['order']['card']['card_type'];
                    $refNos['paymentMethodType'] = $cardType;
                }
            }
            if (!empty($request->content['order']['payment_method'])) {
                $refNos['paymentProcessedVia'] = $request->content['order']['payment_method'];
            }
            PaymentTransaction::where('INTERNAL_REFERENCE_NO', $transaction->INTERNAL_REFERENCE_NO)->update([
                'PAYMENT_TRANSACTION_STATUS' => $transactionVariablesArr['depositTransactionStatusRefundInitiatedID'],
                'PG_REFERENCE_NOS' => $refNos
            ]);

            $transactionDetails = PaymentTransaction::select('USER_ID','PG_REFERENCE_NOS')->where('INTERNAL_REFERENCE_NO', $transaction->INTERNAL_REFERENCE_NO)->first();
            $requestedVia = $transactionDetails->PG_REFERENCE_NOS['requestedVia'] ?? null;

            if (!empty($requestedVia)){
                $tracking = Tracking::where('USER_ID', $transactionDetails->USER_ID)->latest()->first();
                $deviceName = $requestedVia == 'app' ? $tracking->DEVICE_MODEL : $tracking->OPERATING_SYSTEM;
            } else {
                $deviceName = '';
            }

            return $this->successResponse($apiCode);
        } elseif ($request->event_name == "AUTO_REFUND_SUCCEEDED" || $request->event_name == "ORDER_REFUNDED") {
            if(!in_array($transaction->PAYMENT_TRANSACTION_STATUS, [$transactionVariablesArr['depositTransactionStatusInitiatedID'], $transactionVariablesArr['depositTransactionStatusRefundInitiatedID'], $transactionVariablesArr['transactionFailedPaymentStatus'] ])){

                $this->userActivitiesTracking($transaction->USER_ID, 'WEBHOOK_PAYMENT_STATUS_ALREADY_PROCESSED', $request->all());
                return $this->successResponse($apiCode);
            }

            $this->userActivitiesTracking($transaction->USER_ID, 'ORDER_REFUNDED_RESPONSE_CAME_FROM_JUSPAY_WEBHOOK_PAYMENT_FAILED - ' . $referenceNumber, [$request->all(), $transaction]);

            $refNos = $transaction->PG_REFERENCE_NOS;
            if (!empty($request->content['order']['id'])) {
                $refNos['jusPayOrderID'] = $request->content['order']['id'];
            }
            if (!empty($request->content['order']['payment_gateway_response']['txn_id'])) {
                $refNos['jusPayPGTxnID'] = $request->content['order']['payment_gateway_response']['txn_id'];
            }
            if (!empty($request->content['order']['payment_gateway_response']['epg_txn_id'])) {
                $refNos['paymentGatewayTxnID'] = $request->content['order']['payment_gateway_response']['epg_txn_id'];
            }
            if (!empty($request->content['order']['txn_detail']['gateway'])) {
                $refNos['paymentMethod'] = "JusPay-" . $request->content['order']['txn_detail']['gateway'];
            }
            $paymentMethodType = "";
            $cardType = "";
            if (!empty($request->content['order']['payment_method_type'])) {
                $paymentMethodType = $request->content['order']['payment_method_type'];
                $refNos['paymentMethodType'] = $paymentMethodType;
                if ($paymentMethodType == "CARD" && !empty($request->content['order']['card']['card_type'])) {
                    $cardType = $request->content['order']['card']['card_type'];
                    $refNos['paymentMethodType'] = $cardType;
                }
            }
            if (!empty($request->content['order']['payment_method'])) {
                $refNos['paymentProcessedVia'] = $request->content['order']['payment_method'];
            }
            PaymentTransaction::where('INTERNAL_REFERENCE_NO', $transaction->INTERNAL_REFERENCE_NO)->update([
                'PAYMENT_TRANSACTION_STATUS' => $transactionVariablesArr['depositTransactionStatusRefundApprovedID'],
                'PG_REFERENCE_NOS' => $refNos
            ]);

            //PaymentTransaction::where(['INTERNAL_REFERENCE_NO' => $transaction->INTERNAL_REFERENCE_NO])->update(['PAYMENT_TRANSACTION_STATUS' => 204]);
            $transactionDetails = PaymentTransaction::select('USER_ID','PG_REFERENCE_NOS')->where('INTERNAL_REFERENCE_NO', $transaction->INTERNAL_REFERENCE_NO)->first();
            $requestedVia = $transactionDetails->PG_REFERENCE_NOS['requestedVia'] ?? null;

            if (!empty($requestedVia)){
                $tracking = Tracking::where('USER_ID', $transactionDetails->USER_ID)->latest()->first();
                $deviceName = $requestedVia == 'app' ? $tracking->DEVICE_MODEL : $tracking->OPERATING_SYSTEM;
            } else {
                $deviceName = '';
            }

            $appType = $tracking->APP_TYPE ?? $transactionDetails->PG_REFERENCE_NOS['appType'] ?? null;

            $depositEventData = [
                'userId' => $transaction->USER_ID,
                "event" => "Deposit Refund Success",
                "evtData" => [
                    "amount" => $transaction->PAYMENT_TRANSACTION_AMOUNT,
                    "promoCode" => $transaction->PROMO_CODE ?? "",
                    "paymentMethod" => "NA",
                    "device" => $deviceName,
                    "transaction_id" => $transaction->INTERNAL_REFERENCE_NO,
                    "payment_id" => $transaction->INTERNAL_REFERENCE_NO,
                    "gst" => $transaction->GST,
                    "amount_excluding_gst" => round($transaction->PAYMENT_TRANSACTION_AMOUNT - $transaction->GST, 2),
                    "username" => $user->USERNAME,
                    "amount_deducted_from_bank" => $transaction->TOTAL_AMOUNT,
                    "app_type" => $transaction->APP_TYPE
                ]
            ];

            $this->createCleverTapEvent($depositEventData);
            return $this->successResponse($apiCode);

        } elseif ($request->event_name == "AUTO_REFUND_FAILED" || $request->event_name == "ORDER_REFUND_FAILED") {

            if(!in_array($transaction->PAYMENT_TRANSACTION_STATUS, [$transactionVariablesArr['depositTransactionStatusInitiatedID'], $transactionVariablesArr['depositTransactionStatusRefundInitiatedID'], $transactionVariablesArr['transactionFailedPaymentStatus'] ])){

                $this->userActivitiesTracking($transaction->USER_ID, 'WEBHOOK_PAYMENT_STATUS_ALREADY_PROCESSED', $request->all());
                return $this->successResponse($apiCode);
            }

            $this->userActivitiesTracking($transaction->USER_ID, 'ORDER_REFUNDED_RESPONSE_CAME_FROM_JUSPAY_WEBHOOK_PAYMENT_FAILED - ' . $referenceNumber, [$request->all(), $transaction]);

            $refNos = $transaction->PG_REFERENCE_NOS;
            if (!empty($request->content['order']['id'])) {
                $refNos['jusPayOrderID'] = $request->content['order']['id'];
            }
            if (!empty($request->content['order']['payment_gateway_response']['txn_id'])) {
                $refNos['jusPayPGTxnID'] = $request->content['order']['payment_gateway_response']['txn_id'];
            }
            if (!empty($request->content['order']['payment_gateway_response']['epg_txn_id'])) {
                $refNos['paymentGatewayTxnID'] = $request->content['order']['payment_gateway_response']['epg_txn_id'];
            }
            if (!empty($request->content['order']['txn_detail']['gateway'])) {
                $refNos['paymentMethod'] = "JusPay-" . $request->content['order']['txn_detail']['gateway'];
            }
            $paymentMethodType = "";
            $cardType = "";
            if (!empty($request->content['order']['payment_method_type'])) {
                $paymentMethodType = $request->content['order']['payment_method_type'];
                $refNos['paymentMethodType'] = $paymentMethodType;
                if ($paymentMethodType == "CARD" && !empty($request->content['order']['card']['card_type'])) {
                    $cardType = $request->content['order']['card']['card_type'];
                    $refNos['paymentMethodType'] = $cardType;
                }
            }
            if (!empty($request->content['order']['payment_method'])) {
                $refNos['paymentProcessedVia'] = $request->content['order']['payment_method'];
            }
            PaymentTransaction::where('INTERNAL_REFERENCE_NO', $transaction->INTERNAL_REFERENCE_NO)->update([
                'PAYMENT_TRANSACTION_STATUS' => $transactionVariablesArr['depositTransactionStatusRefundFailedID'],
                'PG_REFERENCE_NOS' => $refNos
            ]);

            //PaymentTransaction::where(['INTERNAL_REFERENCE_NO' => $transaction->INTERNAL_REFERENCE_NO])->update(['PAYMENT_TRANSACTION_STATUS' => 204]);
            $transactionDetails = PaymentTransaction::select('USER_ID','PG_REFERENCE_NOS')->where('INTERNAL_REFERENCE_NO', $transaction->INTERNAL_REFERENCE_NO)->first();
            $requestedVia = $transactionDetails->PG_REFERENCE_NOS['requestedVia'] ?? null;

            if (!empty($requestedVia)){
                $tracking = Tracking::where('USER_ID', $transactionDetails->USER_ID)->latest()->first();
                $deviceName = $requestedVia == 'app' ? $tracking->DEVICE_MODEL : $tracking->OPERATING_SYSTEM;
            } else {
                $deviceName = '';
            }

            $appType = $tracking->APP_TYPE ?? $transactionDetails->PG_REFERENCE_NOS['appType'] ?? null;

            $depositEventData = [
                'userId' => $transaction->USER_ID,
                "event" => "Deposit Refund Failed",
                "evtData" => [
                    "amount" => $transaction->PAYMENT_TRANSACTION_AMOUNT,
                    "promoCode" => $transaction->PROMO_CODE ?? "",
                    "paymentMethod" => "NA",
                    "device" => $deviceName,
                    "transaction_id" => $transaction->INTERNAL_REFERENCE_NO,
                    "payment_id" => $transaction->INTERNAL_REFERENCE_NO,
                    "gst" => $transaction->GST,
                    "amount_excluding_gst" => round($transaction->PAYMENT_TRANSACTION_AMOUNT - $transaction->GST, 2),
                    "username" => $user->USERNAME,
                    "amount_deducted_from_bank" => $transaction->TOTAL_AMOUNT,
                    "app_type" => $transaction->APP_TYPE
                ]
            ];

            $this->createCleverTapEvent($depositEventData);
            return $this->successResponse($apiCode);

        } else {
            $this->userActivitiesTracking($transaction->USER_ID, 'UNDEFINED_RESPONSE_CAME_FROM_JUSPAY_WEBHOOK', [$request->all(), $transaction]);
            return $this->successResponse($apiCode);
        }
    }

    /**
     * Common method for further process on all payment gateways
     *
     * ! Need to remove player ledger functionality
     *
     * @param Request $request
     * @param object $transaction
     * @param array $transactionVariablesArr
     * @param object $user
     * @param string $pg_transaction_id
     * @param string $referenceNumber
     * @return object|string
     */
    private function validatePromoCodeThenProcessForPaymentStatus(Request $request, object $transaction, array $transactionVariablesArr, object $user, string $pg_transaction_id, string $referenceNumber)
    {
        $content = $request->get('content');
        /* ---------------------------------------------------------------------
         * CHECK IF CURRENT TRANSACTION IS UNDER PROCESS
         * ----------------------------------------------------------------------
         */
        $refNos = $transaction->PG_REFERENCE_NOS;

        /**
         * Fetch Milestone Id from Transaction Data For Handling Multiple
         * Milestone Case
         */
        $milestoneId = $refNos['milestoneId'] ?? null;

        $refNos['paymentMethod'] = "";
        if (!empty($request->mihpayid)) {
            $refNos['payUSeamlessTxnID'] = $request->mihpayid;
            $refNos['paymentMethod'] = "PayUSeamless";
        }
        if (!empty($content['order']['id'])) {
            $refNos['jusPayOrderID'] = $content['order']['id'];
        }
        if (!empty($content['order']['payment_gateway_response']['txn_id'])) {
            $refNos['jusPayPGTxnID'] = $content['order']['payment_gateway_response']['txn_id'];
        }
        if (!empty($content['order']['payment_gateway_response']['epg_txn_id'])) {
            $refNos['paymentGatewayTxnID'] = $content['order']['payment_gateway_response']['epg_txn_id'];
        }
        if (!empty($content['order']['txn_detail']['gateway'])) {
            $refNos['gateway'] = "JusPay-" . $content['order']['txn_detail']['gateway'];
        }
        $refNos['paymentMethod'] = $content['order']['payment_method'] ?? "";

        $paymentMethodType = "";
        $cardType = "";
        if (!empty($request->content['order']['payment_method_type'])) {
            $paymentMethodType = $request->content['order']['payment_method_type'];
            $refNos['paymentMethodType'] = $paymentMethodType;
            if ($paymentMethodType == "CARD" && !empty($request->content['order']['card']['card_type'])) {
                $cardType = $request->content['order']['card']['card_type'];
                $refNos['paymentMethodType'] = $cardType;
            }

            switch ($paymentMethodType) {
                case "CARD":
                    $cardType = $request->content['order']['card'] ?? [];
                    $refNos['cardDetails'] = $cardType;
                    break;
                case "UPI":
                    $refNos['upiDetails'] = $request->content['order']['upi'] ?? [];
                    break;
                default:
                    //
            }
        }
        if (!empty($request->content['order']['payment_method'])) {
            $refNos['paymentProcessedVia'] = $request->content['order']['payment_method'];
        }
        // check if request from BO or from API
        $userName = '';
        if (!empty($content['updated_by'])) {
            $userName = $content['updated_by'];
        } else {
            $userName = $user->USERNAME;
        }
        // check if request from BO or from API
        $transactionStatus = '';
        if (!empty($content['transaction_status_id'])) {
            $transactionStatus = $content['transaction_status_id'];
        } else {
            $transactionStatus = $transactionVariablesArr['transactionSuccessPaymentStatus'];
        }

        $affecredRows = PaymentTransaction::where('INTERNAL_REFERENCE_NO', $transaction->INTERNAL_REFERENCE_NO)
            ->whereRaw('UPDATED_DATE < now() - INTERVAL 3 SECOND')
            ->update([
                'PAYMENT_TRANSACTION_STATUS' => $transactionStatus,
                'PG_REFERENCE_NOS' => $refNos,
                'PAYMENT_TRANSACTION_UPDATED_ON' => \DB::raw("NOW()"),
                'PAYMENT_TRANSACTION_UPDATED_BY' => $userName
            ]);

        if ($affecredRows > 0) {
            $apiCode = 1;
            $errorCode = 422000;
            $realCashCoinType = $transactionVariablesArr['depositCoinTypeID'];
            $userCurrentRealBalance = UserPoint::where([
                'COIN_TYPE_ID' => $realCashCoinType,
                'USER_ID' => $transaction->USER_ID
            ])->limit(1)->first();

            /* ---------------------------------------------------------------------
             * Inserting into master transaction history
             * ----------------------------------------------------------------------
             */
            $sendRAFEmail = false;
            $sendPromoBonusEmail = false;
            $sendTournamentTicketEmail = false;
            $tournamentTicketvariables = array();
            $refferingUserDetail = array();
            $promoBonusVariables = array();

            $processBonus = true;
            // $globalConfig = GlobalConfig::whereIn('CONFIG_KEY', ['doc_validation', 'kyc_method'])->get();
            // $kycMethod = $globalConfig->where('CONFIG_KEY', 'kyc_method')->pluck('CONFIG_VALUE')->first();

            DB::beginTransaction();
            try {
                $promoCampaign = "";
                $depositBalanceType = $transactionVariablesArr['depositBalanceTypeId'];
                $transactionCount = $this->getUserTotaltransactions($transaction->USER_ID);

                if (!empty($transaction->PROMO_CODE)) {
                    $query = PromoCampaign::query();
                    $query->from(app(PromoCampaign::class)->getTable() . " as pc");
                    $query->leftJoin('promo_rule as pr', 'pr.PROMO_CAMPAIGN_ID', '=', 'pc.PROMO_CAMPAIGN_ID');
                    $query->select('pc.*','pr.*', \DB::raw('pr.P_REWARD_DATA->>"$.P_PROMO_VALUE" as P_PROMO_VALUE_COMBO'), \DB::raw('pr.P_REWARD_DATA->>"$.P_PROMO_CAP_VALUE" as P_PROMO_CAP_VALUE_COMBO'));
                    $query->where('pc.PROMO_CAMPAIGN_CODE', $transaction->PROMO_CODE);
                    $query->where('pc.status', 1);
                    $query->orderBy('START_DATE_TIME', 'DESC');
                    $query->limit(1);
                    $promoCampaign = $query->first();
                    if (!empty($promoCampaign)) {
                        if ($promoCampaign->MONEY_OPTION == 2) {
                            $depositBalanceType = $transactionVariablesArr['promoBalanceTypeId'];
                        } else if ($promoCampaign->MONEY_OPTION == 3) {
                            $depositBalanceType = $transactionVariablesArr['winBalanceTypeId'];
                        }
                    }

                    // Pending Bonus functionality removed due to milestones intoduced to promote KYC

                    // if ($transactionCount == 0) {
                    //     if ($kycMethod == 'manual') {
                    //         if ($user->KYC_REMAINING_STEPS != 0) {
                    //             if (!$this->aadharVerificationStatus($transaction->USER_ID)) {
                    //                 $processBonus = false;
                    //             }
                    //         }
                    //     } else if (!$this->aadharVerificationStatus($transaction->USER_ID)) {
                    //         $processBonus = false;
                    //     }
                    // }

                    // if (!$processBonus) {
                    //     $this->userActivitiesTracking($transaction->USER_ID, 'PROMO_BONUS_ADDED_TO_PENDING_AS_AADHAAR_NOT_VERIFIED_BEFORE_FTD - ' . $referenceNumber, [$request->all(), $transaction]);
                    // }
                }

                $userDetails = $this->getUserDetails($transaction->USER_ID);
                $masterTransaction = new MasterTransactionHistory();
                $masterTransaction->USER_ID = $transaction->USER_ID;
                $masterTransaction->BALANCE_TYPE_ID = $depositBalanceType;
                $masterTransaction->TRANSACTION_STATUS_ID = $transactionVariablesArr['transactionSuccessPaymentStatus'];
                $masterTransaction->TRANSACTION_TYPE_ID = $transaction->TRANSACTION_TYPE_ID;
                $masterTransaction->TRANSACTION_AMOUNT = $amountTobeCredit = $transaction->PAYMENT_TRANSACTION_AMOUNT - $transaction->GST;
                $masterTransaction->TRANSACTION_DATE = date('Y-m-d H:i:s');
                $masterTransaction->INTERNAL_REFERENCE_NO = $transaction->INTERNAL_REFERENCE_NO;
                $masterTransaction->CURRENT_TOT_BALANCE = $userCurrentRealBalance->USER_TOT_BALANCE;
                $masterTransaction->CLOSING_TOT_BALANCE = ($userCurrentRealBalance->USER_TOT_BALANCE + $amountTobeCredit);
                $masterTransaction->PARTNER_ID = $userDetails->PARTNER_ID;
                $MasterTransactionHistoryRespurchaseAmmount = $masterTransaction->save();

                $columnToBeUpdated = "USER_DEPOSIT_BALANCE";
                if ($depositBalanceType == 1) {
                    $columnToBeUpdated = "USER_DEPOSIT_BALANCE";
                } elseif ($depositBalanceType == 2) {
                    $columnToBeUpdated = "USER_PROMO_BALANCE";
                } elseif ($depositBalanceType == 3) {
                    $columnToBeUpdated = "USER_WIN_BALANCE";
                }

                DB::select(
                    'update user_points SET
			                `VALUE` = ?,
			                `' . $columnToBeUpdated . '` = `' . $columnToBeUpdated . '` + ?,
			                `USER_TOT_BALANCE` = `USER_WIN_BALANCE` + `USER_PROMO_BALANCE` + `USER_DEPOSIT_BALANCE`,
			                `UPDATED_DATE` = ?
			    WHERE
			        `USER_ID` = ? AND
			        `COIN_TYPE_ID` = ?
			    ',
                    [
                        $transaction->PAYMENT_TRANSACTION_AMOUNT - $transaction->GST,
                        $transaction->PAYMENT_TRANSACTION_AMOUNT - $transaction->GST,
                        date('Y-m-d H:i:s'),
                        $transaction->USER_ID,
                        $realCashCoinType
                    ]
                );

                $userCurrentRealBalance->USER_TOT_BALANCE += $transaction->PAYMENT_TRANSACTION_AMOUNT;

                /**
                 * Make user eligible for tournaments
                 */
                $tournament_user_eligibility = TournamentUserEligibility::where('USER_ID', $transaction->USER_ID)->get();
                if (count($tournament_user_eligibility) > 0 && count($tournament_user_eligibility->where('IS_ELIGIBLE', 0)) > 0) {
                    TournamentUserEligibility::where('USER_ID', $transaction->USER_ID)->update(["IS_ELIGIBLE" => 1]);
                    //$tournament_user_eligibility->update(["IS_ELIGIBLE" => 1]);
                } elseif (count($tournament_user_eligibility) == 0) {
                    TournamentUserEligibility::insert([
                        ['TOURNAMENT_TYPE_ID' => 1, "USER_ID" => $transaction->USER_ID, "IS_ELIGIBLE" => 1, "CREATED" => \DB::raw("NOW()"), "UPDATED" => \DB::raw("NOW()")],
                        ['TOURNAMENT_TYPE_ID' => 2, "USER_ID" => $transaction->USER_ID, "IS_ELIGIBLE" => 1, "CREATED" => \DB::raw("NOW()"), "UPDATED" => \DB::raw("NOW()")],
                        ['TOURNAMENT_TYPE_ID' => 3, "USER_ID" => $transaction->USER_ID, "IS_ELIGIBLE" => 1, "CREATED" => \DB::raw("NOW()"), "UPDATED" => \DB::raw("NOW()")],
                    ]);
                }

                /**
                 * ---------------------------------------------------------------------
                 * GET LIFE TIME DEPOSIT COUNT
                 * ----------------------------------------------------------------------
                 */
                $paymentCount = PaymentTransaction::where('USER_ID', $transaction->USER_ID)
                    ->whereIn('PAYMENT_TRANSACTION_STATUS', [
                        $transactionVariablesArr['transactionSuccessPaymentStatus'],
                        $transactionVariablesArr['depositOkSuccessPaymentStatus']
                    ])->count();

                if ($paymentCount == 1 && ($transaction->PROMO_CODE == "WELCOMERB" || empty($promoCampaign))) {
                    $regPromo = PromoCampaign::query();
                    $regPromo->from(app(PromoCampaign::class)->getTable() . " as pc");
                    $regPromo->where('pc.PROMO_CAMPAIGN_CODE', 'REGISTRATION');
                    $regPromo->where('pc.status', 1);
                    $regPromo->orderBy('START_DATE_TIME', 'DESC');
                    $regPromo->limit(1);
                    $promoCampaign = $regPromo->first();

                    $REGISTRATION_TIMESTAMP = $user->REGISTRATION_TIMESTAMP;

                    $validitydays = "+" . $promoCampaign->BONUS_EXPIRY_INTERVAL_DAYS . " days";
                    $currentdate = strtotime(date("Y-m-d"));
                    $startdate = strtotime($REGISTRATION_TIMESTAMP);
                    $enddate = strtotime($validitydays, strtotime($REGISTRATION_TIMESTAMP));

                    if ($startdate <= $currentdate && $currentdate <= $enddate) {
                        $expiry_date = date('Y-m-d H:i:s', strtotime("+90 days", strtotime($REGISTRATION_TIMESTAMP)));
                        CampaignToUser::where('USER_ID', $transaction->USER_ID)
                            ->update(['EXPIRY_DATE' => $expiry_date]);
                    }
                }

                /**
                 * ------------------------------------------------------------------------------------------
                 * Update withdrawal criteria of a user
                 * -------------------------------------------------------------------------------------------
                 */
                $getUserTransactionLimit = $this->getUserTransactionLimit($transaction->USER_ID);
                if ($getUserTransactionLimit && $getUserTransactionLimit->ATK_STATUS == 1) {
                    $updateWithdrawalCriteria = $this->updateWithdrawalCriteria($transaction->INTERNAL_REFERENCE_NO);
                }

                /**
                 * ------------------------------------------------------------------------------------------
                 * Update TDS Policy
                 * -------------------------------------------------------------------------------------------
                 */
                //$this->updatePlayerLedgerDeposit($transaction->INTERNAL_REFERENCE_NO);
                $updatePlayerLedger = $this->reconcilePlayerLedger($transaction->USER_ID,  "DEPOSIT", $transaction->INTERNAL_REFERENCE_NO, false);


                /**
                 * ---------------------------------------------------------------------
                 * If user has a promo code
                 * ----------------------------------------------------------------------
                 */
                $applyPromoResponse = "";
                if (!empty($promoCampaign)) {
                    $this->userActivitiesTracking($transaction->USER_ID, "Initiate Promo Code - $referenceNumber", [
                        'step' => 'Initiate Promo Code'
                    ]);

                    $promoCodeValidationFlag = true;

                    //if (date('Y-m-d') >= $promoCampaign->START_DATE_TIME && date('Y-m-d') <= $promoCampaign->END_DATE_TIME) 
                    {
                        $usageCount = $promoCodeValidationFlag == false ?: $this->getPromocodeUsagecount($promoCampaign->PROMO_CAMPAIGN_ID);

                        if ($promoCodeValidationFlag && $usageCount >= $promoCampaign->EXP_USER_MAX) {
                            $promoCodeValidationFlag = false;
                            $this->userActivitiesTracking($transaction->USER_ID, "Deposit code already used maximum allowed times - " . $referenceNumber, [
                                "promo_campaign" => $promoCampaign,
                                "used_no_of_times" => $usageCount
                            ]);
                        }

                        $codeUsedNTimes = $promoCodeValidationFlag == false ?: $this->promoCodeUsageCheck($promoCampaign->PROMO_CAMPAIGN_ID, $transaction->USER_ID);
                        if (!empty($promoCampaign->MAX_CAP)) {
                            if ($promoCodeValidationFlag && $codeUsedNTimes >= $promoCampaign->MAX_CAP) {
                                $promoCodeValidationFlag = false;
                                $this->userActivitiesTracking($transaction->USER_ID, "Already User Used Promo Code Max Allowed - " . $referenceNumber, [
                                    "promo_campaign" => $promoCampaign,
                                    "used_no_of_times" => $codeUsedNTimes
                                ]);
                            }
                        }

                        if ($promoCodeValidationFlag) {
                            $applyPromoResponse = $this->applyPromoCode($promoCampaign, $user, $transaction, $processBonus);
                            if (array_key_exists('bonusAmount', $applyPromoResponse)) {
                                if ($applyPromoResponse['bonusAmount'] > 0) {
                                    $sendPromoBonusEmail = true;
                                    $promoBonusVariables['bonusAmount'] = $applyPromoResponse['bonusAmount'];
                                }
                            }

                            if (array_key_exists('tournamentIDs', $applyPromoResponse)) {
                                $sendTournamentTicketEmail = true;
                                $tournamentTicketvariables['tournamentName'] = implode(',', Tournament::select('TOURNAMENT_NAME')->whereIn('TOURNAMENT_ID', $applyPromoResponse['tournamentIDs'])->pluck('TOURNAMENT_NAME')->toArray());
                                $tournamentTicketvariables['depositCode'] = $transaction->PROMO_CODE;
                            }
                        }
                    }
                    /*else {
                        $promoCodeValidationFlag = false;
                        $this->userActivitiesTracking($transaction->USER_ID, "Bonus code has expired - " . $referenceNumber, [
                            "promo_campaign" => $promoCampaign
                        ]);
                    }*/
                }

                /*
                    User GST Bonus Start
                */
               
                if($transaction->GST > 0){
                    
                    $depositGSTBonusVariablesArray = config('rummy_config.transactionVariables.depositGstBonus');
                    $depositGSTBonusRate = $refNos['gst']['refund_rate'];
                    $gstBonusBalanceType = $refNos['gst']['refund_balance_type'];
                    if(!empty($user->STATE)){
                        $stateCode = $this->getStateCode($user->STATE);
                    } else {
                        $stateCode = $this->getStateCode('Delhi');
                    }
                    
                    if($stateCode->display_status == 2 || $stateCode->STATE_CODE_TIN == '07'){
                        $taxBreakup = [
                                        'CGST_AMOUNT' => round($transaction->GST/2, 2),
                                        'SGST_AMOUNT' => round($transaction->GST/2, 2),
                                        'IGST_AMOUNT' => 0,
                                        'CGST_ON_PROCESSING_FEE' => round($refNos['gateway_fee_component']['gst_on_disc_deposit_fee']/2, 2),
                                        'SGST_ON_PROCESSING_FEE' => round($refNos['gateway_fee_component']['gst_on_disc_deposit_fee']/2, 2),
                                        'IGST_ON_PROCESSING_FEE' => 0
                                        ];

                    }else{
                        $taxBreakup = [
                                       'CGST_AMOUNT' => 0,
                                       'SGST_AMOUNT' => 0,
                                       'IGST_AMOUNT' => $transaction->GST,
                                       'CGST_ON_PROCESSING_FEE' => 0,
                                       'SGST_ON_PROCESSING_FEE' => 0,
                                       'IGST_ON_PROCESSING_FEE' => $refNos['gateway_fee_component']['gst_on_disc_deposit_fee']
                                      ];
                    }

                    $transactionDate = Carbon::now();
                    $depositInvoiceInsertArr = array();

                    try {
                        $depositInvoiceInsertArr = (array_merge([
                            'USER_ID' => $transaction->USER_ID,
                            'INTERNAL_REFERENCE_NO' => $transaction->INTERNAL_REFERENCE_NO,
                            'USERNAME' => $user->USERNAME,
                            'STATE' => $user->STATE ?? 'Delhi',
                            'STATE_CODE' =>  $stateCode->STATE_CODE_TIN,
                            'GSTIN_NO' => config('rummy_config.transactionVariables.gstIn'),
                            'INVOICE_NO' => $this->generateDepositInvoiceNo($transactionDate),
                            'PAYMENT_REFERENCE' => "Wallet Payment",
                            'SERVICE_DESCRIPTION' => "Actionable claim involved in or by way of online money gaming",
                            'HSN_SAC_CODE' => config('rummy_config.transactionVariables.hsnSacCode'),
                            'GATEWAY_HSN_CODE' => config('rummy_config.transactionVariables.gatewayHSNCode'),
                            'TAXABLE_VALUE' => $refNos['amount_to_be_credited'],
                            'SUB_TOTAL' => $transaction->PAYMENT_TRANSACTION_AMOUNT,
                            'TOTAL_PAID' => $transaction->TOTAL_AMOUNT,
                            'DEPOSIT_GST_RATE' => $refNos['gst']['rate'],
                            'PROCESSING_FEE' => $refNos['gateway_fee_component']['disc_deposit_fee'] - $refNos['gateway_fee_component']['gst_on_disc_deposit_fee'],
                            'PROCESSING_FEE_GST_RATE' => $refNos['gateway_fee_component']['fee_gst_rate'],
                            'CREATED_DATE' => $transactionDate,
                        ], $taxBreakup));
                        DepositGstInvoice::create($depositInvoiceInsertArr);
                    } catch (\Exception $e) {
                        Log::error('--- DEPOSIT INVOICE GENERATION FAILED ---');
                        Log::error($e); 

                        Queue::push(new DepositInvoiceReconcileJob([
                            'invoiceDataArr' => $depositInvoiceInsertArr
                        ]), '', config('queue.connections.sqs.driver')); 
                    }

                    if($depositGSTBonusRate > 0){
                        $userPoint = UserPoint::select('USER_TOT_BALANCE', 'USER_PROMO_BALANCE', 'USER_WIN_BALANCE', 'USER_DEPOSIT_BALANCE')->where('USER_ID', $transaction->USER_ID)
                            ->where('COIN_TYPE_ID', 1)
                            ->first();

                        /* insert in master transaction table  */
                        MasterTransactionHistory::create([
                            'USER_ID' =>  $transaction->USER_ID,
                            'BALANCE_TYPE_ID' =>  $gstBonusBalanceType,
                            'TRANSACTION_TYPE_ID' => $depositGSTBonusVariablesArray['transactionTypeID'],
                            'TRANSACTION_STATUS_ID' => $depositGSTBonusVariablesArray['transactionStatusID'],
                            'TRANSACTION_AMOUNT' => $gstBonus = $transaction->GST/100*$depositGSTBonusRate,
                            'TRANSACTION_DATE' => $transactionDate,
                            'INTERNAL_REFERENCE_NO' => $transaction->INTERNAL_REFERENCE_NO,
                            'CURRENT_TOT_BALANCE' => $userPoint->USER_TOT_BALANCE,
                            'CLOSING_TOT_BALANCE' => $userPoint->USER_TOT_BALANCE + $gstBonus,
                            'PARTNER_ID' => config('rummy_config.adminId'),
                        ]);

                        if ($gstBonusBalanceType == 1) {
                            $columnToBeUpdated = "USER_DEPOSIT_BALANCE";
                        } elseif ($gstBonusBalanceType == 2) {
                            $columnToBeUpdated = "USER_PROMO_BALANCE";
                        } elseif ($gstBonusBalanceType == 3) {
                            $columnToBeUpdated = "USER_WIN_BALANCE";
                        }

                        DB::select(
                        'update user_points SET
                            `VALUE` = ?,
                            `' . $columnToBeUpdated . '` = `' . $columnToBeUpdated . '` + ?,
                            `USER_TOT_BALANCE` = `USER_WIN_BALANCE` + `USER_PROMO_BALANCE` + `USER_DEPOSIT_BALANCE`,
                            `UPDATED_DATE` = ?
                        WHERE
                            `USER_ID` = ? AND
                            `COIN_TYPE_ID` = ?
                        ',
                                [
                                    $gstBonus,
                                    $gstBonus,
                                    $transactionDate,
                                    $transaction->USER_ID,
                                    1
                                ]
                            );

                        $this->userActivitiesTracking($transaction->USER_ID, "DEPOSIT_GST_BONUS_CREDITED- " . $transaction->INTERNAL_REFERENCE_NO, []);
                    }
                }

                /*
                    User GST Bonus End
                */

                // /**
                //  * RAF Module Start
                //  */
                // if ($user->KYC_REMAINING_STEPS == 0) 
                // {
                //     $rafGlobalSettings = \DB::connection('slave')->table('default_settings')
                //                                     ->where('KEY_VALUE', config('rummy_config.raf.rafGlobalConfigKey'))
                //                                     ->pluck('ACTUAL_VALUE')
                //                                     ->first();

                //     $rafGlobalConfig = json_decode($rafGlobalSettings);
                //     $userSetting = \DB::connection('slave')->table('user_settings')->where('USER_ID', $transaction->USER_ID)->first();

                //     $maxDuration = !empty($userSetting) ? $userSetting->MAX_DURATION_PER_FRIEND : $rafGlobalConfig->MAX_DURATION_PER_FRIEND;

                //     /* check for max duration of the referral and if raf is active for the user*/
                //     if ($userSetting->USER_RAF_BLOCK_STATUS == 1 && $maxDuration >= Carbon::now()->diffInDays($user->REGISTRATION_TIMESTAMP)) 
                //     {
                //     //     if active in global config check that activity type is active in rf_activity_config
                //     //     check if REFERRAL_USER_ID exist is rf_user_mapping
                //         $referrerDetails = RFUserMapping::select('REFER_USER_ID')
                //         ->where('REFERRAL_USER_ID', $transaction->USER_ID)
                //         ->first();
                //         $isRAFReleasable = 0;
                //         if (!empty($referrerDetails->REFER_USER_ID)) {
                //             $this->userActivitiesTracking($transaction->USER_ID, "DEPOSIT_WEBHOOK_RAF_START", [
                //                 'step' => 'initiated RAF',
                //                 'referrerDetails' => $referrerDetails,
                //                 'paymentCount' => $paymentCount,
                //                 'type' => $paymentCount == 1 ? 2 : 3
                //             ]);

                //             $isRAFReleasable = RFUserMapping::where('REFERRAL_USER_ID', $transaction->USER_ID)
                //                 ->where('ACTIVITY_TYPE_ID', $paymentCount == 1 ? 2 : 3)
                //                 ->where('RAF_RELEASE_STATUS', 1)
                //                 ->count();
                //             $output = "";
                //             if ($isRAFReleasable > 0) {
                //                 $output = $this->executeProcedureRAF($paymentCount == 1 ? 2 : 3, $referrerDetails->REFER_USER_ID, $transaction->USER_ID, $transaction->PAYMENT_TRANSACTION_AMOUNT);
                //             }

                //             $data = [
                //                 "UserId" => $transaction->USER_ID,
                //                 'RafID' => $referrerDetails->REFER_USER_ID,
                //                 "PaymentType" => $paymentCount == 1 ? 2 : 3,
                //                 "amount" => $transaction->PAYMENT_TRANSACTION_AMOUNT,
                //                 "data" => $output
                //             ];

                //             $action = "RAF - Payment procedure response - " . $referenceNumber;

                //             $this->userActivitiesTracking($transaction->USER_ID, $action, $data);
                //         }
                //     }
                // }
                // else {
                //     $this->userActivitiesTracking($transaction->USER_ID, "RAF_BONUS_SKIPPED_AS_KYC_NOT_COMPLETED - " . $referenceNumber, ['reason' => "KYC bonus skipped as KYC is not completed"]);
                // }

                // /**
                //  * EO: RAF Module End
                //  */

                $data = [
                    'userPurchasedAmount' => $transaction->PAYMENT_TRANSACTION_AMOUNT,
                    'userPurchasedDate' => $transaction->PAYMENT_TRANSACTION_CREATED_ON,
                    'userPurchasedInRefNo' => $transaction->INTERNAL_REFERENCE_NO,
                    'userPurchasedPromoCode' => $transaction->PROMO_CODE ?? "",
                    'userTournamentName' => $userTournamentSesName ?? "",
                    'userTournamentDate' => $userTournamentSesDate ?? "",
                    'userBonusAmount' => $pPromoValue ?? "",
                    'userUnclaimedBonusAmount' => $uBonusValue ?? ""
                ];

                $action = "validate_PromoCode_And_Payment_Process_success - " . $referenceNumber;

                $payment_process_data = [
                    'api_response' => $request->all(),
                    'api_data' => $data,
                    'updatePaymentTransactionSuccess' => $transaction ?? "",
                    'MasterTransactionHistoryRespurchaseAmmount' => $MasterTransactionHistoryRespurchaseAmmount ?? "",
                    'updateWithdrawalCriteria' => $updateWithdrawalCriteria ?? "",
                    'updatePlayerLedger' => $updatePlayerLedger ?? "",
                    'promo_code' => $transaction->PROMO_CODE ?? "",
                    'checkTourTicketExist' => $ticketCount ?? "",
                    'ticketGiveFirstProcedure' => $ticketGive ?? "ticketGiveFirstProcedure not execute",
                    'getSecondProcResult' => $getProcResult ?? "getSecondProcResult not execute",
                    'rafRes' => $rafRes ?? "",
                    'campaignTypeID' => $campaignTypeID ?? "",
                    'master_Trans_P_PROMO_CAP_VALUE' => $master_Trans_P_PROMO_CAP_VALUE ?? "",
                    'master_Trans_P_BONUS_CAP_VALUE' => $master_Trans_P_BONUS_CAP_VALUE ?? "",
                    'master_Trans_S_PROMO_CHIP_VALUE' => $master_Trans_S_PROMO_CHIP_VALUE ?? "",
                    'master_Trans_PROMO_VALUE1' => $master_Trans_PROMO_VALUE1 ?? "",
                    'CampaignToUserRes' => $CampaignToUserRes ?? ""
                ];

                $this->userActivitiesTracking($transaction->USER_ID, $action, $payment_process_data);
                DB::commit();

                /**
                 * ---------------------------------------------------------------------
                 * Fire Payment Tracking Event
                 * ----------------------------------------------------------------------
                 */
                $this->firePaymentTrackingEvents($transaction->USER_ID, $transaction->PAYMENT_TRANSACTION_AMOUNT, $transaction->INTERNAL_REFERENCE_NO, $transaction->PROMO_CODE, $paymentCount);


                /*
                    * Task: CB-1232 - Remove putting data on redis from TXN, MISC, and New BO
                    * Author: Gagandeep Garg
                    * Date: 06 Sept 2022    
                */
                /*
                try {
                    
                    if (!empty($applyPromoResponse['tournamentIDs'])) {
                        $tournamentUserTicket = TournamentUserTicket::select('TOURNAMENT_ID', 'INTERNAL_REFERENCE_NO')->where('USER_ID', $transaction->USER_ID)->whereIn('TOURNAMENT_ID', $applyPromoResponse['tournamentIDs'])->get();
                        foreach ($tournamentUserTicket as $record) {
                            $jsonDataEncoded = array(
                                'tournamentId' => $record->TOURNAMENT_ID,
                                'userId' => $transaction->USER_ID,
                                'referenceNo' => $record->INTERNAL_REFERENCE_NO,
                                'ticketSource' => 'Deposit_Code',
                                'ticketReason' => $transaction->PROMO_CODE,
                                'auto_register' => true
                            );

                              
                            //Publish to redis
                             \RedisManager::publish('BO', json_encode(['type' => 'tournamentAutoRegister', 'data' => $jsonDataEncoded]));

                            //Insert into user activities tracking
                            $this->userActivitiesTracking($transaction->USER_ID, 'TOURNAMENT_AUTO_REGISTER_REDIS_PUBLISH - ' . $transaction->INTERNAL_REFERENCE_NO, $jsonDataEncoded);
                        }
                    }
                } catch (\Exception $e) {
                    $this->userActivitiesTracking($transaction->USER_ID, 'REDIS_TOURNAMENT_PUBLISH_EXCEPTION', $e->getMessage());
                    Log::error($e);
                }
                */

                try {
                    $bonusAmount = NULL;
                    $eventType = NULL;
                    $expiryDate = NULL;
                    if (!empty($applyPromoResponse)) {
                        if (!empty($applyPromoResponse['bonusType'])) {
                            $eventType = $applyPromoResponse['bonusType'];
                        }
                        if (!empty($applyPromoResponse['bonusAmount'])) {
                            $bonusAmount = $applyPromoResponse['bonusAmount'];
                        }
                        if (!empty($applyPromoResponse['expiryDate'])) {
                            $expiryDate = $applyPromoResponse['expiryDate'];
                        }
                    }

                    $userSuccessfulDepositCount = (int)Redis::get("successful-deposit-count-$transaction->USER_ID");

                    if (empty($userSuccessfulDepositCount)) {
                        $userSuccessfulDepositCount = MasterTransactionHistory::whereIn('TRANSACTION_TYPE_ID', [8, 61, 62, 83])
                            ->whereIn('TRANSACTION_STATUS_ID', [103, 107, 125, 202])
                            ->where('USER_ID', $transaction->USER_ID)
                            ->count();
                        Redis::set("successful-deposit-count-$transaction->USER_ID", $userSuccessfulDepositCount);
                    } else {
                        Redis::incr("successful-deposit-count-$transaction->USER_ID");
                        $userSuccessfulDepositCount += 1;
                    }

                    $data = [
                        "type" => "event",
                        "type_name" => "Deposit",
                        "data" => [
                            "identity" => $transaction->USER_ID,
                            "Success" => true,
                            "Pending" => false,
                            "Code" => $transaction->PROMO_CODE,
                            "Amount" => $transaction->PAYMENT_TRANSACTION_AMOUNT,
                            "Gateway_Fee" => $transaction->DISC_GATEWAY_FEE,
                            "Amount_Deducted" => $transaction->TOTAL_AMOUNT,
                            "Bonus" => $bonusAmount,
                            "Bonus Type" => $eventType,
                            "Payment Method" => $refNos['paymentMethod'],
                            "FTD" => $paymentCount == 1 ? true : false,
                            "MILESTONE_ID" => $milestoneId,
                            "REFERENCE_NUMBER" => $referenceNumber,
                            "EVENT_TIME" => Carbon::now()->format("Y-m-d H:i:s"),
                            "Number Of Successfull Deposit" => $userSuccessfulDepositCount,
                            "app_type" => $transaction->APP_TYPE
                        ]
                    ];
                    $resp = $this->putOnKinesis($data);
                    $this->userActivitiesTracking($transaction->USER_ID, 'KINESIS_DEPOSIT_SUCCESSFUL', [
                        $data,
                        'resp' => $resp
                    ]);

                    /**
                     * Add User To Redis Deposit User SET 
                     */
                    Redis::sadd("deposit-user-list", $transaction->USER_ID);

                    if($paymentCount == 1)
                    {
                        Redis::sadd('single-deposit-user-list', $transaction->USER_ID);
                    } else {
                        Redis::srem('single-deposit-user-list', $transaction->USER_ID);
                    }


                    if (!empty($applyPromoResponse)) {
                        if (!empty($applyPromoResponse['bonusType'])) {
                            $eventType = $applyPromoResponse['bonusType'];
                        }
                        if (!empty($applyPromoResponse['bonusAmount'])) {
                            $bonusAmount = $applyPromoResponse['bonusAmount'];
                        }
                        $data = [
                            "type" => "event",
                            "type_name" => "Bonus",
                            "data" => [
                                "identity" => $transaction->USER_ID,
                                "Amount" => $bonusAmount,
                                "Type" => $eventType,
                                "Expiry Date" => $expiryDate,
                                "Source" => "Deposit Promo"
                            ]
                        ];
                        $resp = $this->putOnKinesis($data);
                        $this->userActivitiesTracking($transaction->USER_ID, 'KINESIS_DEPOSIT_BONUS_SUCCESSFUL', [
                            $data,
                            'resp' => $resp
                        ]);
                    }
                    // if ($isRAFReleasable > 0) {
                    //     $eventType = "";
                    //     if ($paymentCount == 1) {
                    //         $eventType = "RAF First Deposit Success";
                    //     } else {
                    //         $eventType = "RAF Subsequent Deposits Success";
                    //     }
                    //     $data = [
                    //         "type" => "event",
                    //         "type_name" => $eventType,
                    //         "data" => [
                    //             "identity" => $referrerDetails->REFER_USER_ID,
                    //             "Referred Username" => $user->USERNAME,
                    //             "Amount" => $transaction->PAYMENT_TRANSACTION_AMOUNT,
                    //             "Deposit count" => $paymentCount
                    //         ]
                    //     ];
                    //     $resp = $this->putOnKinesis($data);
                    //     $this->userActivitiesTracking($referrerDetails->REFER_USER_ID, 'KINESIS_RAF_BONUS_SUCCESSFUL - ' . $eventType, [
                    //         $data,
                    //         'resp' => $resp
                    //     ]);
                    // }
                } catch (\Exception $e) {
                    $this->userActivitiesTracking($transaction->USER_ID, 'KINESIS_KINESIS_DEPOSIT_FAILED', $e->getMessage());
                    Log::error($e);
                }
                // CB-821 (if - else logic only)
                $checkEmailId = EmailerPreventedEmail::where('EMAIL_ID', $user->EMAIL_ID)->get();
                if (count($checkEmailId) > 0) {
                    Log::info("Emails not sent as email id of user " . $user->EMAIL_ID . " is in bounce restricted email list. Transaction ID: " . $referenceNumber);
                } else {
                    //send email
                    if ($user->EMAIL_VERIFY == 1) {
                        $amountToBeCredited = $rcb = $sb = 0;
                        $commonDiv = '<div
                            style="border-radius: 8px; background: #f5f5f5; padding: 30px"
                        >';
                        $commonDiv = $commonDiv .'<p
                            style="
                                color: #595959;
                                font-size: 16px;
                                font-weight: 700;
                                margin: 0px;
                                padding-bottom: 10px;
                            "
                            >
                            Transaction ID:
                            <span
                                style="color: #595959; font-size: 15px; font-weight: 400"
                                >'.$transaction->INTERNAL_REFERENCE_NO.'</span
                            >
                            </p>';
                            if(!empty($transaction->PROMO_CODE)){
                                $commonDiv = $commonDiv . '<p
                                style="
                                    color: #595959;
                                    font-size: 16px;
                                    font-weight: 700;
                                    margin: 0px;
                                    padding-bottom: 10px;
                                "
                                >
                                Offer Code:
                                <span
                                    style="color: #595959; font-size: 15px; font-weight: 400"
                                >
                                    '.$transaction->PROMO_CODE.'</span
                                >
                                </p>';
                            }
                            
                            $commonDiv = $commonDiv . '<p
                            style="
                                color: #595959;
                                font-size: 16px;
                                font-weight: 700;
                                margin: 0px;
                                padding-bottom: 10px;
                            "
                            >
                            Deposit Requested (incl. GST): ₹<span
                                style="color: #595959; font-size: 15px; font-weight: 400"
                            >
                                '.number_format($transaction->PAYMENT_TRANSACTION_AMOUNT, 2, '.', '').'</span
                            >
                            </p>';
                            if(!empty($transaction->PG_REFERENCE_NOS) && isset($transaction->PG_REFERENCE_NOS['gst']) && isset($transaction->PG_REFERENCE_NOS['gst']['amount_to_be_refunded']) && $transaction->PG_REFERENCE_NOS['gst']['amount_to_be_refunded'] != $transaction->PG_REFERENCE_NOS['gst']['amount']){
                                $commonDiv = $commonDiv . '<p
                                style="
                                    color: #595959;
                                    font-size: 16px;
                                    font-weight: 700;
                                    margin: 0px;
                                    padding-bottom: 10px;
                                "
                                >
                                GST applied on deposit amount @'.$transaction->PG_REFERENCE_NOS['gst']['rate'].'%: ₹<span
                                    style="color: #595959; font-size: 15px; font-weight: 400"
                                >
                                    '.number_format($transaction->PG_REFERENCE_NOS['gst']['amount'], 2, '.', '').'</span
                                >
                                </p>';
                            } 
                            if(!empty($transaction->PG_REFERENCE_NOS) && isset($transaction->PG_REFERENCE_NOS['gst']) && isset($transaction->PG_REFERENCE_NOS['gst']['amount_to_be_refunded']) && $transaction->PG_REFERENCE_NOS['gst']['amount_to_be_refunded'] > 0){
                                $commonDiv = $commonDiv . '<p
                                style="
                                    color: #595959;
                                    font-size: 16px;
                                    font-weight: 700;
                                    margin: 0px;
                                    padding-bottom: 10px;
                                "
                                >
                                GST Saved: ₹<span
                                    style="color: #595959; font-size: 15px; font-weight: 400"
                                >
                                    '.number_format($transaction->PG_REFERENCE_NOS['gst']['amount_to_be_refunded'], 2, '.', '').'</span
                                >
                                </p>';
                            }
                            
                            if(!empty($transaction->PG_REFERENCE_NOS) && isset($transaction->PG_REFERENCE_NOS['gateway_fee_component']) && isset($transaction->PG_REFERENCE_NOS['gateway_fee_component']['disc_deposit_fee'])){

                                $processingFee = '';
                                if($transaction->PG_REFERENCE_NOS['gateway_fee_component']['disc_deposit_fee'] == 0 && $transaction->PG_REFERENCE_NOS['gateway_fee_component']['deposit_fee'] > 0){
                                    $processingFee = $processingFee.'₹ <span style="color: #595959; font-size: 15px; font-weight: 400">';
                                    $processingFee = $processingFee.'<s>'.number_format($transaction->PG_REFERENCE_NOS['gateway_fee_component']['deposit_fee'], 2, '.', '').'</s>'; 
                                    $processingFee = $processingFee.'</span>';
                                } else if($transaction->PG_REFERENCE_NOS['gateway_fee_component']['disc_deposit_fee'] != $transaction->PG_REFERENCE_NOS['gateway_fee_component']['deposit_fee']){
                                    $processingFee = $processingFee.'₹ <span style="color: #595959; font-size: 15px; font-weight: 400">';
                                    $processingFee = $processingFee.'<s>'.number_format($transaction->PG_REFERENCE_NOS['gateway_fee_component']['deposit_fee'], 2, '.', '').'</s>'; 
                                    $processingFee = $processingFee.'</span>';
                                    $processingFee = $processingFee.' ₹ <span style="color: #595959; font-size: 15px; font-weight: 400">';
                                    $processingFee = $processingFee.number_format($transaction->PG_REFERENCE_NOS['gateway_fee_component']['disc_deposit_fee'], 2, '.', ''); 
                                    $processingFee = $processingFee.'</span>';
                                } else {
                                    $processingFee = $processingFee.'₹ <span style="color: #595959; font-size: 15px; font-weight: 400">';
                                    $processingFee = $processingFee.number_format($transaction->PG_REFERENCE_NOS['gateway_fee_component']['disc_deposit_fee'], 2, '.', ''); 
                                    $processingFee = $processingFee.'</span>';
                                }

                                $commonDiv = $commonDiv . '<p
                                style="
                                    color: #595959;
                                    font-size: 16px;
                                    font-weight: 700;
                                    margin: 0px;
                                    padding-bottom: 10px;
                                "
                                >
                                Processing fee applied on deposit: 
                                    '.$processingFee.'
                                </p>';
                            }
                            if(!empty($transaction->PROMO_CODE)){
                                $rcbTxn = MasterTransactionHistory::where('INTERNAL_REFERENCE_NO', $transaction->INTERNAL_REFERENCE_NO)->where('BALANCE_TYPE_ID', 2)->first();
                                if(!empty($rcbTxn)){
                                    $rcb = $rcbTxn->TRANSACTION_AMOUNT;
                                    $commonDiv = $commonDiv . '<p
                                    style="
                                        color: #595959;
                                        font-size: 16px;
                                        font-weight: 700;
                                        margin: 0px;
                                        padding-bottom: 10px;
                                    "
                                    >
                                    Real Cash Bonus (RCB): ₹<span
                                        style="color: #595959; font-size: 15px; font-weight: 400"
                                    >
                                        '.number_format($rcb, 2, '.', '').'</span
                                    >
                                    </p>';
                                }
                                
                                $sbTxn = MasterTransactionHistoryFppBonus::where('INTERNAL_REFERENCE_NO', $transaction->INTERNAL_REFERENCE_NO)->where('BALANCE_TYPE_ID', 2)->first();
                                if(!empty($sbTxn)){
                                    $sb = $sbTxn->TRANSACTION_AMOUNT;
                                    $commonDiv = $commonDiv . '<p
                                    style="
                                        color: #595959;
                                        font-size: 16px;
                                        font-weight: 700;
                                        margin: 0px;
                                        padding-bottom: 10px;
                                    "
                                    >
                                    Special Bonus (SB): ₹<span
                                        style="color: #595959; font-size: 15px; font-weight: 400"
                                    >
                                        '.number_format($sb, 2, '.', '').'</span
                                    >
                                    </p>';
                                }
                            }
                            if(!empty($transaction->PG_REFERENCE_NOS) && isset($transaction->PG_REFERENCE_NOS['gst']) && isset($transaction->PG_REFERENCE_NOS['gst']['amount_to_be_refunded']) && isset($transaction->PG_REFERENCE_NOS['amount_to_be_credited'])){
                                $amountToBeCredited = $transaction->PG_REFERENCE_NOS['amount_to_be_credited']+$transaction->PG_REFERENCE_NOS['gst']['amount_to_be_refunded']+$rcb+$sb;
                                $rcbSb = ($rcb > 0 && $sb > 0) ? 'with (RCB + SB)' : (($rcb > 0) ? 'with RCB' : (($sb > 0) ? 'with SB' : ''));
                                $commonDiv = $commonDiv . '<p
                                style="
                                    color: #595959;
                                    font-size: 16px;
                                    font-weight: 700;
                                    margin: 0px;
                                    padding-bottom: 10px;
                                "
                                >
                                Amount credited to your wallet '.$rcbSb.': ₹<span
                                    style="color: #595959; font-size: 15px; font-weight: 400"
                                >
                                    '.number_format($amountToBeCredited, 2, '.', '').'</span
                                >
                                </p>';
                            }
                            
                            if(!empty($transaction->PG_REFERENCE_NOS) && isset($transaction->PG_REFERENCE_NOS['gateway_fee_component']) && isset($transaction->PG_REFERENCE_NOS['gateway_fee_component']['disc_deposit_fee'])){
                                $commonDiv = $commonDiv . '<p
                                style="
                                    color: #595959;
                                    font-size: 16px;
                                    font-weight: 700;
                                    margin: 0px;
                                    padding-bottom: 10px;
                                "
                                >
                                Net paid: ₹<span
                                    style="color: #595959; font-size: 15px; font-weight: 400"
                                >
                                    '.number_format($transaction->PG_REFERENCE_NOS['gateway_fee_component']['total_amount_with_disc_fee_gst'], 2, '.', '').'</span
                                >
                                </p>';
                            }
                            $commonDiv = $commonDiv. '</div>';
                        $this->sendMail([
                            "email" => $user->EMAIL_ID,
                            "username" => $user->USERNAME,
                            "emailkey" => 'deposit_successful',
                            "userId" => $transaction->USER_ID,
                            "internalReferenceNumber" => $referenceNumber,
                            "amount" => number_format($amountToBeCredited, 2, '.', ''),
                            "transactionDate" => $transaction->PAYMENT_TRANSACTION_CREATED_ON,
                            "paymentId" => $pg_transaction_id,
                            "commonDiv" => $commonDiv
                        ]);
                    }
                    if ($sendTournamentTicketEmail && $user->EMAIL_VERIFY == 1) {
                        $this->sendMail([
                            "email" => $user->EMAIL_ID,
                            "username" => $user->USERNAME,
                            "emailkey" => 'tournament_registration_on_deposit_success',
                            "userId" => $transaction->USER_ID,
                            "tournamentName" => $tournamentTicketvariables['tournamentName'],
                            "depositCode" => $tournamentTicketvariables['depositCode'],
                        ]);
                    }
                    if ($sendPromoBonusEmail && $user->EMAIL_VERIFY == 1) {
                        $this->sendMail([
                            "email" => $user->EMAIL_ID,
                            "username" => $user->USERNAME,
                            "emailkey" => 'promo_bonus_received_on_deposit_success',
                            "userId" => $transaction->USER_ID,
                            "bonusAmount" => $promoBonusVariables['bonusAmount'],
                            "depositCode" => $transaction->PROMO_CODE,
                        ]);
                    }
                    if ($sendRAFEmail && $user->EMAIL_VERIFY == 1) {
                        $refferingUserDetail['allDetails'] = $this->getUserDetails($refferingUserDetail['USER_ID']);
                        $this->sendMail([
                            "email" => $refferingUserDetail['allDetails']->EMAIL_ID,
                            "username" => $refferingUserDetail['allDetails']->USERNAME,
                            "emailkey" => 'refer_a_friend_bonus',
                            "userId" => $refferingUserDetail['USER_ID'],
                            "referrer_username" => $refferingUserDetail['allDetails']->USERNAME,
                            "full_name" => $user->FIRSTNAME . ' ' . $user->LASTNAME,
                            "refferal_username" => $user->USERNAME,
                            "chips" => $refferingUserDetail['calculatedBonusAmount'],
                            "credited_date" => $transaction->PAYMENT_TRANSACTION_CREATED_ON
                        ]);
                    }
                }

                $pushEventToFb = false;

                if (strtoupper(config('app.env')) == 'PRODUCTION' || strtoupper(config('app.env')) == 'PROD') {
                    $pushEventToFb = true;
                } else if (!empty(env('USER_IDS_FOR_FB_QA'))) {
                    $envUserNames = explode(',', env('USER_IDS_FOR_FB_QA'));
                    if (in_array($user->USERNAME, $envUserNames)) {
                        $pushEventToFb = true;
                    }
                } else {
                    $pushEventToFb = false;
                }

                if ($pushEventToFb) {
                    $this->userActivitiesTracking($transaction->USER_ID, 'Initiating FB Deposit with amount: ' . $transaction->PAYMENT_TRANSACTION_AMOUNT, []);

                    $accessToken = config("rummy_config.fbPixelEvents.accessToken");

                    if (!empty($accessToken)) {

                        $email = 99 . $transaction->USER_ID . '@gmail.com';
                        $phone = 99 . rand(10000000, 99999999);

                        $requestedVia = ActionSource::OTHER;
                        if (!empty($transaction->PG_REFERENCE_NOS['requestedVia'])) {
                            $requestedVia = $transaction->PG_REFERENCE_NOS['requestedVia'];
                            if ($requestedVia == 'web') {
                                $requestedVia = ActionSource::WEBSITE;
                            } else if ($requestedVia == 'app') {
                                $requestedVia = ActionSource::SYSTEM_GENERATED;
                            } else {
                                $requestedVia = ActionSource::OTHER;
                            }
                        }

                        $access_token = config("rummy_config.fbPixelEvents.accessToken");
                        $pixel_id = config("rummy_config.fbPixelEvents.fbPixelId");

                        $api = Api::init(null, null, $access_token);
                        $api->setLogger(new CurlLogger());

                        $user_data = (new UserData())
                            ->setEmails(array($user->EMAIL_ID))
                            ->setPhones(array($user->CONTACT))
                            ->setFirstNames(array($user->FIRSTNAME))
                            ->setLastNames(array($user->LASTNAME))
                            ->setDatesOfBirth(array(str_replace('-', '', $user->DATE_OF_BIRTH)))
                            ->setGenders(array($user->GENDER))
                            ->setCities(array($user->CITY))
                            ->setStates(array($user->STATE))
                            ->setZipCodes(array($user->PINCODE))
                            ->setCountryCodes(array(config("rummy_config.fbPixelEvents.fbCountryCode")))
                            ->setExternalIds(array($transaction->INTERNAL_REFERENCE_NO))
                            ->setSubscriptionId($transaction->INTERNAL_REFERENCE_NO)
                            ->setClientIpAddress(config("rummy_config.fbPixelEvents.serverIp"))
                            ->setClientUserAgent($_SERVER['HTTP_USER_AGENT']);

                        $custom_data = (new CustomData())
                            ->setCurrency(config("rummy_config.transactionVariables.depositCurrency"))
                            ->setValue($transaction->PAYMENT_TRANSACTION_AMOUNT);

                        $eventName = $paymentCount == 1 ? config('rummy_config.fbPixelEvents.fbFTDEventName') : config('rummy_config.fbPixelEvents.fbDepositEventName');

                        $event = (new Event())
                            ->setEventName($eventName)
                            ->setEventTime(time())
                            ->setEventSourceUrl(config("rummy_config.fbPixelEvents.eventSourceUrl"))
                            ->setUserData($user_data)
                            ->setCustomData($custom_data)
                            ->setActionSource($requestedVia);
                        $events = array();
                        if ($paymentCount == 1) :
                            $event2 = (new Event())
                                ->setEventName("First Time Deposit")
                                ->setEventTime(time())
                                ->setEventSourceUrl(config("rummy_config.fbPixelEvents.eventSourceUrl"))
                                ->setUserData($user_data)
                                ->setCustomData($custom_data)
                                ->setActionSource($requestedVia);
                            array_push($events, $event2);
                        endif;

                        array_push($events, $event);


                        $request = (new EventRequest($pixel_id))->setEvents($events);
                        $resp = $request->execute();
                        $data = (array) $resp;

                        $this->userActivitiesTracking($transaction->USER_ID, 'FB Pixel - Deposit event response', $data);
                    }
                }
                return 'ok';
            } catch (\Exception $e) {
                DB::rollback();
                //dd($e);
                \Log::error($e);
                $this->userActivitiesTracking($transaction->USER_ID, 'SOMETHING_WENT_WRONG_IN_PAYMENT_AND_PROMO_CODE_PROCESS_NOT_RECEIVED_OK_DB_TRANSACTION_ROLLBACK', ['error' => $e]);
                $txnStatus = PaymentTransaction::where('INTERNAL_REFERENCE_NO', $transaction->INTERNAL_REFERENCE_NO)->first();
                $txnStatus->PAYMENT_TRANSACTION_STATUS = $transaction->PAYMENT_TRANSACTION_STATUS;
                $txnStatus->save();
                return $this->errorResponse($apiCode, array([
                    'code' => $errorCode,
                    'message' => 'Someting went wrong in payment and promocode process not received OK.'
                ]));
            }
        } else {
            return "recently_updated";
        }
    }
}
