<?php

namespace App\Http\Controllers\Payment\AddMoney;

use App\Http\Controllers\Payment\AddMoney\AddMoneyBaseController;
use App\Models\CampaignTournamentMapping;
use App\Models\GlobalConfig;
use App\Models\PaymentTransaction;
use App\Models\PromoCampaign;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use App\Models\User;
use App\Models\UserKycDetail;
use Carbon\Carbon;

/**
 * Add Money Controller Contains all
 * the Add money related Methods
 *
 * @category    Payments
 * @package     Deposit
 * @copyright   RummyBaazi
 * <AUTHOR>
 */
class AddMoneyController extends AddMoneyBaseController
{

    const APPS = [18 => 'CARDBAAZI_ONLINE', 19 => 'RUMMYBAAZI_ONLINE'];

    /**
     * Payment -> Add Money Module: Validation of amount and deposit code before proceeding to add money.
     * API End Point :{base_url}/user/payment/check
     * <AUTHOR> <<EMAIL>>
     */
    public function check(Request $request)
    {
        $depositUnderMaintenance = GlobalConfig::where('CONFIG_KEY', 'deposit_under_maintenance')->where('CONFIG_VALUE', 1)->exists();
        if ($depositUnderMaintenance) {
            $this->errorBag[] = [
                'code' => 503001,
                'message' => 'Deposit under maintenance'
            ];
            return $this->errorResponse(1, $this->errorBag, 503);
        }
        $minDepositAmount = \DB::table('deposit_amount_suggestion_configuration')->where('ID', 1)->value('AMOUNT');
        $transactionVariablesArr = config('rummy_config.transactionVariables');
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:' . $minDepositAmount,
            'deposit_code' => 'nullable|regex:/^[a-zA-Z0-9_\-]*$/|max:30',
        ], [
            'amount.required' => 'Enter amount',
            'amount.numeric' => 'Amount should contain only numbers',
            'amount.min' => 'Minimum deposit amount allowed is ' . $minDepositAmount,
            'deposit_code.regex' => 'Only alphabets, numbers, underscores, and hypen allowed in promo code',
            'deposit_code.max' => 'Maximum 30 characters allowed in promo code'
        ]);
        if ($validator->fails()) {
            $errorCode = ["amount" => 422028, "deposit_code" => 422029];
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $this->errorBag[] = [
                    'code' => $errorCode[$key],
                    'message' => $error[0]
                ];
            }
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        /* validate code before proceeding futher */
        $userId = getUserId();

        $appTypeCode = $request->app_type;
        $appName = self::APPS[$appTypeCode] ?? 'DEFAULT';

        /*Banned state check*/
        if ($bannedState = $this->isUserInBannedState($userId)) {
            $this->userActivitiesTracking($userId, "INTIATED_CHECKING_DEPOSIT_CODE", ["error" => 'User is from banned state']);
            return $this->errorResponse(1, [[
                'code' => 422104,
                'message' => $bannedState
            ]]);
        }
        /*Banned state check end*/
        $this->userActivitiesTracking($userId, "INTIATED_CHECKING_DEPOSIT_CODE", ["request" => $request->all()]);
        if (!empty($request->deposit_code)) {
            $validateCode = $this->validateCode($request->deposit_code, $request->amount, $userId);
            if ($validateCode) {
                if ($validateCode != "Valid") {
                    $this->userActivitiesTracking($userId, "DEPOSIT_CODE_UNPROCESSABLE", ["request" => $request->all(), 'errorResponse' => $validateCode]);
                    return $validateCode;
                }
            } else {
                $this->userActivitiesTracking($userId, "DEPOSIT_CODE_UNPROCESSABLE", ["request" => $request->all()]);
                $this->errorBag[] = [
                    'code' => 422029,
                    'message' => 'Invalid Code'
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            }
        }

        /*  Pan and address proof required as per lifetime deposit limit */
        $availableDocuments = UserKycDetail::where('USER_ID', $userId)->where('DOCUMENT_STATUS', 1)->pluck('DOCUMENT_TYPE')->toArray();

        if (count($availableDocuments) < config('rummy_config.DOC_PROOF.' . $appName . '.DOC_COUNT')) {

            $requiredDocsWithName = config('rummy_config.REQUIRED_DOCUMENT_TYPES');

            $requiredDocs = array_keys($requiredDocsWithName);

            $diff = array_diff($requiredDocs, $availableDocuments);

            $requiredDocsWithName = array_filter($requiredDocsWithName, function ($key) use ($diff) {
                if (in_array($key, $diff)) return true;
            }, ARRAY_FILTER_USE_KEY);

            $lifeTimeDeposit =  $this->getUserDepositSum($userId)->DEPOSITED_AMOUNT;

            if (($lifeTimeDeposit + $request->amount) > config('rummy_config.DOC_PROOF.' . $appName . '.LIFE_TIME_DEPOSIT')) {
                $this->errorBag[] = [
                    'code' => 422154,
                    'message' => 'Kindly provide ' . implode('/', $requiredDocsWithName) . ' for further deposit.',
                    'required_doc' => implode('/', $requiredDocsWithName)
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            }
        }

        /* Validate amount as per deposit limit available */
        $validatAmountAndDepositLimit = $this->validateAmountAndDepositLimit($userId, $request->amount);
        if ($validatAmountAndDepositLimit) {
            if ($validatAmountAndDepositLimit != "Valid") {
                $this->userActivitiesTracking($userId, "DEPOSIT_AMOUNT_AND_DEPOSIT_LIMIT_VALIDATION_FAILED", ["request" => $request->all(), 'errorResponse' => $validatAmountAndDepositLimit]);
                return $validatAmountAndDepositLimit;
            }
        } else {
            $this->userActivitiesTracking($userId, "DEPOSIT_AMOUNT_AND_DEPOSIT_LIMIT_VALIDATION_FAILED", ["request" => $request->all()]);
            $this->errorBag[] = [
                'code' => 422028,
                'message' => 'Amount validation error'
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        $userSettings = $this->getUserSettings($userId);
        $depositGstFlag = $this->checkGstFlagEnabled($userId);
        if($depositGstFlag){
            $depositGSTcomponent = $this->calculateGstOnDepositAmount($request->amount);
            $amountToBePaidAfterGST = round($request->amount - $depositGSTcomponent, 2);
            $gstComp = [
                        'rate' => (int) config('rummy_config.transactionVariables.gstRateOnDeposit'),
                        'refund_rate' => (int) $refundRate = $userSettings->IS_DEPOSIT_GST_WAIVED_OFF ? config('rummy_config.transactionVariables.gstRefundRateOnDeposit') : 0,
                        'amount' => $depositGSTcomponent,
                        'amount_to_be_refunded' => round($depositGSTcomponent/100*$refundRate, 2),
                        'gst_waived_off' => $userSettings->IS_DEPOSIT_GST_WAIVED_OFF ? true : false,
                        'refund_balance_type' => (int) $this->getGstBonusBalanceType()
                        ];  
        }else{
            $amountToBePaidAfterGST = (int) $request->amount;
            $gstComp = [
                        'rate' => (int) config('rummy_config.transactionVariables.gstRateOnDeposit'),
                        'refund_rate' => (int) $refundRate = config('rummy_config.transactionVariables.gstRefundRateOnDeposit'),
                        'amount' => 0,
                        'amount_to_be_refunded' => 0,
                        'gst_waived_off' => true,
                        'refund_balance_type' => (int) $this->getGstBonusBalanceType()
                        ]; 
        }

        /*Check if JusPay is enabled to proceed with checking/creating of JusPay Customer ID*/
        $enabledGateways = $this->enabledPaymentGatewaysList();
        $jusPayCheck = collect($enabledGateways)->pluck("gateway_name")->toArray();
        if (in_array(config('rummy_config.juspay_code'), $jusPayCheck)) {
            /* Validate if JusPay customer id exists in out DB and create if doesn't */
            $user = $this->getUserDetails($userId);
            $jusPayCheck = $this->checkJusPayCustomerDetails($userId, $user);
            if ($jusPayCheck != "Valid") {
                $jusPayUpdate = $this->retrieveAndUpdateJusPayCustomerDetail($userId, $user);
                if (!$jusPayUpdate) {
                    $this->userActivitiesTracking($userId, "ERROR_IN_JUSPAY_ID", ["request" => $request->all()]);
                    $this->errorBag[] = [
                        'code' => 422091,
                        'message' => 'Error in JusPay ID'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                }
            }
            $user = $this->getUserDetails($userId);
            $this->userActivitiesTracking($userId, "PAYMENT_DEPOSIT_CHECK_API_SUCCESS", ["request" => $request->all()]);
            $promoDesc = "";
            $balanceType = "";
            $balanceValue = "";
            $totalWithIncludedBonus = 0;
            $totalSB = 0;
            $totalRCB = 0;
            $tickets = [];
            $includedBonus[] =  [
                'key' => "Deposit Amount :",
                'value' => $totalWithIncludedBonus = $gstComp['gst_waived_off'] ? $request->amount - ((isset($gstComp['amount']) && isset($gstComp['amount_to_be_refunded'])) ? ($gstComp['amount']- $gstComp['amount_to_be_refunded']) : 0) : $request->amount - $gstComp['amount']
            ];
            if (!empty($request->deposit_code)) {
                $promoCampaignDetails = PromoCampaign::query();
                $promoCampaignDetails->from(app(PromoCampaign::class)->getTable() . " as pc");
                $promoCampaignDetails->leftJoin('promo_rule as pr', 'pr.PROMO_CAMPAIGN_ID', '=', 'pc.PROMO_CAMPAIGN_ID');
                $promoCampaignDetails->where('pc.PROMO_CAMPAIGN_CODE', $request->deposit_code);
                $promoCampaignDetails->orderBy('START_DATE_TIME', 'DESC');
                $promoCampaignDetails->limit(1);
                $promoCampaignDetails = $promoCampaignDetails->first();
                if(!$promoCampaignDetails->P_TOURNAMENT_TICKETS){
                    $promoValaueAndType = $this->getPromoValueAndTypeByPromoCampaignDetail($promoCampaignDetails, $request->amount, $userId);
                    $promoDesc = "You will get ₹" . $promoValaueAndType['value'] . " extra " . $promoValaueAndType['type'] . " !!";
                    $balanceType = $promoValaueAndType['type'] ;
                    $balanceValue = $promoValaueAndType['value'] ;
                }
                else{
                    $promoDesc = "You will get a tournament ticket!!";
                    $campaignTournament = CampaignTournamentMapping::from(app(CampaignTournamentMapping::class)->getTable() . " as ctm")
                                            ->leftJoin('tournaments_rein_rummy as trr', 'trr.TOURNAMENT_ID', 'ctm.TOURNAMENT_ID')
                                            ->where('PROMO_CAMPAIGN_ID', $promoCampaignDetails->PROMO_CAMPAIGN_ID)
                                            ->select('trr.TOURNAMENT_ID', \DB::raw("CONCAT(TOURNAMENT_DESC,'(',START_DATETIME,')') as TOURNAMENT_NAME"))
                                            ->pluck('TOURNAMENT_NAME')->toArray();              
                    // $promoValaueAndType['value'] = !empty($campaignTournament) ? $campaignTournament : "";
                    $promoValaueAndType['type'] = 'Tournament Ticket';
                    $balanceType = '';
                    $balanceValue = '';
                }

                if(!empty($promoValaueAndType) && $promoValaueAndType['type'] == 'Special Bonus, Real Cash Bonus') {
                    $promoDesc = "A total bonus of ₹" . $promoValaueAndType['value'] . " will be credited to your account.";
                }
                
                if($promoValaueAndType['type'] == 'SB, RCB'){
                    $includedBonus[] = [
                        'key' => "Real Cash Bonus (RCB) :",
                        'value' => $totalRCB = $promoValaueAndType['rcb']
                    ];
                    $includedBonus[] = [
                        'key' => "Special Bonus (SB) :",
                        'value' => $totalSB = $promoValaueAndType['sb']
                    ];
                } else if($promoValaueAndType['type'] == 'Special Bonus'){
                    $includedBonus[] = [
                        'key' => "Special Bonus (SB) :",
                        'value' => $totalSB = $promoValaueAndType['value']
                    ];
                } else if($promoValaueAndType['type'] == 'Real Cash Bonus'){
                    $includedBonus[] = [
                        'key' => "Real Cash Bonus (RCB) :",
                        'value' => $totalRCB = $promoValaueAndType['value']
                    ];
                } else if($promoValaueAndType['type'] == 'Tournament Ticket'){
                    foreach($campaignTournament as $tournament){
                        $tickets[] = [
                            'key' => "Tournament Ticket :",
                            'value' => $tournament
                        ];
                    }
                    
                }
            } else if(isset($request->offer_id) &&  !empty($request->offer_id)){
                $offerApplied = \DB::table('deposit_amount_suggestion_configuration')->select('ID', 'USER_TYPE', 'AMOUNT', 'AMOUNT_TYPE', 'REWARD_CONFIGURATION', 'DESCRIPTION')->where('ID', $request->offer_id)->first();
                $rewardConfig = json_decode($offerApplied->REWARD_CONFIGURATION);
                if($offerApplied->USER_TYPE == 1){
                    if(isset($rewardConfig->RCB) && !empty($rewardConfig->RCB)){
                        $calculatedRCB = round(($request->amount*$rewardConfig->RCB->value/100),2);
                        $includedBonus[] = [
                            'key' => "Real Cash Bonus (RCB) :",
                            'value' => $totalRCB = $calculatedRCB > $rewardConfig->RCB->cap ? $rewardConfig->RCB->cap : $calculatedRCB
                        ];
                    }
                    if(isset($rewardConfig->SB) && !empty($rewardConfig->SB)){
                        $calculatedSB = round(($request->amount*$rewardConfig->SB->value/100),2);
                        $includedBonus[] = [
                            'key' => "Special Bonus (SB) :",
                            'value' => $totalSB = $calculatedSB > $rewardConfig->SB->cap ? $rewardConfig->SB->cap : $calculatedSB
                        ];
                    }
                } else {
                    if(isset($rewardConfig->RCB) && !empty($rewardConfig->RCB)){
                        $calculatedRCB = round(($request->amount*$rewardConfig->RCB->value/100),2);
                        $includedBonus[] = [
                            'key' => "Real Cash Bonus (RCB) :",
                            'value' => $totalRCB = $calculatedRCB > $rewardConfig->RCB->cap ? $rewardConfig->RCB->cap : $calculatedRCB
                        ];
                    }
                    if(isset($rewardConfig->SB) && !empty($rewardConfig->SB)){
                        $calculatedSB = round(($request->amount*$rewardConfig->SB->value/100),2);
                        $includedBonus[] = [
                            'key' => "Special Bonus (SB) :",
                            'value' => $totalSB = $calculatedSB > $rewardConfig->SB->cap ? $rewardConfig->SB->cap : $calculatedSB
                        ];
                    }
                }
            }
            $totalWithIncludedBonus += $totalRCB + $totalSB;
            $gateway_fee = $this->getPaymentGatewayFeeStructure($userId, $request->amount, $gstComp, $includedBonus, $totalWithIncludedBonus);

            return $this->successResponse(1, ['juspay_cust_id' => $user['USER_OTHER_DETAILS']['jusPay_Cust_ID'] ?? '', 'promo_description' => $promoDesc, 'balance_type' => $balanceType, 'balance_value' => $balanceValue, 'gateway_fee' => $gateway_fee, 'amount_to_be_credited' => $amountToBePaidAfterGST,  'gst_applicable_on_deposit' => $depositGstFlag, "gst" => $gstComp, "tickets" => $tickets]);
        }
        return $this->successResponse(1, ['juspay_cust_id' => ""]);
    }

    /**
     * Payment -> Add Money Module for:
     * 1) Check if JusPay is enabled.
     * 2) If JusPay enabled, then fetch all available payment methods and saved cards for the user
     * 3) If JusPay is disabled then fetch list of enabled payment gateways and fetch all available payment methods if payu_seamless is enabled
     * API End Point :{base_url}/user/payment/gateway
     * <AUTHOR> Aroraa <<EMAIL>>
     */
    public function gateway(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'for' => 'sometimes|in:quick_payment'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(1, [
                [
                    'code' => 422056,
                    'message' => 'Invalid request data'
                ]
            ]);
        }

        $userId = getUserId();
        $userDetails = $this->getUserDetails($userId);
        $successBag = array();
        /* Fetch all enabled gateways from our DB */
        $enabledGateways = $this->enabledPaymentGatewaysList();
        if (!$enabledGateways) {
            return $this->errorResponse(1, [
                [
                    'code' => 422038,
                    'message' => 'No enabled payment method available'
                ]
            ]);
        }

        /**
         * Fetch last two payment methods used in deposit
         */
        $lastUsedPaymentMethods = $this->getLastUsedPaymentMethods($userId);

        $lastUsedPrimaryPaymentMethods = $lastUsedPaymentMethods[0] ?? [];
        $lastUsedSecondaryPaymentMethods = $lastUsedPaymentMethods[1] ?? [];

        $jusPayCheck = collect($enabledGateways)->pluck("gateway_name")->toArray();
        if (in_array(config('rummy_config.juspay_code'), $jusPayCheck)) {
            /* if JusPay is enabled from our backend */
            $index = array_search(config('rummy_config.juspay_code'), $jusPayCheck);
            $successBag['list'][$enabledGateways[$index]['id']]['code'] = $enabledGateways[$index]['gateway_name'];
            $successBag['list'][$enabledGateways[$index]['id']]['name'] = $enabledGateways[$index]['description'];
            $successBag['list'][$enabledGateways[$index]['id']]['logo'] = $enabledGateways[$index]['logo'];
            //$successBag['outage']['list'] = []; // yet to be configured. As per JUSPAY SPoC, this will be available in production only.
            /* fetch all enable payment methods available (from JusPay Server) */
            $paymentMethods = (array)$this->fetchJusPayEnabledPaymentOptions();

            $banks = [];
            $cards = [];
            $wallets = [];
            $outages = $paymentMethods['outage'] ?? [];
            $quickPayments = [];
            /* Prepare details to be returned in succes response */
            if (!empty($paymentMethods['payment_methods'])) {
                foreach ($paymentMethods['payment_methods'] as $paymentMethod) {
                    if ($paymentMethod->payment_method == 'MASTER') {
                        $modifiedMethod = "MASTERCARD";
                    } else {
                        $modifiedMethod = $paymentMethod->payment_method;
                    }
                    $index = array_search($paymentMethod->payment_method, array_column($outages, 'payment_method'));

                    if (!empty($lastUsedPrimaryPaymentMethods)) :
                        if (($paymentMethod->payment_method_type == 'CARD' && $modifiedMethod == $lastUsedPrimaryPaymentMethods['paymentMethod']) ||
                            ($paymentMethod->payment_method_type == ($lastUsedPrimaryPaymentMethods['paymentMethodType'] ?? '') &&
                                ($paymentMethod->payment_method == ($lastUsedPrimaryPaymentMethods['paymentMethod'])))
                        ) {
                            $quickPayments['primary'] = [
                                'name' => $paymentMethod->description,
                                'code' => $paymentMethod->payment_method,
                                'logo' => $paymentMethod->payment_method_type == 'CARD' ? config('rummy_config.cdn_url') . '/generic/icons/' . $paymentMethod->payment_method . ".svg" : 'URL_of_logo',
                                'transaction_success_rate' => ((!empty($index) || $index === 0) ?  $outages[$index]->status : "GOOD"),
                                'type' => $paymentMethod->payment_method_type == 'NB' ? 'netbanking' : $paymentMethod->payment_method_type,
                                'last_four_digits' => $paymentMethod->payment_method_type == 'CARD' ? ($lastUsedPrimaryPaymentMethods['cardDetails']['last_four_digits'] ?? '') : '',
                                'card_type' => $paymentMethod->payment_method_type == 'CARD' ? ($lastUsedPrimaryPaymentMethods['cardDetails']['card_type'] ?? '') : ''
                            ];
                        } else if ("UPI" == ($lastUsedPrimaryPaymentMethods['paymentMethodType'] ?? "") && isset($lastUsedPrimaryPaymentMethods["upiDetails"])) {
                            $quickPayments['primary'] = [
                                'name' => '',
                                'code' => $lastUsedPrimaryPaymentMethods["upiDetails"]["payer_app"] ?? "",
                                'logo' => 'URL_of_logo',
                                'transaction_success_rate' => "GOOD",
                                'type' => "UPI"
                            ];
                        }
                    endif;
                    if (!empty($lastUsedSecondaryPaymentMethods)) :
                        if (($paymentMethod->payment_method_type == 'CARD' && $modifiedMethod == $lastUsedSecondaryPaymentMethods['paymentMethod']) ||
                            ($paymentMethod->payment_method_type == ($lastUsedSecondaryPaymentMethods['paymentMethodType'] ?? '') &&
                                ($paymentMethod->payment_method == ($lastUsedSecondaryPaymentMethods['paymentMethod'])))
                        ) {
                            $quickPayments['secondary'] = [
                                'name' => $paymentMethod->description,
                                'code' => $paymentMethod->payment_method,
                                'logo' => $paymentMethod->payment_method_type == 'CARD' ? config('rummy_config.cdn_url') . '/generic/icons/' . $paymentMethod->payment_method . ".svg" : 'URL_of_logo',
                                'transaction_success_rate' => ((!empty($index) || $index === 0) ?  $outages[$index]->status : "GOOD"),
                                'type' => $paymentMethod->payment_method_type == 'NB' ? 'netbanking' : $paymentMethod->payment_method_type,
                                'last_four_digits' => $paymentMethod->payment_method_type == 'CARD' ? ($lastUsedSecondaryPaymentMethods['cardDetails']['last_four_digits'] ?? '') : '',
                                'card_type' => $paymentMethod->payment_method_type == 'CARD' ? ($lastUsedSecondaryPaymentMethods['cardDetails']['card_type'] ?? '') : ''
                            ];
                        } else if ("UPI" == ($lastUsedSecondaryPaymentMethods['paymentMethodType'] ?? "") && isset($lastUsedSecondaryPaymentMethods["upiDetails"])) {
                            $quickPayments['secondary'] = [
                                'name' => '',
                                'code' => $lastUsedSecondaryPaymentMethods["upiDetails"]["payer_app"] ?? "",
                                'logo' => 'URL_of_logo',
                                'transaction_success_rate' => "GOOD",
                                'type' => "UPI"
                            ];
                        }
                    endif;

                    switch ($paymentMethod->payment_method_type) {
                        case "NB":
                            array_push($banks, ['name' => $paymentMethod->description, 'code' => $paymentMethod->payment_method, 'logo' => 'URL_of_logo', 'transaction_success_rate' => ((!empty($index) || $index === 0) ?  $outages[$index]->status : "GOOD")]);
                            break;
                        case "CARD":
                            array_push($cards, ['name' => $paymentMethod->description, 'code' => $paymentMethod->payment_method, 'logo' => config('rummy_config.cdn_url') . '/generic/icons/' . $paymentMethod->payment_method . ".svg", 'transaction_success_rate' => ((!empty($index) || $index === 0) ?  $outages[$index]->status : "GOOD")]);
                            break;
                        case "WALLET":
                            array_push($wallets, ['name' => $paymentMethod->description, 'code' => $paymentMethod->payment_method, 'logo' => 'URL_of_logo', 'transaction_success_rate' => ((!empty($index) || $index === 0) ?  $outages[$index]->status : "GOOD")]);
                            break;
                    }
                }
            }

            $successBag['quick_payment'] = $quickPayments;

            if ($request->for != 'quick_payment') {
                $successBag['bank']['list'] = $banks;
                $successBag['cards']['list'] = $cards;
                $successBag['wallets']['list'] = $wallets;
            }
            $allSavedPaymentOptions = [];
            if (!empty($userDetails['USER_OTHER_DETAILS']) && isset($userDetails['USER_OTHER_DETAILS']['jusPay_Cust_ID']))
                /* fetch all cards saved with JusPay for the user made the request */
                $allSavedPaymentOptions = $this->getSavedPaymentOptions($userDetails['USER_OTHER_DETAILS']['jusPay_Cust_ID'], $userId);

            $successBag['saved_list']['cards'] = [];
            if ($allSavedPaymentOptions) {
                foreach ($allSavedPaymentOptions as $savedOption) {
                    $savedDetail = [];
                    $savedDetail['card_no'] = $savedOption->card_number;
                    $savedDetail['expiry'] = $savedOption->card_exp_month . '/' . substr($savedOption->card_exp_year, 2);
                    $savedDetail['name'] = $savedOption->name_on_card;
                    $savedDetail['payment_option_id'] = $savedOption->card_token;
                    $savedDetail['logo'] = config('rummy_config.cdn_url') . '/generic/icons/' . $savedOption->card_brand . ".svg";
                    $savedDetail['card_isin'] = $savedOption->card_isin;
                    $savedDetail['card_brand'] = $savedOption->card_brand;
                    $savedDetail['card_type'] = $savedOption->card_type;
                    array_push($successBag['saved_list']['cards'], $savedDetail);
                }
            }
        } else {
            /* if JusPay is disabled from our backend */
            foreach ($enabledGateways as $gateway) {
                $successBag['list'][$gateway->id]['code'] = $gateway->gateway_name;
                $successBag['list'][$gateway->id]['name'] = $gateway->description;
                $successBag['list'][$gateway->id]['logo'] = $gateway->logo;
            }
            /* fetch all payment options available with Pay U Money */
            $file_path = base_path() . '/public/json/payUBankCodes.json';
            $json = file_get_contents($file_path);
            $successBag['bank']['list'] = json_decode($json);
            $file_path = base_path() . '/public/json/payUWalletCodes.json';
            $json = file_get_contents($file_path);
            $successBag['wallets']['list'] = json_decode($json);
            $file_path = base_path() . '/public/json/payUUPICodes.json';
            $json = file_get_contents($file_path);
            $successBag['upi']['list'] = json_decode($json);
        }
        $this->userActivitiesTracking($userId, "GATEWAY_LIST_GENERATED_SUCCESSFULLY", ['gatewaysActive' => $jusPayCheck]);
        return $this->successResponse(1, $successBag);
    }

    /**
     * Payment -> Add Money Module: For validating inputs and generating Pay U hash to be posted to Pay U server.
     * API EP :{base_url}/user/payment/deposit
     * <AUTHOR> Aroraa <<EMAIL>>
     */
    public function deposit(Request $request)
    {
        $details = array();
        $userId = getUserId();
        $minDepositAmount = \DB::table('deposit_amount_suggestion_configuration')->where('ID', 1)->value('AMOUNT');
        $transactionVariablesArr = config('rummy_config.transactionVariables');
        /* Common request validation */
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:' . $minDepositAmount,
            'code' => 'nullable|regex:/^[a-zA-Z0-9_\-]*$/|max:30',
            'gateway_code' => 'required|alpha_dash',
            'requested_via' => 'nullable|in:app,web',
            'vpa' => 'nullable|regex:/^[\w\.\-_]{3,}@[a-zA-Z]{3,}/',
            'milestone_id' => 'sometimes|exists:milestones,MILESTONE_ID'
        ], [
            'amount.required' => 'Enter amount',
            'amount.numeric' => 'Amount should contain only numbers',
            'amount.min' => 'Minimum deposit amount allowed is ' . $minDepositAmount,
            'code.regex' => 'Only alphabets, numbers, underscores, and hypen allowed in promo code',
            'code.max' => 'Maximum 30 characters allowed in promo code',
            'gateway_code.required' => 'Payment gateway code is required',
            'gateway_code.alpha_dash' => 'Only alphabets, underscore, and numbers are allowed in payment gateway code',
            'requested_via.in' => 'Invalid request platform',
            'vpa.regex' => 'Invaild VPA',
            'milestone_id.exists' => 'Invalid Milestone ID'
        ]);

        if ($validator->fails()) {
            $errorCode = ["amount" => 422028, "code" => 422029, 'gateway_code' => 422038, 'requested_via' => 422111, 'vpa' => 422041, "milestone_id" => 422061];
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $this->errorBag[] = [
                    'code' => $errorCode[$key],
                    'message' => $error[0]
                ];
            }
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        if (empty($request->requested_via)) {
            $request->requested_via = "app";
        }
        /* Common request validation end */

        if (strtolower($request->gateway_code) == config('rummy_config.payu_seamless_code')) {

            $validatorPayu = Validator::make($request->all(), [
                'payment_method' => 'required|in:card,upi,wallet,netbanking',
                'vpa' => 'required_if:payment_method,==,upi|regex:/^[\w\.\-_]{3,}@[a-zA-Z]{3,}/',
                'card_number' => 'required_if:payment_method,==,card|digits_between:13,19',
                'name' => 'required_if:payment_method,==,card|max:50|regex:/^[A-Za-z0-9\s]+$/',
                'expiry_month' => ['required_if:payment_method,==,card', Rule::in('01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12')],
                'expiry_year' => 'required_if:payment_method,==,card|digits:4',
                'cvv' => 'nullable|digits_between:3,4',
                'nb_code' => 'required_if:payment_method,==,netbanking|alpha_dash',
                'save_payment_details' => 'nullable|boolean'
            ], [
                'payment_method.required_without' => 'Payment Method is required',
                'payment_method.in' => 'Invalid payment method',
                'card_number.required_if' => 'Enter card number',
                'card_number.digits_between' => 'Card number can be between 13 to 19 digits only',
                'name.required_if' => 'Enter name',
                'name.max' => 'Maximum 50 characters allowed in name',
                'name.regex' => 'Only alphabets, numbers, and spaces are allowed in name',
                'expiry_month.required_if' => 'Expiry month is required',
                'expiry_month.in' => 'Invalid expiry month',
                'expiry_year.required_if' => 'Expiry year is required',
                'expiry_year.digits' => 'Expiry year should be of 4 digits',
                'cvv.digits_between' => 'Check CVV',
                'nb_code.required_if' => 'Net banking code is required',
                'nb_code.alpha_dash' => 'Only alphabets and numbers are allowed in net banking code',
                'save_payment_details.boolean' => 'Only boolean is allowed in save payment details',
                'vpa.required_if' => 'Enter VPA',
                'vpa.regex' => 'Invaild VPA'
            ]);

            if ($validatorPayu->fails()) {
                $errorCode = ['payment_method' => 422039, 'payment_option_id' => 422040, 'vpa' => 422041, 'card_number' => 422042, 'name' => 422043, 'expiry_month' => 422044, 'expiry_year' => 422044, 'nb_code' => 422045, 'save_payment_details' => 422054, 'cvv' => 422096];
                foreach ($validatorPayu->errors()->getMessages() as $key => $error) {
                    $errorBag[] = [
                        'code' => $errorCode[$key],
                        'message' => $error[0]
                    ];
                }
                return $this->errorResponse(1, $errorBag, 422);
            }

            if ($request->payment_method == "netbanking") {
                $file_path = storage_path() . '/json/bankCodes.json';
                $json = file_get_contents($file_path);
                $data = json_decode($json, true);
                if (!empty($data)) {
                    if (array_search($request->nb_code, array_column($data, 'code')) === false) {
                        $this->errorBag[] = [
                            'code' => 422045,
                            'message' => 'Invalid netbanking code'
                        ];
                        return $this->errorResponse(1, $this->errorBag, 422);
                    }
                } else {
                    $this->errorBag[] = [
                        'code' => 422045,
                        'message' => 'Invalid netbanking code'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                }
            }

            /* Check if expiry date of card in equal to current date and above */
            if ($request->payment_method == "card") {
                if (!$this->checkCardValidity($request->expiry_month, $request->expiry_year)) {
                    $this->errorBag[] = [
                        'code' => 422044,
                        'message' => 'Invalid expiry'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                }
            }
            $paymentMethod = strtolower($request->payment_method);
            switch ($paymentMethod) {
                case "card":
                    $details['card_number'] = $request->card_number;
                    $details['expiry_month'] = $request->expiry_month;
                    $details['expiry_year'] = $request->expiry_year;
                    $details['name'] = $request->name;
                    break;
                case "upi":
                    $details['vpa'] = $request->vpa;
                    break;
            }
        } elseif (strtolower($request->gateway_code) == config('rummy_config.juspay_code')) {
            if (!empty($request->vpa)) {
                $validateVPA = $this->validateVPA($request->vpa, $userId);
                if (!$validateVPA) {
                    $this->errorBag[] = [
                        'code' => 422041,
                        'message' => 'Invalid VPA'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                }
            }
        }
        $this->userActivitiesTracking($userId, "INTIATED_DEPOSIT_API_FRAMEWORK_VALIDATIONS_PASSED", ["request" => $request->all()]);
        /* Check for validity of gateway id */
        $paymentProviderID = $this->checkGatewayCode($request->gateway_code);
        if (!$paymentProviderID) {
            $this->userActivitiesTracking($userId, "DEPOSIT_API_INVALID_GATEWAY", ["request" => $request->all()]);
            $this->errorBag[] = [
                'code' => 422038,
                'message' => 'Invalid Gateway'
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        /* Check for deposit code */
        $depositCode = "";
        if ($request->exists("code")) {
            if ($request->code != NULL) {
                $depositCode = $request->code;
                $validateCode = $this->validateCode($request->code, $request->amount, $userId);
                if ($validateCode) {
                    if ($validateCode != "Valid") {
                        $this->userActivitiesTracking($userId, "DEPOSIT_CODE_UNPROCESSABLE", ["request" => $request->all()]);
                        return $validateCode;
                    }
                } else {
                    $this->userActivitiesTracking($userId, "DEPOSIT_CODE_INVALID", ["request" => $request->all()]);
                    $this->errorBag[] = [
                        'code' => 422029,
                        'message' => 'Invalid Code'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                }
            }
        }
        /* Validate amount as per deposit limit available */
        $validatAmountAndDepositLimit = $this->validateAmountAndDepositLimit($userId, $request->amount);
        if ($validatAmountAndDepositLimit) {
            if ($validatAmountAndDepositLimit != "Valid") {
                $this->userActivitiesTracking($userId, "DEPOSIT_API_AMOUNT_LIMIT_VALIDATION_ERROR", ["request" => $request->all()]);
                return $validatAmountAndDepositLimit;
            }
        } else {
            $this->userActivitiesTracking($userId, "DEPOSIT_API_AMOUNT_LIMIT_VALIDATION_ERROR", ["request" => $request->all()]);
            $this->errorBag[] = [
                'code' => 422028,
                'message' => 'Amount validation error'
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        /* Check if payment option id supplied is connected to user's account */
        $paymentMethod = "";
        $code = "";
        $checkPaymentOptionID = false;
        $userDetails = $this->getUserDetails($userId);
        if ($request->exists("payment_option_id")) {
            $checkPaymentOptionID['card_token'] = $request->payment_option_id;
            $paymentMethod = "card";
        } else {
            $paymentMethod = $request->payment_method;
            if ($request->payment_method == "wallet") {
                $code = $request->wallet_code;
            } elseif ($request->payment_method == "netbanking") {
                $code = $request->nb_code;
            }
            if ($request->exists('save_payment_details') && $request->save_payment_details) {
                if ($request->payment_method == "card" || $request->payment_method == "upi") {
                    $savePaymentDetails = $this->savePaymentOptions($userId, $request->gateway_code, $request->payment_method, $details, $userDetails);
                    if (empty($savePaymentDetails->card_token)) {
                        return $savePaymentDetails;
                    }
                }
                if ($request->payment_method == "card") {
                    $checkPaymentOptionID['card_token'] = $savePaymentDetails->card_token;
                }
            } else {
                if ($request->payment_method == "card" && $request->gateway_code == config('rummy_config.juspay_code')) {
                    $checkPaymentOptionID['card_token'] = $this->jusPayTokenizeCard($details, $request->cvv, $userId);
                }
            }
        }
        //if (strlen($userDetails->FIRSTNAME) == 0 || strlen($userDetails->LASTNAME) == 0) {
        if (strlen($userDetails->FIRSTNAME) == 0) {
            $this->userActivitiesTracking($userId, "USERS_FULL_NAME_IS_NOT_UPDATED", ["request" => $request->all()]);
            if (!$this->checkCardValidity($request->expiry_month, $request->expiry_year)) {
                $this->errorBag[] = [
                    'code' => 422074,
                    'message' => 'User hasn\'t updated name'
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            }
        }
        $micro_date = explode(" ", microtime());
        $micro_date = substr($micro_date[0], 2);
        $txnid = "121" . $userId . $micro_date . rand(10, 99);
        $transactionTypeID = $transactionVariablesArr['realCashPurchaseTransactionTypes']['deposit'];
        $transactionStatusID = $transactionVariablesArr['depositTransactionStatusPendingID']; //deposit pending untill the payment status is success
        $createdBY = $userId;
        $createdOn = date('Y-m-d H:i:s');
        if (strtolower($request->gateway_code) == config('rummy_config.juspay_code')) {
            return $this->processJusPayCall($paymentMethod, $txnid, $userId, $request, $userDetails, $paymentProviderID, $transactionTypeID, $transactionStatusID, $createdBY, $createdOn, $depositCode, $checkPaymentOptionID, $code);
        } else if (strtolower($request->gateway_code) == config('rummy_config.payu_seamless_code')) {
            return $this->processPayUCall($txnid, $userId, $request, $userDetails, $paymentProviderID, $transactionTypeID, $transactionStatusID, $createdBY, $createdOn, $depositCode, $checkPaymentOptionID);
        } else {
            $this->errorBag[] = [
                'code' => 422038,
                'message' => 'Unable to validate payment gateway'
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
    }

    /**
     * Payment -> Add Money Module: For validating inputs and generating Pay U hash to be posted to Pay U server.
     * API EP :{base_url}/v2/user/payment/deposit
     * <AUTHOR> Sharma <<EMAIL>>
     */
    public function depositNew(Request $request)
    {
        $details = array();
        $userId = getUserId();
        $minDepositAmount = \DB::table('deposit_amount_suggestion_configuration')->where('ID', 1)->value('AMOUNT');
        $transactionVariablesArr = config('rummy_config.transactionVariables');
        /* Common request validation */
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:' . $minDepositAmount,
            'code' => 'nullable|regex:/^[a-zA-Z0-9_\-]*$/|max:30',
            'gateway_code' => 'required|alpha_dash',
            'requested_via' => 'nullable|in:app,web',
            'vpa' => 'nullable|regex:/^[\w\.\-_]{3,}@[a-zA-Z]{3,}/',
            'payment_mode' => 'required|in:cc,dc,nb,wallet,upi'
        ], [
            'amount.required' => 'Enter amount',
            'amount.min' => 'Amount must be greater than ' . $minDepositAmount,
            'amount.numeric' => 'Amount should contain only numbers',
            'amount.min' => 'Minimum deposit amount allowed is ' . $minDepositAmount,
            'code.regex' => 'Only alphabets, numbers, underscores, and hypen allowed in promo code',
            'code.max' => 'Maximum 30 characters allowed in promo code',
            'gateway_code.required' => 'Payment gateway code is required',
            'gateway_code.alpha_dash' => 'Only alphabets, underscore, and numbers are allowed in payment gateway code',
            'requested_via.in' => 'Invalid request platform',
            'vpa.regex' => 'Invaild VPA',
            'payment_mode.required' => 'Payment mode is required',
        ]);

        if ($validator->fails()) {
            $errorCode = ["amount" => 422028, "code" => 422029, 'gateway_code' => 422038, 'requested_via' => 422111, 'vpa' => 422041, 'payment_mode' => 422112];
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $this->errorBag[] = [
                    'code' => $errorCode[$key],
                    'message' => $error[0]
                ];
            }
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        if (empty($request->requested_via)) {
            $request->requested_via = "app";
        }

        /* Common request validation end */
         
        if (strtolower($request->gateway_code) == config('rummy_config.payu_seamless.code')) {
           
            $validatorPayu = Validator::make($request->all(), [
                'payment_method' => 'required|in:card,upi,wallet,netbanking',
                'vpa' => 'required_if:payment_method,==,upi|regex:/^[\w\.\-_]{3,}@[a-zA-Z]{3,}/',
                'card_number' => 'required_if:payment_method,==,card|digits_between:13,19',
                'name' => 'required_if:payment_method,==,card|max:50|regex:/^[A-Za-z0-9\s]+$/',
                'expiry_month' => ['required_if:payment_method,==,card', Rule::in('01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12')],
                'expiry_year' => 'required_if:payment_method,==,card|digits:4',
                'cvv' => 'nullable|digits_between:3,4',
                'nb_code' => 'required_if:payment_method,==,netbanking|alpha_dash',
                'save_payment_details' => 'nullable|boolean'
            ], [
                'payment_method.required_without' => 'Payment Method is required',
                'payment_method.in' => 'Invalid payment method',
                'card_number.required_if' => 'Enter card number',
                'card_number.digits_between' => 'Card number can be between 13 to 19 digits only',
                'name.required_if' => 'Enter name',
                'name.max' => 'Maximum 50 characters allowed in name',
                'name.regex' => 'Only alphabets, numbers, and spaces are allowed in name',
                'expiry_month.required_if' => 'Expiry month is required',
                'expiry_month.in' => 'Invalid expiry month',
                'expiry_year.required_if' => 'Expiry year is required',
                'expiry_year.digits' => 'Expiry year should be of 4 digits',
                'cvv.digits_between' => 'Check CVV',
                'nb_code.required_if' => 'Net banking code is required',
                'nb_code.alpha_dash' => 'Only alphabets and numbers are allowed in net banking code',
                'save_payment_details.boolean' => 'Only boolean is allowed in save payment details',
                'vpa.required_if' => 'Enter VPA',
                'vpa.regex' => 'Invaild VPA'
            ]);

            if ($validatorPayu->fails()) {
                $errorCode = ['payment_method' => 422039, 'payment_option_id' => 422040, 'vpa' => 422041, 'card_number' => 422042, 'name' => 422043, 'expiry_month' => 422044, 'expiry_year' => 422044, 'nb_code' => 422045, 'save_payment_details' => 422054, 'cvv' => 422096];
                foreach ($validatorPayu->errors()->getMessages() as $key => $error) {
                    $errorBag[] = [
                        'code' => $errorCode[$key],
                        'message' => $error[0]
                    ];
                }
                return $this->errorResponse(1, $errorBag, 422);
            }

            if ($request->payment_method == "netbanking") {
                $file_path = storage_path() . '/json/bankCodes.json';
                $json = file_get_contents($file_path);
                $data = json_decode($json, true);
                if (!empty($data)) {
                    if (array_search($request->nb_code, array_column($data, 'code')) === false) {
                        $this->errorBag[] = [
                            'code' => 422045,
                            'message' => 'Invalid netbanking code'
                        ];
                        return $this->errorResponse(1, $this->errorBag, 422);
                    }
                } else {
                    $this->errorBag[] = [
                        'code' => 422045,
                        'message' => 'Invalid netbanking code'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                }
            }

            /* Check if expiry date of card in equal to current date and above */
            if ($request->payment_method == "card") {
                if (!$this->checkCardValidity($request->expiry_month, $request->expiry_year)) {
                    $this->errorBag[] = [
                        'code' => 422044,
                        'message' => 'Invalid expiry'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                }
            }
            
            $paymentMethod = strtolower($request->payment_method);
            switch ($paymentMethod) {
                case "card":
                    $details['card_number'] = $request->card_number;
                    $details['expiry_month'] = $request->expiry_month;
                    $details['expiry_year'] = $request->expiry_year;
                    $details['name'] = $request->name;
                    break;
                case "upi":
                    $details['vpa'] = $request->vpa;
                    break;
            }
        } 
        elseif (strtolower($request->gateway_code) == config('rummy_config.juspay_code')) {
            if (!empty($request->vpa)) {
                $validateVPA = $this->validateVPA($request->vpa, $userId);
                if (!$validateVPA) {
                    $this->errorBag[] = [
                        'code' => 422041,
                        'message' => 'Invalid VPA'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                }
            }
        }
        
        $this->userActivitiesTracking($userId, "INTIATED_DEPOSIT_API_FRAMEWORK_VALIDATIONS_PASSED", ["request" => $request->all()]);
        
        /* Check for validity of gateway id */
        $paymentProviderID = $this->checkGatewayCode($request->gateway_code);
        if (!$paymentProviderID) {
            $this->userActivitiesTracking($userId, "DEPOSIT_API_INVALID_GATEWAY", ["request" => $request->all()]);
            $this->errorBag[] = [
                'code' => 422038,
                'message' => 'Invalid Gateway'
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }        

        /* Check for deposit code */
        $depositCode = "";
        if ($request->exists("code")) {
            if ($request->code != NULL) {
                $depositCode = $request->code;
                $validateCode = $this->validateCode($request->code, $request->amount, $userId);
                if ($validateCode) {
                    if ($validateCode != "Valid") {
                        $this->userActivitiesTracking($userId, "DEPOSIT_CODE_UNPROCESSABLE", ["request" => $request->all()]);
                        return $validateCode;
                    }
                } else {
                    $this->userActivitiesTracking($userId, "DEPOSIT_CODE_INVALID", ["request" => $request->all()]);
                    $this->errorBag[] = [
                        'code' => 422029,
                        'message' => 'Invalid Code'
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                }
            } else if(isset($request->offer_id) &&  !empty($request->offer_id)){
                /* Runtime campaign code generation for FTD and STD user as per the new add cash flow */
                $runtimeDepositCode = $this->createRuntimeDepositCode($request);
                if(is_null($runtimeDepositCode)){
                    return $this->errorResponse(1, "Something went wrong", 500);
                }
                $depositCode = $runtimeDepositCode;
                $request->merge(['code' => $runtimeDepositCode]);
            }
        }
        /* Validate amount as per deposit limit available */
        $validatAmountAndDepositLimit = $this->validateAmountAndDepositLimit($userId, $request->amount);
        
        if ($validatAmountAndDepositLimit) {
            if ($validatAmountAndDepositLimit != "Valid") {
                $this->userActivitiesTracking($userId, "DEPOSIT_API_AMOUNT_LIMIT_VALIDATION_ERROR", ["request" => $request->all()]);
                return $validatAmountAndDepositLimit;
            }
        } 
        else {
            $this->userActivitiesTracking($userId, "DEPOSIT_API_AMOUNT_LIMIT_VALIDATION_ERROR", ["request" => $request->all()]);
            $this->errorBag[] = [
                'code' => 422028,
                'message' => 'Amount validation error'
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        /* Check if payment option id supplied is connected to user's account */
        $paymentMethod = "";
        $code = "";
        $checkPaymentOptionID = false;
        $userDetails = $this->getUserDetails($userId);
        $paymentMode = $request->payment_mode;
            if ($request->exists("payment_option_id")) {
                $checkPaymentOptionID['card_token'] = $request->payment_option_id;
                $paymentMethod = "card";
            } else {
            $paymentMethod = $request->payment_method;
            if ($request->payment_method == "wallet") {
                $code = $request->wallet_code;
            } elseif ($request->payment_method == "netbanking") {
                $code = $request->nb_code;
            }
            if ($request->exists('save_payment_details') && $request->save_payment_details) {
                if ($request->payment_method == "card" || $request->payment_method == "upi") {
                    $savePaymentDetails = $this->savePaymentOptions($userId, $request->gateway_code, $request->payment_method, $details, $userDetails);
                    if (empty($savePaymentDetails->card_token)) {
                        return $savePaymentDetails;
                    }
                }
                if ($request->payment_method == "card") {
                    $checkPaymentOptionID['card_token'] = $savePaymentDetails->card_token;
                }
            } else {
                if ($request->payment_method == "card" && $request->gateway_code == config('rummy_config.juspay_code')) {
                    $checkPaymentOptionID['card_token'] = $this->jusPayTokenizeCard($details, $request->cvv, $userId);
                }
            }
        }
        //if (strlen($userDetails->FIRSTNAME) == 0 || strlen($userDetails->LASTNAME) == 0) {
        if (strlen($userDetails->FIRSTNAME) == 0) {
            $this->userActivitiesTracking($userId, "USERS_FULL_NAME_IS_NOT_UPDATED", ["request" => $request->all()]);
            if (!$this->checkCardValidity($request->expiry_month, $request->expiry_year)) {
                $this->errorBag[] = [
                    'code' => 422074,
                    'message' => 'User hasn\'t updated name'
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            }
        }
        $micro_date = explode(" ", microtime());
        $micro_date = substr($micro_date[0], 2);
        $txnid = "121" . $userId . $micro_date . rand(10, 99);
        $transactionTypeID = $transactionVariablesArr['realCashPurchaseTransactionTypes']['deposit'];
        $transactionStatusID = $transactionVariablesArr['depositTransactionStatusPendingID']; //deposit pending untill the payment status is success
        $createdBY = $userId;
        $createdOn = date('Y-m-d H:i:s');
        
        if($paymentMode){
        
        //calculate payment gateway fee
            $gatewayFeeComponent =  $this->paymentGatewayFeeCalculator($request->amount, $paymentMode, $userDetails);
            //calculate payment gateway fee
            $userSettings = $this->getUserSettings($userId);
            $depositGstFlag = $this->checkGstFlagEnabled($userId);
            if($depositGstFlag){
                $depositGSTcomponent = $this->calculateGstOnDepositAmount($request->amount);
                $amountToBePaidAfterGST = $request->amount - $depositGSTcomponent;
                $gstComp = [
                            'rate' => (int) config('rummy_config.transactionVariables.gstRateOnDeposit'),
                            'refund_rate' => (int) $refundRate = $userSettings->IS_DEPOSIT_GST_WAIVED_OFF ? config('rummy_config.transactionVariables.gstRefundRateOnDeposit') : 0,
                            'amount' => $depositGSTcomponent,
                            'amount_to_be_refunded' => round($depositGSTcomponent/100*$refundRate, 2),
                            'gst_waived_off' => $userSettings->IS_DEPOSIT_GST_WAIVED_OFF ? true : false,
                            'refund_balance_type' => (int) $this->getGstBonusBalanceType()
                        ];
            }else{
                $gstComp = [
                            'rate' => (int) config('rummy_config.transactionVariables.gstRateOnDeposit'),
                            'refund_rate' => (int) $refundRate = config('rummy_config.transactionVariables.gstRefundRateOnDeposit'),
                            'amount' => 0,
                            'amount_to_be_refunded' => 0,
                            'gst_waived_off' => true,
                            'refund_balance_type' => (int) $this->getGstBonusBalanceType()
                        ];   
            }
            
            if (strtolower($request->gateway_code) == config('rummy_config.juspay_code')) {
                return $this->processJusPayCallNew($paymentMethod, $txnid, $userId, $request, $userDetails, $paymentProviderID, $transactionTypeID, $transactionStatusID, $createdBY, $createdOn, $depositCode, $checkPaymentOptionID, $code, $gatewayFeeComponent, $gstComp);
            } else {
                $this->errorBag[] = [
                    'code' => 422038,
                    'message' => 'Unable to validate payment gateway'
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            }
        } else {
            if (strtolower($request->gateway_code) == config('rummy_config.juspay_code')) {
                return $this->processJusPayCall($paymentMethod, $txnid, $userId, $request, $userDetails, $paymentProviderID, $transactionTypeID, $transactionStatusID, $createdBY, $createdOn, $depositCode, $checkPaymentOptionID, $code);
            } else {
                $this->errorBag[] = [
                    'code' => 422038,
                    'message' => 'Unable to validate payment gateway'
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            }
        }
        // else if (strtolower($request->gateway_code) == config('rummy_config.payu_seamless.code')) {
        //     return $this->processPayUCall($txnid, $userId, $request, $userDetails, $paymentProviderID, $transactionTypeID, $transactionStatusID, $createdBY, $createdOn, $depositCode, $checkPaymentOptionID);
        // }
        /*
        else if (strtolower($request->gateway_code) ==  config('rummy_config.cashfree_seamless.code')) {
            return $this->processCashfreeCall($txnid, $userId, $request, $userDetails, $paymentProviderID, $transactionTypeID, $transactionStatusID, $createdBY, $createdOn, $depositCode, $checkPaymentOptionID);
        } 
        */
    }

    protected function processJusPayCall($paymentMethod, $txnid, $userId, $request, $userDetails, $paymentProviderID, $transactionTypeID, $transactionStatusID, $createdBY, $createdOn, $depositCode, $checkPaymentOptionID, $code)
    {
        $this->userActivitiesTracking($userId, "DEPOSIT_API_INITIATED_JUSPAY_CALL", ["pg" => "juspay"]);
        /* Juspay Order Creation */
        $jusPayOrder = $this->jusPayOrderCreate($txnid, $request->amount . ".00", $userDetails['USER_OTHER_DETAILS']['jusPay_Cust_ID'], $userDetails['EMAIL_ID'], $userDetails['CONTACT'], config('rummy_config.paymentGateway_product_desc'), $userId, $request->requested_via);
        /* Order creation end */
        if ($jusPayOrder) {
            $successBag = [
                'id' => $jusPayOrder['id'],
                'order_id' => $jusPayOrder['order_id'],
                'payment_links' => $jusPayOrder['payment_links'],
                'client_auth_token' => $jusPayOrder['juspay']->client_auth_token ?? '',
                'client_token_expiry' => $jusPayOrder['juspay']->client_auth_token_expiry ?? ''
            ];

            $paymentTransaction = $this->insertIntoPaymentTransaction($userId, $paymentProviderID->id, $transactionTypeID, $request->amount, $transactionStatusID, $createdBY, $createdOn, $txnid, $depositCode, $request->gateway_code, $request->requested_via, $request->app_type, $request->milestone_id);
            if ($paymentTransaction) {
                $this->userActivitiesTracking($userId, "DEPOSIT_API_INITIATED_JUSPAY_CALL_SUCCESSFULLY - " . $txnid, ['request' => $request->all(), 'success_response' => $successBag]);
                return $this->successResponse(1, $successBag);
            } else {
                $this->userActivitiesTracking($userId, "DEPOSIT_API_ERROR_IN_INITIATING_JUSPAY_CALL - " . $txnid, $request->all());
                return $this->errorResponse(1, $this->errorBag, 422);
            }
        } else {
            $this->userActivitiesTracking($userId, "JUSPAY_ORDER_CREATION_FAILED - " . $txnid, [$paymentMethod, $txnid, $request->amount . ".00", $userDetails['USER_OTHER_DETAILS']['jusPay_Cust_ID'], $userDetails['EMAIL_ID'], $userDetails['CONTACT'], config('rummy_config.paymentGateway_product_desc'), $code, $checkPaymentOptionID, $userId]);
            $this->errorBag[] = [
                'code' => 422060,
                'message' => 'Failed to create order.'
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
    }

    protected function processJusPayCallNew($paymentMethod, $txnid, $userId, $request, $userDetails, $paymentProviderID, $transactionTypeID, $transactionStatusID, $createdBY, $createdOn, $depositCode, $checkPaymentOptionID, $code, $gatewayFeeComponent, $gstComp)
    {
        $this->userActivitiesTracking($userId, "DEPOSIT_API_INITIATED_JUSPAY_CALL", ["pg" => "juspay"]);
        /* Juspay Order Creation */
        $jusPayOrder = $this->jusPayOrderCreate($txnid, $gatewayFeeComponent['total_amount_with_disc_fee_gst'], $userDetails['USER_OTHER_DETAILS']['jusPay_Cust_ID'], $userDetails['EMAIL_ID'], $userDetails['CONTACT'], config('rummy_config.paymentGateway_product_desc'), $userId, $request->requested_via);
        /* Order creation end */
        if ($jusPayOrder) {
            $successBag = [
                'id' => $jusPayOrder['id'],
                'order_id' => $jusPayOrder['order_id'],
                'payment_links' => $jusPayOrder['payment_links'],
                'client_auth_token' => $jusPayOrder['juspay']->client_auth_token ?? '',
                'client_token_expiry' => $jusPayOrder['juspay']->client_auth_token_expiry ?? ''
            ];
            $extraFields['app_build'] = $request->app_build ?? '';
            $extraFields['gateway_fee_component'] = $gatewayFeeComponent ?? '';
            $extraFields['gst'] = $gstComp ?? '';

            $paymentTransaction = $this->insertIntoPaymentTransactionNew($userId, $paymentProviderID->id, $transactionTypeID, $request->amount, $transactionStatusID, $createdBY, $createdOn, $txnid, $depositCode, $request->gateway_code, $request->requested_via,$request->app_type ?? "",$request->milestone_id,$extraFields);
            if ($paymentTransaction) {
                $this->userActivitiesTracking($userId, "DEPOSIT_API_INITIATED_JUSPAY_CALL_SUCCESSFULLY - " . $txnid, ['request' => $request->all(), 'gatewayFeeComponent' => $gatewayFeeComponent['total_amount_disc_fee_without_gst'],'success_response' => $successBag]);
                return $this->successResponse(1, $successBag);
            } else {
                $this->userActivitiesTracking($userId, "DEPOSIT_API_ERROR_IN_INITIATING_JUSPAY_CALL - " . $txnid, $request->all());
                return $this->errorResponse(1, $this->errorBag, 422);
            }
        } 
        else {
            $this->userActivitiesTracking($userId, "JUSPAY_ORDER_CREATION_FAILED - " . $txnid, [$paymentMethod, $txnid, $request->amount . ".00", $userDetails['USER_OTHER_DETAILS']['jusPay_Cust_ID'], $userDetails['EMAIL_ID'], $userDetails['CONTACT'], config('rummy_config.paymentGateway_product_desc'), $code, $checkPaymentOptionID, $userId]);
            $this->errorBag[] = [
                'code' => 422060,
                'message' => 'Failed to create order.'
            ];
            return $this->errorResponse(1, $this->errorBag, 422);
        }
    }

    protected function processPayUCall($txnid, $userId, $request, $userDetails, $paymentProviderID, $transactionTypeID, $transactionStatusID, $createdBY, $createdOn, $depositCode, $checkPaymentOptionID)
    {
        $this->userActivitiesTracking($userId, "DEPOSIT_API_INITIATED_PAYUMONEY_CALL - " . $txnid, ["request" => $request->all()]);
        $details = array();
        $hash_string = config('payU.payU_key') . "|" . $txnid . "|" . $request->amount . ".00|" . config('rummy_config.paymentGateway_product_desc') . "|" . $userDetails->FIRSTNAME . "|" . $userDetails->EMAIL_ID . "|" . config('rummy_config.payu_seamless_code') . "|" . $txnid . "|" . $request->requested_via . "||||||||" . config('payU.payu_salt');
        $successBag['hash'] = strtolower(hash('sha512', $hash_string));
        $successBag['gateway_code'] = config('rummy_config.payu_seamless_code');
        $successBag['redirect_url'] = config('payU.payu_url');
        $successBag['key'] = config('payU.payU_key');
        $successBag['txnid'] = $txnid;
        $successBag['amount'] = $request->amount . ".00";
        $successBag['productinfo'] = config('rummy_config.paymentGateway_product_desc');
        $successBag['first_name'] = $userDetails->FIRSTNAME;
        $successBag['last_name'] = $userDetails->LASTNAME;
        $successBag['email'] = $userDetails->EMAIL_ID;
        $successBag['mobile'] = $userDetails->CONTACT;
        $successBag['surl'] = config('payU.surl');
        $successBag['furl'] = config('payU.furl');
        $successBag['curl'] = config('payU.curl');
        $successBag['udf1'] = "payu_seamless";
        $successBag['udf2'] = $txnid;
        $successBag['udf3'] = $request->requested_via;
        if ($checkPaymentOptionID) {
            $successBag['payment_option_id'] = $request->payment_option_id;
            switch (strtolower($checkPaymentOptionID->PAYMENT_METHOD)) {
                case "card":
                    $successBag['payment_method'] = "CC";
                    $successBag['bank_code'] = "CC";
                    $successBag['card_number'] = $checkPaymentOptionID->CARD_NO;
                    $successBag['ccname'] = $checkPaymentOptionID->NAME;
                    $successBag['expiry_month'] = $checkPaymentOptionID->EXPIRY_MONTH;
                    $successBag['expiry_year'] = $checkPaymentOptionID->EXPIRY_YR;
                    $details['card_number'] = $checkPaymentOptionID->CARD_NO;
                    $details['name'] = $checkPaymentOptionID->NAME;
                    $details['expiry_month'] = $checkPaymentOptionID->EXPIRY_MONTH;
                    $details['expiry_year'] = $checkPaymentOptionID->EXPIRY_YR;
                    break;
                case "upi":
                    $successBag['payment_method'] = "UPI";
                    $successBag['bank_code'] = $request->nb_code;
                    $successBag['vpa'] = $checkPaymentOptionID->VPA;
                    $details['vpa'] = $checkPaymentOptionID->VPA;
                    break;
            }
        } else {
            switch (strtolower($request->payment_method)) {
                case "netbanking":
                    $successBag['payment_method'] = "NB";
                    $successBag['bank_code'] = $request->nb_code;
                    break;
                case "card":
                    $successBag['payment_method'] = "CC";
                    $successBag['bank_code'] = "CC";
                    $successBag['card_number'] = $request->card_number;
                    $successBag['expiry_month'] = $request->expiry_month;
                    $successBag['expiry_year'] = $request->expiry_year;
                    $successBag['ccname'] = $request->name;
                    $details['card_number'] = $request->card_number;
                    $details['expiry_month'] = $request->expiry_month;
                    $details['expiry_year'] = $request->expiry_year;
                    $details['name'] = $request->name;
                    break;
                case "upi":
                    $successBag['payment_method'] = "UPI";
                    $successBag['bank_code'] = $request->nb_code;
                    $successBag['vpa'] = $request->vpa;
                    $details['vpa'] = $request->vpa;
                    break;
                case "wallet";
                    $successBag['payment_method'] = "CASHCARD";
                    $successBag['bank_code'] = $request->wallet_code;
            }
        }
        $paymentTransaction = $this->insertIntoPaymentTransaction($userId, $paymentProviderID->id, $transactionTypeID, $request->amount, $transactionStatusID, $createdBY, $createdOn, $txnid, $depositCode, $request->gateway_code, $request->requested_via, $request->app_type, $request->milestone_id);
        if ($paymentTransaction) {
            $this->userActivitiesTracking($userId, "DEPOSIT_API_INITIATed_PAYU_CALL_SUCCESSFULLY - " . $txnid, ['request' => $request->all(), 'success_response' => $successBag]);
            return $this->successResponse(1, $successBag);
        } else {
            $this->userActivitiesTracking($userId, "DEPOSIT_API_ERROR_IN_INITIATING_PAYU_CALL - " . $txnid, $request->all());
            return $this->errorResponse(1, $this->errorBag, 422);
        }
    }

    /**
     * Payment -> Add Money Module: For removing user's saved payment credentials.
     * API EP :{base_url}/user/payment/credential
     * <AUTHOR> Aroraa <<EMAIL>>
     */
    public function removeSavedCredentials(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'payment_option_id' => 'required|max:75',
        ], [
            'payment_option_id.required' => 'Payment option id is required',
            'payment_option_id.max' => 'Maximum 75 characters allowed in payment option id'
        ]);
        if ($validator->fails()) {
            $errorCode = ['payment_option_id' => 422040];
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $this->errorBag[] = [
                    'code' => $errorCode[$key],
                    'message' => $error[0]
                ];
            }
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        $userId = getUserId();
        $this->userActivitiesTracking($userId, "REMOVE_CREDENTIALS_INITIATED", ["request" => $request->all()]);
        $deletePaymentOption = $this->deletePaymentOption($request->payment_option_id);
        return $deletePaymentOption;
    }

    /**
     * Payment -> Check Transaction Status Module: for checking and reverting for a transaction id and relevant details required as per document.
     * API EP :{base_url}/user/payment/status
     * <AUTHOR> Aroraa <<EMAIL>>
     */
    public function getTransactionStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'txn_id' => 'required|alpha_num',
        ], [
            'txn_id.required' => 'Transaction ID is required',
            'txn_id.alpha_num' => 'Only alphabets and numbers are allowed in transaction ID'
        ]);
        if ($validator->fails()) {
            $errorCode = ['txn_id' => 422088];
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $this->errorBag[] = [
                    'code' => $errorCode[$key],
                    'message' => $error[0]
                ];
            }
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        $userId = getUserId();
        $this->userActivitiesTracking($userId, "GET_TRANSACTION_STATUS_INITIATED - " . $request->txn_id, ["request" => $request->all()]);
        return $this->transationStatus($request->txn_id);
    }

    public function getDepositAmountSuggestion()
    {
        $userID = getUserId();
        return $this->fetchLastestTransactions($userID);
    }

    public function checkCardType(Request $request)
    {   
        $validator = Validator::make($request->all(), [
            'card_digit' => 'required|numeric|digits:6'
        ], [
            'card_digit.required' => 'Enter card number',
            'card_digit.numeric' => 'Card number should contain only numbers',
            'card_digit.digits' => 'Card number can be of 6 digits only',
        ]);
        if ($validator->fails()) {
            $errorCode = ["card_digit" => 422301];
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $this->errorBag[] = [
                    'code' => $errorCode[$key],
                    'message' => $error[0]
                ];
            }
            return $this->errorResponse(1, $this->errorBag, 422);
        }
        $userID = getUserId();
        $data = $this->checkCardFromVendorJuspay($request->card_digit, $userID);

        try{
            $paymentModes = [
                "DEBIT" => "dc",
                "CREDIT" => "cc"
            ];
            if ($data->httpStatus == 200 && isset($data->response)) {
                if(array_key_exists($data->response->type, $paymentModes)){
                    $mode = $paymentModes[$data->response->type];
                    $successBag['card_type'] =  $mode;
                    return $this->successResponse(1, $successBag);
                } else {
                    $this->errorBag[] = [
                        'code' => 422302,
                        'message' => "Invalid card number"
                    ];
                    return $this->errorResponse(1, $this->errorBag, 422);
                } 
            } else{
                $this->errorBag[] = [
                    'code' => 422302,
                    'message' => "Invalid card number"
                ];
                return $this->errorResponse(1, $this->errorBag, 422);
            }  
        }catch(\Exception $e){
            $this->userActivitiesTracking($userID, "UNABLE_TO_CARD_DETAILS",$e);
            return $this->errorResponse(1, "Something went wrong", 500);
        }

    }
    
    /**
     * getDepositAmountSuggestionNew
     *
     * @return void
     */
    public function getDepositAmountSuggestionNew()
    {
        $userID = getUserId();
        $user = User::where('USER_ID', $userID)->first();
        $transactionVariablesArr = config('rummy_config.transactionVariables');
        $paymentCount = getSuccessfulDepositCount($userID);
        $amountSuggestions = [];
        $minDepositAmount = \DB::table('deposit_amount_suggestion_configuration')->where('ID', 1)->value('AMOUNT');
        if($paymentCount == 0){
            $suggestionConfigs = \DB::table('deposit_amount_suggestion_configuration')->select('ID', 'USER_TYPE', 'AMOUNT', 'REWARD_CONFIGURATION', 'DESCRIPTION')->where('USER_TYPE', 1)->get();
            foreach($suggestionConfigs as $suggestionConfig){
                $amountSuggestions[] = [
                    'id' => $suggestionConfig->ID,
                    'user_type' => $suggestionConfig->USER_TYPE,
                    'amount' => round($suggestionConfig->AMOUNT),
                    'reward_config' => Carbon::now()->subDay()->format('Y-m-d H:i:s') <= $user->REGISTRATION_TIMESTAMP ? json_decode($suggestionConfig->REWARD_CONFIGURATION) : null,
                    'description' => Carbon::now()->subDay()->format('Y-m-d H:i:s') <= $user->REGISTRATION_TIMESTAMP ? $suggestionConfig->DESCRIPTION : null
                ];
            }
            
        } else if($paymentCount == 1){
            $firstSuccessfulDeposit = PaymentTransaction::where('USER_ID', $userID)
            ->select('PAYMENT_TRANSACTION_AMOUNT')
            ->whereIn('PAYMENT_TRANSACTION_STATUS', [
                $transactionVariablesArr['transactionSuccessPaymentStatus'],
                $transactionVariablesArr['depositOkSuccessPaymentStatus']
            ])->first();
            $firstTxnAmount = $firstSuccessfulDeposit->PAYMENT_TRANSACTION_AMOUNT < $minDepositAmount ? $minDepositAmount : $firstSuccessfulDeposit->PAYMENT_TRANSACTION_AMOUNT;
            $firstTxnAmount = $this->customRound(round($firstTxnAmount));
            $suggestionConfigs = \DB::table('deposit_amount_suggestion_configuration')->select('ID', 'USER_TYPE', 'AMOUNT', 'REWARD_CONFIGURATION', 'DESCRIPTION')->where('USER_TYPE', 2)->get();
            foreach($suggestionConfigs as $suggestionConfig){
                $amountSuggestions[] = [
                    'id' => $suggestionConfig->ID,
                    'user_type' => $suggestionConfig->USER_TYPE,
                    'amount' => round($suggestionConfig->AMOUNT*$firstTxnAmount),
                    'reward_config' => json_decode($suggestionConfig->REWARD_CONFIGURATION),
                    'description' => $suggestionConfig->DESCRIPTION
                ];
            }
        } else {
            $noOfLastSuccessfulTxns = GlobalConfig::where('CONFIG_KEY', 'number_of_last_successful_transactions')->where('CONFIG_VALUE', '>', 0)->value('CONFIG_VALUE') ?? 10;
            $successfulDeposits = PaymentTransaction::where('USER_ID', $userID)
            ->select('PAYMENT_TRANSACTION_ID', 'PAYMENT_TRANSACTION_AMOUNT')
            ->whereIn('PAYMENT_TRANSACTION_STATUS', [
                $transactionVariablesArr['transactionSuccessPaymentStatus'],
                $transactionVariablesArr['depositOkSuccessPaymentStatus']
            ])->orderBy('PAYMENT_TRANSACTION_ID', 'DESC')->limit($noOfLastSuccessfulTxns)->get();
            $suggestionConfigs = \DB::table('deposit_amount_suggestion_configuration')->select('ID', 'USER_TYPE', 'AMOUNT', 'REWARD_CONFIGURATION', 'DESCRIPTION')->where('USER_TYPE', 3)->get();
            $amountAvg = $successfulDeposits->avg('PAYMENT_TRANSACTION_AMOUNT') < $minDepositAmount ? $minDepositAmount : $successfulDeposits->avg('PAYMENT_TRANSACTION_AMOUNT');
            $amountAvg = $this->customRound(round($amountAvg));
            foreach($suggestionConfigs as $suggestionConfig){
                $amountSuggestions[] = [
                    'id' => $suggestionConfig->ID,
                    'user_type' => $suggestionConfig->USER_TYPE,
                    'amount' => round($suggestionConfig->AMOUNT*$amountAvg),
                    'reward_config' => json_decode($suggestionConfig->REWARD_CONFIGURATION),
                    'description' => $suggestionConfig->DESCRIPTION
                ];
            }
        }
        return $this->successResponse(1, $amountSuggestions);
    }
    
    /**
     * customRound
     * For values 0 to 50 it will be 50
     * For values 51 to 100 it will be 100
     * For values 101 to 150 it will be 150
     * For values 151 to 200 it will be 200
     * For values 1001 to 1500 it will be 1500
     * Far values 1501 to 2000 it will be 2000
     * ..
     * ..
     * and so on 
     * <AUTHOR> Sharma (<EMAIL>)
     * @param  mixed $value
     * @return int
     */
    public function customRound($value) {
        if ($value >= 0 && $value <= 50) {
            return 50;
        } elseif ($value >= 51 && $value <= 100) {
            return 100;
        } else {
            // Handle other cases as needed
            $digits = strlen($value);
            $minNumber = pow(10, $digits - 1);
            $nextDigitMinNumber = pow(10, $digits);
            $halfOfMinNumber = $minNumber/2;
            for($i=$minNumber; $i<=$nextDigitMinNumber; $i++){
                if($value <= $i){
                    return $i;
                }
                $i = $i + $halfOfMinNumber - 1;
            }
            return $value;
        }
    }

    /**
     * Payment -> Fail payment after given threshold.
     * API EP :{base_url}/user/payment/credential
     * <AUTHOR> Sharma <<EMAIL>>
     */
    public function failPaymentAfterGivenTime(Request $request)
    {
     
        $validator = Validator::make($request->all(), [
            'txn_id' => 'required|alpha_num',
        ], [
            'txn_id.required' => 'Transaction ID is required',
            'txn_id.alpha_num' => 'Only alphabets and numbers are allowed in transaction ID'
        ]);

        if ($validator->fails()) {
            $errorCode = ['txn_id' => 422088];
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $this->errorBag[] = [
                    'code' => $errorCode[$key],
                    'message' => $error[0]
                ];
            }
            return $this->errorResponse(1, $this->errorBag, 422);
        }

        $userId = getUserId();
        
        $this->userActivitiesTracking($userId, 'WAITING_TIME_FINISHED_PAYMENT_FAILED-STARTED - ' . $request->txn_id, [$request->all()]);
        return $this->updatePaymentStatusToFail($request->txn_id,$userId, $request->skip_threshold);
    }
}
