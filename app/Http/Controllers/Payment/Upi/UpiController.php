<?php
namespace App\Http\Controllers\Payment\Upi;

use App\Http\Controllers\Controller;
use App\Traits\AuthenticationTraits;
use Illuminate\Http\Request;
use App\Models\UserAccountDetail;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class UpiController extends Controller
{
    use AuthenticationTraits;

    private function postCurl($endpoint, $headers, $params = []){
        $postFields = json_encode($params);
        array_push($headers, 'Content-Type: application/json', 'Content-Length: ' . strlen($postFields));
        $endpoint = $endpoint . "?";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $returnData = curl_exec($ch);
        curl_close($ch);

        if ($returnData != "") {
            return json_decode($returnData, true);
        }
        return NULL;
    }

    private function getCurl($endpoint, $headers){
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $returnData = curl_exec($ch);
        curl_close($ch);
        if ($returnData != "") {
            return json_decode($returnData, true);
        }
        return NULL;
    }

        /*
    * -----------------------------------------------------------------------------------
    * initiateRequestToCashFree : initiate a request to cash free api for upi verification
    * @param Request $request
    * @return Array subCode,status and message
    * <AUTHOR> Sharma <<EMAIL>>
    * ----------------------------------------------------------------------------------
    */
    public function verifyUpi(Request $request)
    {   
        $userId = getUserId();
        $user = User::select('FIRSTNAME', 'LASTNAME')->where('USER_ID', $userId)->first();
        $name = $user->FIRSTNAME." ".$user->LASTNAME;
        $upiExists = UserAccountDetail::where('USER_ID', '<>' ,$userId)->where('UPI_ID', $request->upi_id)->first();
        if(!empty($upiExists)){
            return [
                'status' => 'failed',
                'message' =>  "UPI ID already exist with another user account",
                'subCode' => 000
            ];
        }
        $userAccountDetails = UserAccountDetail::where('USER_ID', $userId)->first();

        $basePayOutUrl = config('rummy_config.cashfree.payoutUrl');

        $headers = array(
            "X-Client-Id: ".config('rummy_config.cashfree.payoutClientId'),
            "X-Client-Secret: ".config('rummy_config.cashfree.payoutClientSec'),
        );

        $cashFreeAuthorizeUrl =  $basePayOutUrl . "/authorize";

        try{

            $authorizeResp = $this->postCurl($cashFreeAuthorizeUrl, $headers);

            if($authorizeResp['status'] == "SUCCESS"){
                try{
                    $cashFreeTokenVerificationUrl    = $basePayOutUrl . "/verifyToken";
                    $accessToken            = $authorizeResp["data"]["token"];
                    
                    $accessTokenheaders     = array("Authorization: Bearer $accessToken", "Content-Type: application/json");
                    $verifyTokenResponse    = $this->postCurl($cashFreeTokenVerificationUrl, $accessTokenheaders);
                    if($verifyTokenResponse['status'] == 'SUCCESS'){
                        try{
                            $cashFreeUpiVerificationUrl    = $basePayOutUrl ."/validation/upiDetails?name=&vpa=$request->upi_id";
                            $getVerifyUpiResponse = $this->getCurl($cashFreeUpiVerificationUrl, $accessTokenheaders);

                            if(strtoupper($getVerifyUpiResponse['status']) == 'SUCCESS'){
                                if($getVerifyUpiResponse['data']['accountExists'] == "NO"){
                                    return [
                                        'status' => 'failed',
                                        'message' =>  "UPI ID does not exist",
                                        'subCode' => 000
                                    ];
                                }

                                if(!isNameCheckFailed($getVerifyUpiResponse['data']['nameAtBank'], $userId)){
                                    try{
                                        UserAccountDetail::updateOrCreate(['USER_ID' => $userId],['ACCOUNT_HOLDER_NAME' => $getVerifyUpiResponse['data']['nameAtBank'], 'UPI_ID' => $request->upi_id]);
                                        return [
                                            'status' => 'success',
                                            'message' =>  "UPI verification successful",
                                            'subCode' => $getVerifyUpiResponse['subCode']
                                        ];
                                    } catch(\Exception $e){
                                        Log::error($e);
                                        return [
                                            'status' => 'failed',
                                            'message' => 'UPI verification failed',
                                            'subCode' => 000
                                        ];
                                    }
                                    
                                } else {
                                    return [
                                        'status' => 'failed',
                                        'message' => 'Name check failed',
                                        'subCode' => 000
                                    ];
                                }
                                
                            } else {
                                return [
                                    'status' => 'failed',
                                    'message' =>  'UPI verification failed',
                                    'subCode' => $getVerifyUpiResponse['subCode']
                                ];
                            }
                        }
                        catch(\Exception $e){
                            Log::error($e);
                            return [
                                'status' => 'failed',
                                'message' => 'UPI verification failed',
                                'subCode' => 000
                            ];
                        }
                    }
                    else{
                        return [
                            'status' => 'failed',
                            'message' => 'UPI verification failed',
                            'subCode' => $verifyTokenResponse['subCode']
                        ];
                    }
                }
                catch(\Exception $e){
                    Log::error($e);
                    return [
                        'status' => 'failed',
                        'message' => 'UPI verification failed',
                        'subCode' => 000
                    ];
                }
            }
            else{
                return [
                    'status' => 'failed',
                    'message' => 'UPI verification failed',
                    'subCode' => $authorizeResp['subCode']
                ];
            }
        }
        catch(\Exception $e){
            Log::error($e);
            return [
                'status' => 'failed',
                'message' => 'UPI verification failed',
                'subCode' => 000
            ];
        }
    }

}