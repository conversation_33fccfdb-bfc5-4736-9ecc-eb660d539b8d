<?php

namespace App\Http\Controllers\Adjustment;

use App\Http\Controllers\Controller;
use App\Models\AdjustmentTransaction;
use App\Models\CampaignToUser;
use App\Models\MasterTransactionHistory;
use App\Models\MasterTransactionHistoryCoins;
use App\Models\MasterTransactionHistoryFppBonus;
use App\Models\MasterTransactionHistoryPlayMoney;
use App\Models\MasterTransactionHistoryRewardpoints;
use App\Models\User;
use App\Models\UserPoint;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Balance Controller Contains all
 * the adjustment related Methods
 *
 * @category    Adjustment
 * <AUTHOR>
 */
class BalanceController extends Controller
{
    public function adjustBalance(Request $request)
    {
        $authToken = isset(getallheaders()['Auth-Token']) ? getallheaders()['Auth-Token'] : null;
        $apiCode = 1;
        if($authToken != config('rummy_config.CT_WEBHOOK_AUTH_TOKEN')){
            return $this->errorResponse($apiCode, array([
                'code' => 401,
                'message' => "Unauthorized user."
            ]));
        }
        $requestAllowedFromIP = false;
        Log::info([$request->all()]);
        $errorCode = 422000;
        $ip = $this->getRealIpAddr();
        $ctAllowedIPs = explode("|", config('rummy_config.CT_WEBHOOK_IP'));
        if (in_array('0.0.0.0', $ctAllowedIPs) || in_array($ip, $ctAllowedIPs)) {
            $requestAllowedFromIP = true;
        }
        if($requestAllowedFromIP){
            foreach($request->profiles as $key => $profile){
                $userId = $profile['identity'] ?? null;
                if(!empty($userId)){
                    $user = User::where('USER_ID', $userId)->first();
                    if(!empty($user)){
                        $where = [
                            'USER_ID' => $userId,
                            'COIN_TYPE_ID'  => $profile['key_values']['coinType']
                        ];
                        $reasonId = 30; //Goodwill Adjustment
                        $internalRefNo='122'.rand(0,50).rand(51,99).$userId.date('dmYHis');
                        $userPoints = UserPoint::where('USER_ID', $userId)->where('COIN_TYPE_ID', $profile['key_values']['coinType'])->first();
                        $action    =	"Adjustment Promo";
                        $utransactionStatusId = "107";
                        $utransactionTypeId= $profile['key_values']['type'] == "credit"?"254":"255";
                        $adjustmentExist = DB::table('bonus_adjustment_activities')->where([
                            'USER_ID' => $user->USER_ID,
                            'SEGMENT_KEY' => $profile['key_values']['segmentKey'],
                            'ACTIVITY_NAME' => $profile['key_values']['activityname']
                        ])->orderBy('BONUS_ADJUSTMENT_ACTIVITY_ID','DESC')->first();
                        if(!empty($adjustmentExist) && $profile['key_values']['type'] == "debit"){
                            $isGamePlayed = DB::table('vendor_wallet_transfer')
                                            ->whereIn('TRANSACTION_TYPE_ID', config('rummy_config.CREDIT_TXN_TYPE_ID'))
                                            ->where('TRANSACTION_DATE', '>', $adjustmentExist->CREATED_DATE)
                                            ->where('USER_ID', $user->USER_ID)
                                            ->exists();
                            if($isGamePlayed){
                                Log::info("Game played by user - ". $userId.", segement key - ".$profile['key_values']['segmentKey']."  and activity name - ".$profile['key_values']['activityname']);
                                continue;
                            }
                        }
                        if(!empty($adjustmentExist) && $adjustmentExist->TRANSACTION_TYPE_ID == $utransactionTypeId){
                            Log::info("Adjustment already done for given user - ". $userId.", segement key - ".$profile['key_values']['segmentKey']."  and activity name - ".$profile['key_values']['activityname']);
                            continue;
                            // return $this->errorResponse($apiCode, array([
                            //     'code' => $errorCode,
                            //     'message' => "Adjustment already done for given user - ". $userId.", segement key - ".$profile['key_values']['segmentKey']."  and activity name - ".$profile['key_values']['activityname']
                            // ]));
                        } else if(empty($adjustmentExist) && $profile['key_values']['type'] == "debit"){
                            Log::info("There is no credit transaction for given user - ". $userId.", segement key - ".$profile['key_values']['segmentKey']."  and activity name - ".$profile['key_values']['activityname']);
                            continue;
                            // return $this->errorResponse($apiCode, array([
                            //     'code' => $errorCode,
                            //     'message' => "There is no credit transaction for given user - ". $userId.", segement key - ".$profile['key_values']['segmentKey']."  and activity name - ".$profile['key_values']['activityname']
                            // ]));
                        }
                        $data = [];
                        $gratificationId = isset($profile['key_values']['gratificationConfigId']) ? $profile['key_values']['gratificationConfigId'] : null;
                        $gratificationData = DB::table('gratification_configuration')->where('GRATIFICATION_ID', $gratificationId)->first();
                        if(empty($gratificationData) || $gratificationData->GRATIFICATION_TYPE != 3){ //3 => free game
                            if($profile['key_values']['coinType'] == 1){ //Cash
                                if($profile['key_values']['balanceType'] == 1){//Deposit
                                    if($profile['key_values']['type'] == "credit"){
                                        $data['USER_DEPOSIT_BALANCE'] = $userPoints->USER_DEPOSIT_BALANCE +  $profile['key_values']['amount'];
                                        $data['VALUE'] = $profile['key_values']['amount'];
                                        $data['USER_TOT_BALANCE'] = $userPoints->USER_TOT_BALANCE + $profile['key_values']['amount'];
                                        $calcSymbol  = '+';
                                    } else {
                                        if($profile['key_values']['amount'] > $userPoints->USER_DEPOSIT_BALANCE){
                                            $data['USER_DEPOSIT_BALANCE'] = 0;
                                            $data['VALUE'] = $userPoints->USER_DEPOSIT_BALANCE;
                                            $data['USER_TOT_BALANCE'] = $userPoints->USER_TOT_BALANCE - $userPoints->USER_DEPOSIT_BALANCE;
                                            $calcSymbol  = '-';
                                        } else{
                                            $data['USER_DEPOSIT_BALANCE'] = $userPoints->USER_DEPOSIT_BALANCE - $profile['key_values']['amount'];
                                            $data['VALUE'] = $profile['key_values']['amount'];
                                            $data['USER_TOT_BALANCE'] = $userPoints->USER_TOT_BALANCE - $profile['key_values']['amount'];
                                            $calcSymbol  = '-';
                                        }
                                    }
                                } else if($profile['key_values']['balanceType'] == 2){//Promo
                                    if($profile['key_values']['type'] == "credit"){ 
                                        $data['USER_PROMO_BALANCE'] = $userPoints->USER_PROMO_BALANCE +  $profile['key_values']['amount'];
                                        $data['VALUE'] = $profile['key_values']['amount'];
                                        $data['USER_TOT_BALANCE'] = $userPoints->USER_TOT_BALANCE + $profile['key_values']['amount'];
                                        $calcSymbol  = '+';
                                    } else {
                                        if($profile['key_values']['amount'] > $userPoints->USER_PROMO_BALANCE){
                                            $data['USER_PROMO_BALANCE'] = 0;
                                            $data['VALUE'] = $userPoints->USER_PROMO_BALANCE;
                                            $data['USER_TOT_BALANCE'] = $userPoints->USER_TOT_BALANCE - $userPoints->USER_PROMO_BALANCE;
                                            $calcSymbol  = '-';
                                        } else{
                                            $data['USER_PROMO_BALANCE'] = $userPoints->USER_PROMO_BALANCE - $profile['key_values']['amount'];
                                            $data['VALUE'] = $profile['key_values']['amount'];
                                            $data['USER_TOT_BALANCE'] = $userPoints->USER_TOT_BALANCE - $profile['key_values']['amount'];
                                            $calcSymbol  = '-';
                                        }
                                    }
                                } else if($profile['key_values']['balanceType'] == 3){//Win
                                    if($profile['key_values']['type'] == "credit"){
                                        $data['USER_WIN_BALANCE'] = $userPoints->USER_WIN_BALANCE +  $profile['key_values']['amount'];
                                        $data['VALUE'] = $profile['key_values']['amount'];
                                        $data['USER_TOT_BALANCE'] = $userPoints->USER_TOT_BALANCE + $profile['key_values']['amount'];
                                        $calcSymbol  = '+';
                                    } else {
                                        if($profile['key_values']['amount'] > $userPoints->USER_WIN_BALANCE){
                                            $data['USER_WIN_BALANCE'] = 0;
                                            $data['VALUE'] = $userPoints->USER_WIN_BALANCE;
                                            $data['USER_TOT_BALANCE'] = $userPoints->USER_TOT_BALANCE - $userPoints->USER_WIN_BALANCE;
                                            $calcSymbol  = '-';
                                        } else{
                                            $data['USER_WIN_BALANCE'] = $userPoints->USER_WIN_BALANCE - $profile['key_values']['amount'];
                                            $data['VALUE'] = $profile['key_values']['amount'];
                                            $data['USER_TOT_BALANCE'] = $userPoints->USER_TOT_BALANCE - $profile['key_values']['amount'];
                                            $calcSymbol  = '-';
                                        }
                                    }
                                }
                            } else {
                                $profile['key_values']['balanceType'] = 2;
                                if($profile['key_values']['type'] == "credit"){ //Cash
                                    $data['USER_PROMO_BALANCE'] = $userPoints->USER_PROMO_BALANCE +  $profile['key_values']['amount'];
                                    $data['VALUE'] = $profile['key_values']['amount'];
                                    $data['USER_TOT_BALANCE'] = $userPoints->USER_TOT_BALANCE + $profile['key_values']['amount'];
                                    $calcSymbol  = '+';
                                } else {
                                    if($profile['key_values']['amount'] > $userPoints->USER_PROMO_BALANCE){
                                        $data['USER_PROMO_BALANCE'] = 0;
                                        $data['VALUE'] = $userPoints->USER_PROMO_BALANCE;
                                        $data['USER_TOT_BALANCE'] = $userPoints->USER_TOT_BALANCE - $userPoints->USER_PROMO_BALANCE;
                                        $calcSymbol  = '-';
                                    } else{
                                        $data['USER_PROMO_BALANCE'] = $userPoints->USER_PROMO_BALANCE - $profile['key_values']['amount'];
                                        $data['VALUE'] = $profile['key_values']['amount'];
                                        $data['USER_TOT_BALANCE'] = $userPoints->USER_TOT_BALANCE - $profile['key_values']['amount'];
                                        $calcSymbol  = '-';
                                    }
                                }
                            }
                            $CLOSING_TOT_BALANCE = $data['USER_TOT_BALANCE'];
                            $response = $this->finalInsert($where,$data,$profile,$utransactionTypeId,$internalRefNo,$utransactionStatusId,$CLOSING_TOT_BALANCE,$calcSymbol,$userPoints,$action,$reasonId,$user);
                        } else {
                            if($profile['key_values']['coinType'] == 1){
                                $gratificationUser = DB::table('gratification_users')->where([
                                    'USER_ID' => $user->USER_ID,
                                    'SEGMENT_KEY' => $profile['key_values']['segmentKey'],
                                    'ACTIVITY_NAME' => $profile['key_values']['activityname']
                                ])->first();
                                if(empty($gratificationUser)){
                                    DB::table('gratification_users')->insert([
                                        'GRATIFICATION_ID' => $gratificationId,
                                        'USER_ID' => $userId,
                                        'COIN_TYPE_ID' => $profile['key_values']['coinType'],
                                        'BALANCE_TYPE_ID' => $profile['key_values']['balanceType'],
                                        'SEGMENT_KEY' => $profile['key_values']['segmentKey'],
                                        'ACTIVITY_NAME' => $profile['key_values']['activityname'],
                                        'EXPIRY_DATETIME' => Carbon::now()->addHour($gratificationData->VALIDITY)
                                    ]);
                                } else {
                                    Log::info("Free game already given to user - ". $userId.", segement key - ".$profile['key_values']['segmentKey']."  and activity name - ".$profile['key_values']['activityname']);
                                    continue;
                                    // return $this->errorResponse($apiCode, array([
                                    //     'code' => $errorCode,
                                    //     'message' => "Free game already given to user - ". $userId.", segement key - ".$profile['key_values']['segmentKey']."  and activity name - ".$profile['key_values']['activityname']
                                    // ]));
                                }
                                
                            }
                            $response = response()->json(['status'=>200, 'message'=>'Amount updated successfully']);
                        }
                        $eventName = $profile['key_values']['type'] == "credit" ? 'Bonus reward added' : 'Bonus reward deducted';
                        if(!empty($gratificationData) && $utransactionTypeId == "254" && $profile['key_values']['coinType'] == 1){
                            $eventName = "Gratification";
                            $data = [
                                'GRATIFICATION_TYPE' => $gratificationData->GRATIFICATION_TYPE,
                                'TIP' => $gratificationData->TIP,
                                'GAME_ID' => $gratificationData->GAME_ID,
                                'GAME_SUB_TYPE_ID' => $gratificationData->GAME_SUB_TYPE_ID,
                                'POINT_VALUE' => $gratificationData->GAME_SUB_TYPE_ID == 25 ? $gratificationData->LOBBY_AMOUNT/80 : null,
                                'FORMAT' => $gratificationData->FORMAT,
                                'AMOUNT' => $gratificationData->LOBBY_AMOUNT ?? $profile['key_values']['amount'],
                                'EXPIRY_DATE' => Carbon::now()->addHour($gratificationData->VALIDITY),
                                'GAME_IMAGE' => $gratificationData->GAME_IMAGE
                            ];
                            $this->sendDataToCardbaaziRealTimeEventsTable($userId, "Gratification Data", $data);
                        }

                        $reason = DB::table('action_reasons')->where('ACTIONS_REASONS_ID',$reasonId)->first();
                        $rewardTypeList = Cache::rememberForever('coin-reward-type-list', function () {
                            return DB::table('reward_types')->select('REWARD_NAME as NAME', 'REWARD_TYPE_ID', 'COIN_TYPE_ID', 'BALANCE_TYPE_ID')->where('STATUS', 1)->get();
                        });
                        $rewardType = $rewardTypeList->where('COIN_TYPE_ID', $profile['key_values']['coinType'])->where('BALANCE_TYPE_ID', $profile['key_values']['balanceType'])->first();

                        // CT events data
                        $data = [
                            "type"	 	=> "event",
                            "type_name" => $eventName,
                            "data" 		=> [
                                "identity" => $user->USER_ID,
                                "Reward Type" => $rewardType->NAME ?? "",
                                "Amount" => $profile['key_values']['amount'],
                                "TXN id" => $internalRefNo,
                                "Reason" => $reason->ACTIONS_REASON ?? "",
                                "Activity Name" => $profile['key_values']['activityname'] ?? ""
                            ]
                        ];
                        $this->putOnKinesis($data);
                        // return $response;
                    } else {
                        Log::info("User not found.");
                        // return $this->errorResponse($apiCode, array([
                        //     'code' => $errorCode,
                        //     'message' => "User not found."
                        // ]));
                    }
                } else {
                    Log::info("User id not available.");
                    // return $this->errorResponse($apiCode, array([
                    //     'code' => $errorCode,
                    //     'message' => "User id not available."
                    // ]));
                }
                //Breaking this loop because we are handling the first data of given array this time
                // break;
            }
            return response()->json(['status'=>200, 'message'=>'Amount updated successfully']);
        } else {
            Log::info("Transction came from invalid IP.");
            return $this->errorResponse($apiCode, array([
                'code' => $errorCode,
                'message' => 'Transction came from invalid IP.'
            ]));
        }
        
    }

    public function finalInsert($where,$data,$profile,$utransactionTypeId,$internalRefNo,$utransactionStatusId,$CLOSING_TOT_BALANCE,$calcSymbol,$userPoints,$action,$reasonId,$user){
        DB::transaction(function() use ($where,$data,$profile,$utransactionTypeId,$internalRefNo,$utransactionStatusId,$CLOSING_TOT_BALANCE,$calcSymbol,$userPoints,$action,$reasonId,$user){
            
            UserPoint::where($where)->update($data);
            $this->insertAdjustmentTransaction($insertAdjustdata = [
                'USER_ID'  => $user->USER_ID,
                'REASON_ID' => $reasonId,
                'TRANSACTION_TYPE_ID'  => $utransactionTypeId,
                'INTERNAL_REFERENCE_NO'  => $internalRefNo,
                'ADJUSTMENT_CREATED_BY'  => 'admin',
                'ADJUSTMENT_CREATED_ON'  => date('Y-m-d H:i:s'),
                'ADJUSTMENT_AMOUNT'  => $data['VALUE'],
                'ADJUSTMENT_ACTION'  => ($profile['key_values']['type'] == "credit")?'Add':'Subtract',
                'ADJUSTMENT_COMMENT'  => '',
                'COIN_TYPE_ID'  => $profile['key_values']['coinType']
            ]);
            $this->insertMasterTransactionHistory($profile['key_values']['coinType'], $insertData=[
                'USER_ID' => $user->USER_ID,
                'BALANCE_TYPE_ID' => $profile['key_values']['balanceType'],
                'TRANSACTION_STATUS_ID' => $utransactionStatusId,
                'TRANSACTION_TYPE_ID' => $utransactionTypeId,
                'TRANSACTION_AMOUNT' => $data['VALUE'],
                'TRANSACTION_DATE' => date('Y-m-d H:i:s'),
                'INTERNAL_REFERENCE_NO' => $internalRefNo,
                'CURRENT_TOT_BALANCE' => $userPoints->USER_TOT_BALANCE ?? 0,
                'CLOSING_TOT_BALANCE' => $CLOSING_TOT_BALANCE,
                'PARTNER_ID' => $user->fk_partner_id
            ]);

            DB::table('bonus_adjustment_activities')->insert([
                'USER_ID' => $user->USER_ID,
                'TRANSACTION_TYPE_ID' => $utransactionTypeId,
                'INTERNAL_REFERENCE_NO' => $internalRefNo,
                'BALANCE_TYPE_ID' => $profile['key_values']['balanceType'],
                'COIN_TYPE_ID'  => $profile['key_values']['coinType'],
                'ADJUSTMENT_AMOUNT' => $data['VALUE'],
                'SEGMENT_KEY' => $profile['key_values']['segmentKey'],
                'ACTIVITY_NAME' => $profile['key_values']['activityname']
            ]);
            
        }, 5);
        return response()->json(['status'=>200, 'message'=>'Amount updated successfully']);
    }

    // Insert Adjustment Transaction 
    public function insertAdjustmentTransaction($data){
        $data = new AdjustmentTransaction($data);
        return $data->save();
    }

    // Insert data in master Transaction History
    public function insertMasterTransactionHistory($coinType, $data){ 
        if($coinType == 3){
            CampaignToUser::create([
                'PROMO_CAMPAIGN_ID' => 0,
                'USER_ID' => $data['USER_ID'],
                'BONUSE_TYPE' => config('rummy_config.FPP_BONUS_ADJUSTMENT.BONUSE_TYPE'),
                'BONUS_AMOUNT' => $data['TRANSACTION_AMOUNT'],
                'BALANCE_BONUS_AMOUNT' => $data['TRANSACTION_AMOUNT'],
                'BONUS_RELEASE_PER' => config('rummy_config.FPP_BONUS_ADJUSTMENT.BONUS_RELEASE_PER'),
                'REDEEM_DATE' => $data['TRANSACTION_DATE'],
                'EXPIRY_DATE' => Carbon::parse($data['TRANSACTION_DATE'])->addDays(config('rummy_config.FPP_BONUS_ADJUSTMENT.EXPIRY_DAYS')),
                'BONUS_STATUS' => 1,
                'STATUS' => 1,
                'COIN_TYPE_ID' => $coinType
            ]);
            $data = new MasterTransactionHistoryFppBonus($data);
            return $data->save();
        } else if($coinType == 12){
            $data = new MasterTransactionHistoryRewardpoints($data);
            return $data->save();
        } else if($coinType == 2){
            $data = new MasterTransactionHistoryPlayMoney($data);
            return $data->save();
        } else if($coinType == 1){
            $data = new MasterTransactionHistory($data);
            return $data->save();
        }
    }
}