<?php

namespace App\RummyBaazi;



/**
 * This file used for the DyanmoDB Facades 
 * 
 * Here defined all the method which need to 
 * access the out the app, can use anywhere with the 
 * help of \DyanmoDB Facade
 * 
 * Class DyanmoDB
 * 
 * @package     DyanmoDB
 * @copyright   RummyBaazi
 * <AUTHOR>
 */

class DynamoDB
{

    protected $client;

    public function __construct()
    {
        $this->createConnection();
    }

    protected function createConnection()
    {
        if (!$this->client) {
            $aws = config("dynamodb.connections.aws");

            $config = [
                'region' => $aws['region'],
                'version' => 'latest',
            ];

            // Added Profile key if using named profile on server
            if (!empty($aws['profile'])) {
                $config['profile'] = $aws['profile'];
            }

            $sdk = new \Aws\Sdk($config);
            $this->client = $sdk->createDynamoDb();
        }
    }

    public function getConnection()
    {
        $this->createConnection();
        return $this->client;
    }
}
