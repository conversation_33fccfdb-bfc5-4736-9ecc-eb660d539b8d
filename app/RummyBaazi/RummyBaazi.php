<?php

namespace App\RummyBaazi;

use App\Models\User;
use App\RummyBaazi\Traits\Miscellaneous;
use Auth;
use App\Http\Controllers\Controller;
use App\RummyBaazi\Traits\Authentication as RummyBaaziAuthTraits;
use App\RummyBaazi\Traits\MethodHelpers;
use App\Traits\Models\CommonQueries;
use Illuminate\Support\Facades\Log;
use DB;

/**
 * This file used for the RummyBaazi Facades
 *
 * Here defined all the method which need to
 * access the out the app, can use anywhere with the
 * help of \RummyBaazi Facade
 *
 * @example \RummyBaazi::storeActivityWithParams($action, $data, $module_id=null)
 *
 * Class RummyBaazi
 *
 * @package     RummyBaazi
 * @copyright   RummyBaazi
 * <AUTHOR>
 */
class RummyBaazi extends Controller
{

    use MethodHelpers,Miscellaneous;

    public static $user;


    public function __construct()
    {

    }

    public function test()
    {
        dd("working");
    }

    public function user()
    {
        if (empty(static::$user)) {
            static::$user = User::find(getUserId());
        }
        return static::$user;
    }
}
