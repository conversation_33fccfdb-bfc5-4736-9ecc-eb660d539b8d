<?php

namespace App\RummyBaazi\Traits;

use App\Models\SiteJincNewsLetter;
use Illuminate\Support\Facades\Log;

trait Miscellaneous
{
    public function getMailTemplate($tempId)
    {
        $mailTemplate = SiteJincNewsletter::where('email_key',$tempId)->first();
        if (!empty($mailTemplate)) {
            return $mailTemplate;
        }else{
            Log::error("Failed to send mail: Inavalid email_key=$tempId");
            return;
        }
    }
}
