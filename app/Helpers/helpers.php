<?php

use App\Models\GlobalConfig;
use App\Models\PaymentTransaction;
use App\Models\User;

if (!function_exists('getUserId')) {
    function getUserId($user = null)
    {
        if ($user) {
            return $user->USER_ID;
        }
        $token = app('request')->bearerToken();
        if ($token != '') {
            $config = \Lcobucci\JWT\Configuration::forUnsecuredSigner();
            $token = $config->parser()->parse($token);
            return $token->claims()->get('cst_uid');
        } else
            return null;
    }
}

if (!function_exists('isNameCheckFailed')) {
    function isNameCheckFailed($name, $userId)
    {
        /* if name response is not empty */
        $user = User::select('FIRSTNAME', 'LASTNAME')->where('USER_ID', $userId)->first();

        $verifiedAddressName = $user->FIRSTNAME . " " . $user->LASTNAME;

        similar_text(strtolower("$verifiedAddressName"), strtolower($name), $percent);

        /* name check failed  */

        if ($percent < config('rummy_config.nameMatchPercentage')) {
            /* error found */
            return true;
        } else {
            return false;
        }
    }
}

if (!function_exists('getLedgerConfig')) {
    function getLedgerConfig()
    {
        $ledgerConfig = GlobalConfig::whereIn('CONFIG_KEY', ['tds_financial_year_start', 'tds_exemption'])->pluck('CONFIG_VALUE', 'CONFIG_KEY')->toArray();
        return $ledgerConfig;
    }
}

if (!function_exists('getSuccessfulDepositCount')) {
    function getSuccessfulDepositCount($userID)
    {
        $transactionVariablesArr = config('rummy_config.transactionVariables');
        return PaymentTransaction::where('USER_ID', $userID)
            ->whereIn('PAYMENT_TRANSACTION_STATUS', [
                $transactionVariablesArr['transactionSuccessPaymentStatus'],
                $transactionVariablesArr['depositOkSuccessPaymentStatus']
            ])->count();;
    }
}
