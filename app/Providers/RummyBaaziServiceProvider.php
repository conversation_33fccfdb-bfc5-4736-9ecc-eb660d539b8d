<?php

namespace App\Providers;

/**
 * RummyBaazi Service Provider
 *
 * RummyBaazi Service Provider is used for Mange Global
 * Method and Manage Custom singleton class, manage other
 * service used for RummyBaazi related thing.
 *
 * Class RummyBaaziServiceProvider
 *
 * @copyright   RummyBaazi
 * <AUTHOR>
 * Created At:  03/01/2020
 */

use App\RummyBaazi\DynamoDB;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Collection;
use App\RummyBaazi\RummyBaazi;

use Illuminate\Support\Str;

class RummyBaaziServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton('rummybaazi', function () {
            return new RummyBaazi();
        });

        $this->app->bind('dynamodb', function () {
            return new DynamoDB();
        });

        $this->loadHelpers();
        $this->customMacros();
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {

    }

    /**
     * Custom Macros created to access or use method from
     * with there objects, used macros feature of laravel
     * <AUTHOR>
     * Created At: 01/03/2020
     *
     * @return void
     */
    public function customMacros(){
        /**
         * @example collect(['NITIN_SHARMA'])->attributeTitleCase()
         * it will give in response "NITIN SHARMA"
         * that will used for Validation because validation change
         * Capital Named field to space exploaded
         * @example NITIN to n i t i n
         */
        Collection::macro('attributeTitleCase', function () {
            return $this->map(function ($value) {
                return str_replace("_"," ",Str::title($value));
            });
        });
    }

    /**
     * Load all the helper file contain app Helpers folder
     * to access global function
     * <AUTHOR> Sharma<<EMAIL>>
     * Created At: 01/03/2020
     *
     * @return void
     */
    protected function loadHelpers()
    {
        foreach (glob(base_path("app/Helpers/*.php")) as $filename) {
            require_once $filename;
        }
    }
}
