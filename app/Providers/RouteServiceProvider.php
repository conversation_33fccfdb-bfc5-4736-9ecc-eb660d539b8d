<?php

namespace App\Providers;

/**
 * Route Service Provider
 *
 * These Provider is used to manage Route file
 * Lumen Not having Route service Provider So create
 * custom RouteService Provider for Manage Route files
 *
 * Class RouteServiceProvider
 *
 * @copyright   RummyBaazi
 * <AUTHOR>
 * Created At:  01/03/2020
 */

use Illuminate\Support\ServiceProvider;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * This namespace is applied to your controller routes.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'App\Http\Controllers';

    /**
     * Define your route model bindings, pattern filters, etc.
     *
     * @return void
     */

    public function boot()
    {
        $this->map();
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map()
    {
        $this->mapApiRoutes();
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapApiRoutes()
    {
        $this->app->router->group([
            'namespace' => $this->namespace,
        ], function ($router) {
            require base_path('routes/api.php');
        });
    }
}
