<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * @var int $custom_alias contain all,
     * the custom loaded alias list
     */
    protected $custom_alias=[];

    /**
     * @var int $custom_config contain all,
     * the custom loaded configurations list
     */
    protected $custom_config=[];
    
    /**
     * @var int $custom_alias contain all,
     * the custom loaded providers list
     */
    protected $custom_providers=[];

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->loadCustomAlias();
        $this->loadCustomConfigrations();
        $this->loadCustomProviders();
    }

    /**
     * custom Alias or Facades
     * <AUTHOR>
     * 
     * @return void
     */
    protected function loadCustomAlias(){
        $custom_aliases = config('app.custom_aliases');
        foreach($custom_aliases as $original => $alias){
            if(!$this->app->isAlias($original)){
                class_alias($alias, $original);
                $this->app->alias($original, $alias);
                $this->custom_alias[$original] = $alias;
            }
        }
    }

    /**
     * custom Configration
     * <AUTHOR>
     * 
     * @return void
     */
    protected function loadCustomConfigrations(){
        foreach (glob(base_path("config/*.php")) as $filename) {
            $name = explode('.',basename($filename))[0];
            $this->app->configure($name);
            $this->custom_config[]=$name;
        }
    }

    /**
     * custom_providers
     * <AUTHOR> Sharma<<EMAIL>>
     * 
     * @return void
     */
    protected function loadCustomProviders(){
        $custom_providers = config('app.custom_providers');
        foreach($custom_providers as $key => $provider){
            $this->app->register($provider);
            $this->custom_providers[]=$provider;
        }
    }
}
