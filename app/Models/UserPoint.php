<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserPoint extends Model
{
    protected $table ="user_points";

    protected $primaryKey = 'USER_POINTS_ID';
    public $timestamps = ["updated_at"];

    const UPDATED_AT = 'UPDATED_DATE';

    protected $fillable = ['USER_POINTS_ID', 'COIN_TYPE_ID', 'USER_ID', 'VALUE', 'USER_DEPOSIT_BALANCE', 'USER_PROMO_BALANCE', 'USER_WIN_BALANCE', 'USER_TOT_BALANCE', 'UPDATED_DATE'];
	
}
