<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PaymentTransactionRummycommission extends Model
{
    protected $table = 'payment_transaction_rummycommission';
    protected $primaryKey = 'PAYMENT_TRANSACTION_ID';
    const CREATED_AT = 'PAYMENT_TRANSACTION_CREATED_ON';
    const UPDATED_AT = 'UPDATED_DATE';

    protected $fillable = ['USER_ID', 'PAYMENT_PROVIDER_ID', 'TRANSACTION_TYPE_ID', 'PAYMENT_TRANSACTION_AMOUNT', 'PAYMENT_TRANSACTION_STATUS', 'PAYMENT_TRANSACTION_CREATED_BY', 'PAYMENT_TRANSACTION_CREATED_ON', 'BANK_REFERENCE_NO', 'INTERNAL_REFERENCE_NO', 'TRANSACTION_TOKEN', 'PAYPAL_RETURN_VALUES', 'WITHDRAW_BY', 'WITHDRAW_TYPE', 'PROMO_CODE', 'TOPUP_CONFIG_ID', 'UPDATED_DATE', 'TRANASACTION_OTP', 'APPROVE_BY_CASHFREE_STATUS', 'TRANSFER_ID', 'APPROVE_BY_PAYU_STATUS', 'PAYU_TRANSFER_ID', 'GATEWAY_ID', 'APPROVE_BY_RBL_STATUS', 'RBL_TRANSFER_ID', 'RRN_NO', 'CHECKING_STATUS', 'CHECKING_COMMENT', 'APPROVE_TYPE', 'SETTLEMENT'];

}
