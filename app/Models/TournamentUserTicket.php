<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TournamentUserTicket extends Model
{
    protected $table ="tournament_user_ticket";

    protected $primaryKey = 'TOURNAMENT_USER_TICKET_ID';

    public $timestamps = ["updated_at"];

    const UPDATED_AT = 'UPDATED';
    const CREATED_AT = 'CREATED';

    protected $guarded = [];

    public function tournament()
    {
        return $this->belongsTo('App\Models\Tournament','TOURNAMENT_ID','TOURNAMENT_ID');
    }

	
}
