<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Tracking extends Model {

    protected $table = "tracking";
    protected $primaryKey ="TRACKING_ID";

    protected $fillable = ["USER_ID", "USERNAME","ACTION_NAME","DATE_TIME","SYSTEM_IP","REFERRENCE_NO","SYSTEM_MAC","STATUS","LOGIN_STATUS","UPDATED_DATE","DEVICE_TYPE","APP_TYPE","DEVICE_MODEL","BROWSER_NAME","OPERATING_SYSTEM","SCREEN_SIZE","FINGERPRINT_ID","PACK_VERSION","BUILD_VERSION"];

    const CREATED_AT = "DATE_TIME";
    const UPDATED_AT = "UPDATED_DATE";
}
