<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Model;

class CashoutApproveTransaction extends Model
{
    protected $table = "cashout_approve_transaction";
    protected $primaryKey = "CASHOUT_TRANSACTTION_ID";

    protected $fillable = ['CASHOUT_TRANSACTTION_ID', 'USER_ID', 'INTERNAL_REFERENCE_NO', 'TRANSACTION_AMOUNT', 'GATEWAY_REFERENCE_NO', 'GATEWAY_STATUS', 'ACCOUNT_HOLDER_NAME', 'ACCOUNT_NUMBER', 'IFSC_CODE', 'GATEWAY_REQUEST', 'GATEWAY_RESPONSE', 'GATEWAY_MESSAGE', 'TYPE', 'REQUEST_DATE', 'RESPONSE_DATE', 'UPDATED_DATE'];

    public $timestamps = false;

}
