<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RFTransactionHitory extends Model
{
    protected $table = 'raf_transaction_history';
    protected $primaryKey = 'RAF_TRANSACTION_HISTORY_ID';
    public $timestamps = false;
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'USER_ID', 'REFERRAL_USER_ID', 'TRANSACTION_STATUS_ID', 'TRANSACTION_TYPE_ID', 'BALANCE_TYPE_ID', 'RAF_TRANSACTION_AMOUNT', 'INTERNAL_REFERENCE_NO', 'PARTNER_ID', 'ACTIVITY_ID', 'TRANSACTION_AMOUNT'
    ];
}
