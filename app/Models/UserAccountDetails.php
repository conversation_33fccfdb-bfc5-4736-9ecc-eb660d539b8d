<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserAccountDetails extends Model
{
	protected $table = 'user_account_details';

    protected $primaryKey = 'USER_ACCOUNT_ID';
	public $timestamps = false;

	protected $fillable = ["USER_ACCOUNT_ID", "USER_ID", "ACCOUNT_HOLDER_NAME", "ACCOUNT_TYPE", "ACCOUNT_NUMBER", "MICR_CODE", "IFSC_CODE", "BANK_NAME", "BRANCH_NAME", "UPDATED_DATE"];
}
