<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CampaignToUser extends Model
{
    protected $table = 'campaign_to_user';

    protected $primaryKey = 'CAMPAIGN_TO_USER_ID';

    protected $fillable = ["PROMO_CAMPAIGN_ID", "USER_ID", "<PERSON><PERSON><PERSON><PERSON><PERSON>_CODE", "<PERSON><PERSON><PERSON><PERSON>N_NAME", "BONUSE_TYPE", "BONUS_AMOUNT", "BALANCE_BONUS_AMOUNT", "BONUS_RELEASE_PER", "REDEEM_DATE", "EXPIRY_DATE", "BONUS_STATUS", "STATUS", "COIN_TYPE_ID", "UPDATED_DATE"];

    const UPDATED_AT = 'UPDATED_DATE';
    public $timestamps = false;

}
