<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PaymentTransaction extends Model
{
    protected  $table = 'payment_transaction';
	protected $primaryKey = 'PAYMENT_TRANSACTION_ID';
	public $timestamps = false;

	protected $fillable = ['USER_ID','PAYMENT_PROVIDER_ID','TRANSACTION_TYPE_ID','PAYMENT_TRANSACTION_AMOUNT','TOTAL_AMOUNT','DISC_GATEWAY_FEE','DISC_GATEWAY_FEE_WITH_GST','GATEWAY_FEE','GATEWAY_FEE_WITH_GST','PAYMENT_TRANSACTION_STATUS','PAYMENT_TRANSACTION_CREATED_BY','PAYMENT_TRANSACTION_CREATED_ON','BANK_REFERENCE_NO','INTERNAL_REFERENCE_NO','PROMO_CODE','PAYPAL_RETURN_VALUES','APP_TYPE'];

	protected $casts = [
        'PG_REFERENCE_NOS' => 'array'
    ];
}