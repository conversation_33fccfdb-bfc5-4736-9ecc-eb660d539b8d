<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class User<PERSON>yc extends Model
{
    protected $table = 'USER_KYC';

    protected $primaryKey = 'USER_KYC_ID';

    protected $fillable = ["USER_KYC_ID", "USER_ID", "<PERSON><PERSON><PERSON>AR_FRONT_URL", "AAD<PERSON><PERSON>_BACK_URL", "AADHAAR_NUMBER", "AA<PERSON><PERSON><PERSON>_FRONT_REQUEST_ID", "AAD<PERSON>AR_FRONT_RESPONSE", "AADHAAR_STATUS", "AAD<PERSON>AR_COMMENT", "PAN_NUMBER", "PAN_URL", "NON_PAN_URL", "PAN_NUMBER_REQUEST_ID", "PAN_NUMBER_RESPONSE", "PAN_STATUS", "PAN_COMMENT", "OTHER_DOC_NUMBER", "OTHER_DOC_URL", "OTHER_DOC_URL_BACK", "OTHER_DOC_STATUS", "O<PERSON>ER_DOC_COMMENT", "CREATED_DATE", "UPDATE_DATE"];
	
	const CREATED_AT = 'CREATED_DATE';
    const UPDATED_AT = 'UPDATE_DATE';
}
