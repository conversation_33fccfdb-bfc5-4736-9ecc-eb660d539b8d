<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ResponsibleGameSetting extends Model {

    const CREATED_AT = 'OFC_UPDATED_DATE';
    const UPDATED_AT = 'OFC_UPDATED_DATE';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'responsible_game_settings';
    /**
     * The primary key associated with the table.
     *
     * @var Integer
     */
    protected $primaryKey = 'RESPONSIBLE_GAME_SETTINGS_ID';

    protected $guarded = [];

}
