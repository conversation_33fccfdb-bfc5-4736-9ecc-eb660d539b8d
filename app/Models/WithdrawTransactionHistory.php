<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WithdrawTransactionHistory extends Model
{

    protected $table = "withdraw_transaction_history";
    protected $primaryKey = "WITHDRAW_TRANSACTTION_ID";

    protected $fillable = ['WITHDRAW_TRANSACTTION_ID', 'USER_ID', 'BALANCE_TYPE_ID', 'TRANSACTION_STATUS_ID', 'TRANSACTION_TYPE_ID', 'WITHDRAW_AMOUNT', 'WITHDRAW_TDS', 'WITHDRAW_FEE', 'PAYABLE_AMOUNT', 'FEE_WAIVED', 'PAID_AMOUNT', 'INTERNAL_REFERENCE_NO', 'GATEWAY_REFERENCE_NO', 'WITHDRAW_TYPE', 'TRANSACTION_DATE', 'APPROVED_AT', 'APPROVED_BY', 'APPROVE_TYPE', 'GATEWAY_STATUS', 'CHECKING_STATUS', 'CHECKING_COMMENT', 'UPDATED_ON','AMOUNT_BREAKUP_DETAILS','APP_TYPE'];

    public $timestamps = false;
}
