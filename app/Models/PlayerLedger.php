<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Model;

class PlayerLedger extends Model
{
    protected $table = 'player_ledger';
    public $timestamps = false;
    protected $fillable = ['PLAYER_LEDGER_ID', 'USER_ID', 'ACTION', 'TRANSACTION_AMOUNT', 'INTERNAL_REFERENCE_NO', 'TOTAL_DEPOSITS', 'TOTAL_WITHDRAWALS', 'TOTAL_TAXABLE_WITHDRAWALS', 'ELIGIBLE_WITHDRAWAL_WITHOUT_TAX', 'EXEMPTION_10K', 'TOTAL_ELIGIBLE_WITHDRAWAL_WITHOUT_TAX', 'PAYMENT_TRANSACTION_CREATED_ON', 'CREATED_DATE', 'UPDATED_DATE'];
}
