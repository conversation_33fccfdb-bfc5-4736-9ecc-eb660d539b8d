<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DepositLimitUser extends Model
{

    protected $table = "deposit_limit_user";
    protected $primaryKey = "DEPOSIT_LIMIT_USER_ID";

    protected $fillable = ["DEPOSIT_LIMIT_USER_ID", "USER_ID", "DEP_LEVEL", "DEP_AMOUNT", "DEP_AMOUNT_PER_DAY", "TRANSACTIONS_PER_DAY", "DEP_AMOUNT_PER_WEEK", "TRANSACTIONS_PER_WEEK", "DEP_AMOUNT_PER_MONTH", "TRANSACTIONS_PER_MONTH", "UPDATED_STATUS", "UPDATED_ON", "CREATED_BY", "UPDATED_BY", "CREATED_DATE"];

    public $timestamps = false;
}
