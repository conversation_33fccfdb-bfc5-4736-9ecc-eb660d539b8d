<?php
namespace App\Models;
use Illuminate\Database\Eloquent\Model;
class DepositGstInvoice extends Model
{
    protected $table='deposit_gst_invoices';
    protected $primaryKey = 'ID';
    public $timestamps = false;
    protected $fillable = ['USER_ID', 'INTERNAL_REFERENCE_NO', 'USERNAME', 'STATE', 'STATE_CODE', 'GSTIN_NO', 'INVOICE_NO', 'PAYMENT_REFERENCE', 'SERVICE_DESCRIPTION', 'HSN_SAC_CODE', 'TAXABLE_VALUE', 'CGST_AMOUNT', 'SGST_AMOUNT', 'IGST_AMOUNT', 'SUB_TOTAL', 'PROCESSING_FEE', 'CGST_ON_PROCESSING_FEE', 'SGST_ON_PROCESSING_FEE', 'IGST_ON_PROCESSING_FEE', 'TOTAL_PAID', 'DEPOSIT_GST_RATE', 'PROCESSING_FEE_GST_RATE', 'CREATED_DATE', 'GATEWAY_HSN_CODE'];
	
}