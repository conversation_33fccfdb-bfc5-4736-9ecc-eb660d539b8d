<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Tournament extends Model
{
    protected $table ="tournament";

    protected $primaryKey = 'TOURNAMENT_ID';

    public $timestamps = ["updated_at"];

    const UPDATED_AT = 'UPDATED_DATE';

    protected $guarded = [];

    public function userTickets()
    {
        return $this->hasMany('App\Models\TournamentUserTicket','TOURNAMENT_ID','TOURNAMENT_ID')->where('USER_ID',getUserId());
    }
	
}
