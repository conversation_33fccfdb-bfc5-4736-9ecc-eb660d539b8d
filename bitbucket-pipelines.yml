image:
  name: 635018872373.dkr.ecr.ap-south-1.amazonaws.com/build-image-2-0:latest
  oidc: true
  aws:
     oidc-role: arn:aws:iam::635018872373:role/ecs-bb-oidc-access-role

definitions:
  steps:
    - step: &build
        name: Build, test and Deploy image to bitbucket
        caches:
          - docker
        script:
          - export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token
          - export AWS_ROLE_ARN=$OIDC_ROLE_ARN
          - export AWS_DEFAULT_REGION=$AWS_REGION
          - echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token
          - echo $BITBUCKET_CLONE_DIR
          - pwd
          - export SM_NAME=$SM_NAME
          - cp -rf * /usr/local/apache2/htdocs
          - cd /usr/local/apache2/htdocs
          - php composer.phar install
          - php artisan generate-env $SM_NAME
          - version=$(node -p "require('./composer.json').version")
          - docker build --build-arg ELASTIC_APM_SERVICE_NAME=${ELASTIC_APM_SERVICE_NAME} --build-arg ELASTIC_APM_SERVER_URL=${ELASTIC_APM_SERVER_URL} --build-arg DD_SERVICE=${DD_SERVICE} --build-arg DD_ENV=${DD_ENV} --build-arg DD_VERSION=${version} --build-arg DD_PROFILING_ENABLED=${DD_PROFILING_ENABLED} -t $IMAGE_NAME -f build-script/ApacheDockerFile .
          - pipe: atlassian/aws-ecr-push-image:1.6.2
            variables:
              AWS_OIDC_ROLE_ARN: $OIDC_ROLE_ARN
              AWS_DEFAULT_REGION: '${AWS_REGION}'
              IMAGE_NAME: "${IMAGE_NAME}"
              TAGS: "${version} latest "
          - aws ecs update-service --cluster $CLUSTER_NAME  --service $SERVICE_NAME --force-new-deployment --region ap-south-1
          - echo "Will update the Task Definition here if any , todo provide a CICD implementation "
        services:
          - docker


pipelines:
  branches:
    # runs test on master commits
    master:
      - step:
          oidc: true
          name: Merge with release/v1
          script:
            - apk add git
            - echo "run to merge release/v1"
            - git fetch
            - git pull --all
            - git branch -a
            - git checkout -b release/v1
            - git branch -a
            - git merge master
            - git push origin release/v1
    sprint/*:
      - step:
          name: Run Tests
          script:
            - echo "todo run tests"
    # Run deployment on releases
    release/*:
      - step:
          oidc: true
          deployment: production
          <<: *build         
  custom:
    merge-staging-to-master:
      - step:
          oidc: true
          name: merge with master
          script:
            - apk add git
            - echo "run to merge master"
            - git config -l
            - git config --local --replace-all user.name "Akshay-Jain1"
            #Add a public name for the new user you created
            - git config --local --replace-all user.email "<EMAIL>"
            #Email of the user
            - git config -l
            - git fetch
            - git pull --all
            - git branch -a
            - git checkout -b master
            - git branch -a
            - git merge staging
            #- git push origin master
            - git push https://Akshay-Jain1:$<EMAIL>/moonshinedevs/$Repo_Name


  tags:
    # Tags with QA will be  pushed to staging
    QA:
      - stage:
         name: QA stage
         deployment: staging
         steps:
           - step:
               oidc: true
               name: Building images and pushing to QA ECR
               <<: *build