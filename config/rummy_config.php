<?php

return [
    'url' => env('APP_URL', 'http://localhost'),
    'api_version' => "2.0.0",
    'cdn_url' => env('CDN_URL'),
    'reg_setting' => [
        'registrationLimitToIP' => env('REGISTRATION_LIMIT_TO_IP', 1000),
        'registrationLimitToFingerprint' => env('REGISTRATION_LIMIT_TO_FINGERPRINT', 1000)
    ],
    'cashfree' => [
        'payoutUrl' => env('CASHFREE_PAYOUT_URL', ''),
        'payoutClientId' => env('CASHFREE_PAYOUT_CLIENT_ID', ''),
        'payoutClientSec' => env('CASHFREE_PAYOUT_CLIENT_SEC', '')
    ],
    'transactionVariables' => [
        'depositCurrency' => env('DEPOSIT_CURRENCY'),
        'tdsRateOnCommission' => env('TDS_RATE_ON_COMMISSION', 5),
        'tdsRateOnCash' => env('TDS_RATE_ON_CASH', 30),
        'feeCutRateOnDepositAmount' => env('FEE_RATE_ON_DEPOSIT_AMOUNT', 15),
        'cardNetbankingUpiPaymentProviderType' => 3,
        'depositOkSuccessPaymentStatus' => 103,
        'promoOkSuccessPaymentStatus' => 107,
        'transactionSuccessPaymentStatus' => 125,
        'transactionFailedPaymentStatus' => 204,
        'depositTransactionStatusPendingID' => 122,
        'promoTransactionTypeId' => 9,
        'depositBalanceTypeId' => 1,
        'promoBalanceTypeId' => 2,
        'winBalanceTypeId' => 3,
        'minimumDepositAmount' => env('MIN_DEPOSIT_AMOUNT', 25),
        'depositCoinTypeID' => 1,
        'promoPendingStatusId' => 258,
        'realCashPurchaseTransactionTypes' => array(
            'rummyPurchase' => 61,
            'rummyPurchased' => 62,
            'payByCash' => 83,
            'deposit' => 8,
            'neftCheque' => 111
        ),
        'dailyWithdrawLimit' => env('DAILY_WITHDRAW_LIMIT', 50000),
        'lifetimeDepositLimitNonKycUsers' => env('LIFETIME_DEPOSIT_LIMIT_NON_KYC_USERS'),
        'cbVoucherBonusTransactionTypeId' => 192,
        'cbVoucherRCCTransactionTypeId' => 193,
        'fppBonusCoinTypeID' => 3,
        'rewardPointsCoinTypeID' => 12,
        'gstRateOnGatewayFee' => env('GST_RATE_ON_GATEWAY_FEE', 28),
        'gstRateOnDeposit' => env('DEPOSIT_GST_RATE', 28),
        'gstRefundRateOnDeposit' => env('DEPOSIT_GST_REFUND_RATE', 100),
        'gatewayHSNCode' => env('GATEWAY_HSN_CODE', '98076000'),
        'gstIn' => env('GST_IN', '07AAHCB1545K1ZN'),
        'hsnSacCode' => env('HSN_SAC_CODE', '98076000'),
        'depositGstBonus' => array(
            'transactionStatusID' => 266,
            'transactionTypeID' => 248,
        ),
        //refund related status
        'depositTransactionStatusInitiatedID' => 280,
        'depositTransactionStatusRefundInitiatedID' => 281,
        'depositTransactionStatusRefundApprovedID' => 282,
        'depositTransactionStatusRefundFailedID' => 283,
        'waitingThreshold' => env('WAITING_THRESHOLD', 10),
    ],
    'msg91' => [
        'authKey' => env('MSG91_AUTH_KEY'),
        'senderId' => env('MSG91_SENDER_ID', 'PBAAZI'),
        'messageContent' => 'Your One-Time Password (OTP) is {{OTP}} for verifying your phone number on RummyBaazi.com',
        'sendOtpCurlUrl' => env('MSG91_SEND_OTP_CURL_URL', 'https://control.msg91.com/api/sendotp.php'),
        'verifyOtpCurlUrl' => env('MSG91_VERIFY_OTP_CURL_URL', 'https://control.msg91.com/api/verifyRequestOTP.php'),
        'templateIdv5' => env('MSG91_TEMPLATE_ID_V5'),
        'sendOTPCurlURLv5' => env('MSG91_SEND_OTP_CURL_URL_V5', 'https://api.msg91.com/api/v5/otp')
    ],
    "mail" => [
        'from' => [
            'support' => env('MAIL_FROM_SUPPORT', '<EMAIL>')
        ],
        "to" => [
            'support' => env('MAIL_FROM_SUPPORT', '<EMAIL>')
        ],
        "validDomainList" => explode('|', env('VALID_MAIL_DOMAINS', 'pokerbaazi.com|moonshinetechnology.com|rummybaazi.com|cardbaazi.com|baazigames.com'))
    ],
    "maxOtpSentLimit" => 6,
    "maxOtpVerifyLimit" => env('MAX_OTP_VERIFY_LIMIT', 5),
    'pagination' => [
        'limit' => env('PAGINATE_LIMIT', 50),
        'offset' => env('PAGINATE_OFFSET', 0)
    ],
    'responsible_gaming' => [
        'coolingPeriod' => env('COOLING_PERIOD', 60)  //In Seconds
    ],
    'juspay' => [
        'juspay_response_key' => env('JUSPAY_RESPONSE_KEY')
    ],
    'branch' => [
        'apiKey' => env("BRANCH_API_KEY"),
        'standardEventUrl' => env("BRANCH_STANDARD_EVENT_URL", 'https://api2.branch.io/v2/event/standard'),
        'customEventUrl' => env("BRANCH_CUSTOM_EVENT_URL", 'https://api2.branch.io/v2/event/custom')
    ],
    'ga_setting' => [
        'gaTrackingId' => env('GA_TRACKING_ID'),
        'gaCategorySignups' => 'User Signups',
        'gaCategoryDeposits' => 'User Deposits',
        'gaActionRegistrationSuccess' => 'Signup Success',
        'gaActionAccountVerificationSuccess' => 'Account Verification Success',
        'gaActionMobileVerificationSuccess' => 'Mobile Verification Success',
        'gaActionEmailVerificationSuccess' => 'Email Verification Success',
        'gaMeasurementProtocolUrl' => env('GA_MESUREMENT_PROTOCOL'),
    ],
    'maskingChar' => 'X', // Character to be used for masking payment details
    'payu_seamless_code' => env('PAYU_SEAMLESS_CODE', 'payu_seamless'), // DB code for PayU Seamless
    'cashfree_code' => env('CASHFREE_CODE', 'cashfree'), // DB code for cashfree
    'paytm_code' => env('PAYTM_CODE', 'paytm'), // DB code for payTM
    'daily_bonus_amount' => env('DAILY_BONUS_AMOUNT', 50000),
    'juspay_code' => env('JUSPAY_CODE', 'juspay'), // DB Code for JusPay
    'paymentGateway_product_desc' => env('PAYMENTGATEWAY_PRODUCT_DESC', 'RummyBaazi'),
    'website_base_url' => env('WEBSITE_BASE_URL'),
    'payment_response_route' => env('PAYMENT_RESPONSE_ROUTE', 'home/add-money/payment'),
    'firebaseCredentials' => env('FIREBASE_CREDENTIALS', json_encode([])),
    'minimum_daily_deposit_limit' => env('MINIMUM_DAILY_DEPOSIT_LIMIT'),
    'kickbox_setting' => [
        'kickBoxApiKey' => env('KICKBOX_API_KEY'),
        'kickBoxApiUrl' => 'https://api.kickbox.io/v2/verify',
        'validDomainList' => array('myuselessdomain'),
        'invalidDomainList' => array('mail.ru', 'yandex.com'),
        'kickboxResultList' => array('undeliverable', 'risky', 'unknown'),
        'kickboxReasonList' => array('invalid_email', 'invalid_domain', 'rejected_email', 'no_connect', 'invalid_smtp', 'timeout'),
        'kickboxDisposableList' => array('true'),
    ],
    'winners_list_criteria' => [
        'listTourAboveAmount' => env('LIST_TOUR_ABOVE_AMOUNT', 500000)
    ],
    'deposit_limit_propotion' => [
        'daily' => env('DEP_LIMIT_DAILY_PROPOTION', 1),
        'weekly' => env('DEP_LIMIT_WEEKLY_PROPOTION', 4),
        'monthly' => env('DEP_LIMIT_MONTHLY_PROPOTION', 3)
    ],
    'caching' => [
        'time' => env('STANDARD_QUERY_CACHING_TIME_IN_MINS', 10) * 60,
    ],
    'newTenKExemption' => 10000,
    'JUSPAY_WEBHOOK_IP' => env('JUSPAY_WEBHOOK_IP'),
    'PAYU_MONEY_WEBHOOK_IP' => env('PAYU_MONEY_WEBHOOK_IP'),
    'instantPayoutFlagConsider' => [
        'green' => env('CONSIDER_GREEN_FLAG_USER', true),
        'yellow' => env('CONSIDER_YELLOW_FLAG_USER', true)
    ],
    'convertIntoRedFlag' => env('CONVERT_INTO_RED_FLAG', false),
    'instantWithdrawApplicableForCommission' => env('INSTANT_WITHDRAW_APPICABLE_FOR_COMMISSION', false),
    'instantWithdrawMaxCapping' => [
        'greenPerTrans' => env('INSTANT_WITHDRAWAL_GREEN_MAX_LIMIT', 100000),
        'greenPerDay' => env('INSTANT_WITHDRAWAL_GREEN_MAX_PER_DAY_CAPPING', 500000),
        'yellowPerTrans' => env('INSTANT_WITHDRAWAL_YELLOW_MAX_LIMIT', 30000),
        'yellowPerDay' => env('INSTANT_WITHDRAWAL_YELLOW_MAX_PER_DAY_CAPPING', 30000)
    ],
    'cashfree_instant_withdraw_limit' => env('CASHFREE_INSTANT_WITHDRAW_LIMIT', 100000),
    'rafVariables' => [
        'firstTimeDepositKey' => env('FIRST_TIME_DEPOSIT_KEY', 'first_time_deposit'),
        'recurrenceDepositKey' => env('RECURRENCE_DEPOSIT_KEY', 'recurrence_deposit'),
        'transactionTypeId' => 69,
        'RAFBonusTransactionStatusSuccessId' => 212
    ],
    'adminId' => env('ADMIN_ID', 10001),
    'accountSectionBaseURL' => env('ACCOUNTSECTION_BASE_URL', 'https://dashboard.alphabetabox.com/'),
    'accountSectionResponseRoute' => env('ACCOUNTSECTION_RESPONSE_ROUTE', 'home/add-money/payment'),
    'kinesis' => [
        'region' => env('AWS_KINESIS_DEFAULT_REGION'),
        'stream_name' => env('AWS_KINESIS_STREAM_NAME')
    ],
    'bannedState' => [
        'proIpUrl' => env('PRO_IP_URL'),
        'ipstackUrl' => env('IPSTACK_URL'),
        'activeIpVendor' => env('ACTIVE_IP_VENDOR', 'proip'),
        'validateBannedState' => env('VALIDATE_BANNED_STATE', '1'),
        'bannedStateBypassJson' => env('BANNED_STATE_BYPASS_JSON'),
        'bannedStates' => env('BANNED_STATES', 'bihar'),
        "bannedStateSettingJson" => env('BANNED_STATE_SETTING_JSON'),
        "bannedStateLambdaUrl" => env('BANNED_STATE_LAMBDA_URL')
    ],
    'noOfWithdrawDayTransactionLimit' => [
        'winning' => env('NO_OF_WITHDRAW_DAY_TRANSACTION_LIMIT_WINNING', 3),
        'loosing' => env('NO_OF_WITHDRAW_DAY_TRANSACTION_LIMIT_LOOSING', 10)
    ],
    'invoid' => [
        'authKey' => env('INVOID_AUTH_KEY', '********-975e-4a99-917a-b1ebb070f47a'),
        'init-verify' => env('INVOID_INIT_VERIFY_URL'),
        'init-step-add' => env('INVOID_INIT_ADDRESS_PAN_URL', 'https://ic.invoid.co/v2'),
        'init-step-bank' => env('INVOID_INIT_BANK_URL', 'https://gc.invoid.co/v3'),
        'init-step-digilocker' => env('INVOID_INIT_DIGILOCKER_URL', 'https://dl.invoid.co/init'),
        'invoid_cipher_key' => env('INVOID_CIPHER_KEY'),
        'invoid_cipher_digilocker_key' => env('INVOID_CIPHER_DIGILOCKER_KEY'),
        'openssl_cipher_name' => env('OPENSSL_CIPHER_NAME'),
        'cipher_key_len' => env('CIPHER_KEY_LEN'),
        'invoid_fail_url' => env('INVOID_FAIL_REDIRECT_URL'),
        'invoid_pending_url' => env('INVOID_PENDING_URL'),
        'invoid_fail_url_digilocker' => env('INVOID_FAIL_REDIRECT_URL_DIGILOCKER'),
        'invoid_pending_url_digilocker' => env('INVOID_PENDING_URL_DIGILOCKER')
    ],
    'maxKycAttempt' => env('MAX_KYC_FAIL_ATTEMPT'),
    'fbPixelEvents' => [
        'url' => env('FB_PIXEL_EVENT_URL'),
        'accessToken' => env('FB_PIXEL_ACCESS_TOKEN'),
        'eventSourceUrl' => env('FB_EVENT_SOURCE_URL'),
        'serverIp' => env('FB_SERVER_IP'),
        'fbPixelId' => env('FB_PIXEL_ID'),
        'fbCountryCode' => env('FB_PIXEL_COUNTRY_CODE'),
        'fbDepositEventName' => env('FB_DEPOSIT_EVENT_NAME', 'Purchase'),
        'fbFTDEventName' => env('FB_FTD_EVENT_NAME', 'FTD')
    ],
    'google_analytics' => [
        'ga4_url' => env('GA4_URL', 'https://www.google-analytics.com/mp/collect?firebase_app_id=<FIREBASE_APP_ID>&api_secret=<API_SECRET>'),
        'android' => [
            'firebase_app_id' => env('ANDROID_FIREBASE_APP_ID', ''),
            'app_secret' => env('ANDROID_APP_SECRET', '')
        ],
        'ios' => [
            'firebase_app_id' => env('IOS_FIREBASE_APP_ID', ''),
            'app_secret' => env('IOS_APP_SECRET', '')
        ],
        'web' => [
            'gaTrackingId' => env('GA_WEB_TRACKING_ID', ''),
            'gaTrackingWeb_SerectKey' => env('GA_WEB_TRACKING_SECRET', '')
        ]
    ],
    'ledger' => [
        'tds_financial_year_start' => env('TDS_FINANCIAL_YEAR_START', '2022-04-01 00:00:00')
    ],
    'TRANSACTION_STATUS_CODE' => [
        'DEPOSIT_PENDING' => 122,
        'DEPOSIT_SUCCESS' => 125,
        'DEPOSIT_FAILED' => 204
    ],
    'DOC_PROOF' => [
        'DEFAULT' => [
            'LIFE_TIME_WITHDRAW' => env('LIFE_TIME_WITHDRAW_DEFAULT', 10000),
            'LIFE_TIME_DEPOSIT' => env('LIFE_TIME_DEPOSIT_DEFAULT', 30000),
            'DOC_COUNT' => env('DOC_COUNT_DEFAULT', 2)
        ],
        'CARDBAAZI_ONLINE' => [
            'LIFE_TIME_WITHDRAW' => env('LIFE_TIME_WITHDRAW_CARDBAAZI_ONLINE', 10000),
            'LIFE_TIME_DEPOSIT' => env('LIFE_TIME_DEPOSIT_CARDBAAZI_ONLINE', 30000),
            'DOC_COUNT' => env('DOC_COUNT_CARDBAAZI_ONLINE', 2)
        ],
        'RUMMYBAAZI_ONLINE' => [
            'LIFE_TIME_WITHDRAW' => env('LIFE_TIME_WITHDRAW_RUMMYBAAZI_ONLINE', 10000),
            'LIFE_TIME_DEPOSIT' => env('LIFE_TIME_DEPOSIT_RUMMYBAAZI_ONLINE', 30000),
            'DOC_COUNT' => env('DOC_COUNT_RUMMYBAAZI_ONLINE', 2)
        ]
    ],
    'REQUIRED_DOCUMENT_TYPES' => [1 => "Address Proof", 2 => "PAN"],
    'nameMatchPercentage' => env('NAME_MATCH_PERCENTAGE', 80),
    'DEPOSIT_WITHDRAWAL' => env('DEPOSIT_WITHDRAWAL', false),
    'MINIMUM_WITHDRAW_AMOUNT' => env('MIN_WITHDRAW_AMOUNT', 25),
    'CT_WEBHOOK_IP' => env('CT_WEBHOOK_IP'),
    'CT_WEBHOOK_AUTH_TOKEN' => env('CT_WEBHOOK_AUTH_TOKEN', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJDVCIsIm5hbWUiOiJDQi1DVCIsImlhdCI6MTY3NjIzOTAyMn0.Ba384khPllPER_4DojJtSPnpnDh0LqQOMpnmvPf4Edg'),
    'DISABLED_IFSC' => env('DISABLED_IFSC','PYTM0123456'),
    'FPP_BONUS_ADJUSTMENT' => [
        'BONUSE_TYPE' => 'Special',
        'BONUS_RELEASE_PER' => env('BONUS_RELEASE_PER', 15),
        'EXPIRY_DAYS' => env('EXPIRY_DAYS', 30)
    ],
    'CREDIT_TXN_TYPE_ID' => explode('|', env('CREDIT_TXN_TYPE_ID', '175|186|188|190|195|199|202|205|211|225|229|236|239|250|257|264|269|281')),
    'restricted_deposit_without_kyc_state_codes' => explode("|", env('RESTRICTED_DEPOSIT_WITHOUT_KYC_STATES'))
];
